/**
 * Boss SEO Dashboard
 *
 * Utilise les composants WordPress natifs et Tailwind CSS
 */

import { render } from '@wordpress/element';

// Styles
import './styles/tailwind.css';

// Composant principal
import App from './App';

// Rendu de l'application dans l'élément DOM approprié
const dashboardApp = document.getElementById('boss-seo-dashboard-app');
const optimizerApp = document.getElementById('boss-seo-optimizer-app');
const technicalApp = document.getElementById('boss-seo-technical-app');
const technicalV2App = document.getElementById('boss-seo-technical-analysis-v2-app');
const contentApp = document.getElementById('boss-seo-content-app');
const schemaApp = document.getElementById('boss-seo-schema-app');
const analyticsApp = document.getElementById('boss-seo-analytics-app');
const localApp = document.getElementById('boss-seo-local-app');
const technicalManagementApp = document.getElementById('boss-seo-technical-management-app');
const reportsApp = document.getElementById('boss-seo-reports-app');
const settingsApp = document.getElementById('boss-seo-settings-app');
const helpApp = document.getElementById('boss-seo-help-app');

// Initialiser l'application dans le conteneur approprié
if (dashboardApp) {
    render(<App initialPage="dashboard" />, dashboardApp);
} else if (optimizerApp) {
    render(<App initialPage="optimizer" />, optimizerApp);
} else if (technicalApp) {
    render(<App initialPage="technical" />, technicalApp);
} else if (technicalV2App) {
    render(<App initialPage="technical" />, technicalV2App);
} else if (contentApp) {
    render(<App initialPage="content_multistep" />, contentApp);
} else if (schemaApp) {
    render(<App initialPage="schema" />, schemaApp);
} else if (analyticsApp) {
    render(<App initialPage="analytics" />, analyticsApp);
} else if (localApp) {
    render(<App initialPage="local" />, localApp);
} else if (technicalManagementApp) {
    render(<App initialPage="technical_management" />, technicalManagementApp);
} else if (reportsApp) {
    render(<App initialPage="reports" />, reportsApp);
} else if (settingsApp) {
    render(<App initialPage="settings" />, settingsApp);
} else if (helpApp) {
    render(<App initialPage="help" />, helpApp);
}
