import { useState } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  Card,
  CardBody,
  Button,
  Dashicon,
  Spinner,
  __experimentalSpacer as Spacer
} from '@wordpress/components';

// Composants
import IAExplanation from './IAExplanation';
import CorrectionSuggestion from './CorrectionSuggestion';

const ErrorCard = ({
  error,
  severity,
  isExpanded,
  onToggleExpansion,
  showIAExplanation,
  onToggleIAExplanation,
  aiEnabled
}) => {
  const [isLoadingExplanation, setIsLoadingExplanation] = useState(false);
  const [isLoadingSuggestion, setIsLoadingSuggestion] = useState(false);
  const [explanation, setExplanation] = useState(null);
  const [suggestion, setSuggestion] = useState(null);

  // Obtenir la couleur selon la sévérité
  const getSeverityColor = () => {
    switch (severity) {
      case 'critical':
        return 'boss-error';
      case 'medium':
        return 'boss-warning';
      case 'low':
        return 'boss-gray';
      default:
        return 'boss-gray';
    }
  };

  // Obtenir l'icône selon la sévérité
  const getSeverityIcon = () => {
    switch (severity) {
      case 'critical':
        return 'warning';
      case 'medium':
        return 'info';
      case 'low':
        return 'lightbulb';
      default:
        return 'info';
    }
  };

  // Obtenir le label de sévérité
  const getSeverityLabel = () => {
    switch (severity) {
      case 'critical':
        return __('Critique', 'boss-seo');
      case 'medium':
        return __('Moyen', 'boss-seo');
      case 'low':
        return __('Faible', 'boss-seo');
      default:
        return __('Inconnu', 'boss-seo');
    }
  };

  // Obtenir la catégorie d'erreur
  const getCategoryLabel = (category) => {
    const categories = {
      'meta': __('Balises Meta', 'boss-seo'),
      'content': __('Contenu', 'boss-seo'),
      'technical': __('Technique', 'boss-seo'),
      'performance': __('Performance', 'boss-seo'),
      'accessibility': __('Accessibilité', 'boss-seo'),
      'images': __('Images', 'boss-seo'),
      'links': __('Liens', 'boss-seo'),
      'structure': __('Structure', 'boss-seo')
    };
    return categories[category] || category;
  };

  // Charger l'explication IA
  const loadIAExplanation = async () => {
    if (explanation) {
      onToggleIAExplanation();
      return;
    }

    try {
      setIsLoadingExplanation(true);
      
      // Simuler un appel API pour l'explication IA
      // Dans la vraie implémentation, cela appellerait le service IA
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      const mockExplanation = {
        description: __('Cette erreur affecte la façon dont les moteurs de recherche comprennent et indexent votre contenu.', 'boss-seo'),
        impact: severity === 'critical' ? __('Impact élevé sur le SEO', 'boss-seo') : 
                severity === 'medium' ? __('Impact modéré sur le SEO', 'boss-seo') : 
                __('Impact faible sur le SEO', 'boss-seo'),
        recommendation: __('Il est recommandé de corriger cette erreur pour améliorer votre référencement.', 'boss-seo')
      };
      
      setExplanation(mockExplanation);
      onToggleIAExplanation();
      
    } catch (err) {
      console.error('Erreur lors du chargement de l\'explication IA:', err);
    } finally {
      setIsLoadingExplanation(false);
    }
  };

  // Charger la suggestion de correction
  const loadCorrectionSuggestion = async () => {
    if (suggestion) {
      return;
    }

    try {
      setIsLoadingSuggestion(true);
      
      // Simuler un appel API pour la suggestion de correction
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const mockSuggestion = {
        type: 'code',
        title: __('Suggestion de correction', 'boss-seo'),
        description: __('Voici comment corriger cette erreur :', 'boss-seo'),
        code: `<meta name="description" content="Votre nouvelle méta description optimisée pour le SEO">`,
        canApply: error.category === 'meta',
        steps: [
          __('Accédez à l\'éditeur de votre page', 'boss-seo'),
          __('Modifiez la méta description', 'boss-seo'),
          __('Sauvegardez les modifications', 'boss-seo')
        ]
      };
      
      setSuggestion(mockSuggestion);
      
    } catch (err) {
      console.error('Erreur lors du chargement de la suggestion:', err);
    } finally {
      setIsLoadingSuggestion(false);
    }
  };

  return (
    <Card className={`boss-border-l-4 boss-border-${getSeverityColor()}`}>
      <CardBody>
        {/* En-tête de l'erreur */}
        <div className="boss-flex boss-items-start boss-justify-between boss-mb-3">
          <div className="boss-flex boss-items-start boss-flex-1">
            <div className={`boss-bg-${getSeverityColor()}/10 boss-p-2 boss-rounded-lg boss-mr-3 boss-flex-shrink-0`}>
              <Dashicon 
                icon={getSeverityIcon()} 
                className={`boss-text-${getSeverityColor()} boss-text-lg`} 
              />
            </div>
            
            <div className="boss-flex-1">
              <div className="boss-flex boss-items-center boss-mb-2">
                <h3 className="boss-text-lg boss-font-semibold boss-text-boss-dark boss-mr-3">
                  {error.title}
                </h3>
                <span className={`boss-px-2 boss-py-1 boss-rounded boss-text-xs boss-font-medium boss-bg-${getSeverityColor()}/10 boss-text-${getSeverityColor()}`}>
                  {getSeverityLabel()}
                </span>
                <span className="boss-px-2 boss-py-1 boss-rounded boss-text-xs boss-bg-gray-100 boss-text-boss-gray boss-ml-2">
                  {getCategoryLabel(error.category)}
                </span>
              </div>
              
              <p className="boss-text-boss-gray boss-text-sm boss-mb-3">
                {error.description}
              </p>
              
              {/* Éléments affectés */}
              {error.elements && error.elements.length > 0 && (
                <div className="boss-mb-3">
                  <span className="boss-text-sm boss-font-medium boss-text-boss-dark">
                    {__('Éléments affectés:', 'boss-seo')} 
                  </span>
                  <span className="boss-text-sm boss-text-boss-gray boss-ml-1">
                    {error.elements.length} {error.elements.length > 1 ? __('éléments', 'boss-seo') : __('élément', 'boss-seo')}
                  </span>
                </div>
              )}
            </div>
          </div>
          
          {/* Bouton d'expansion */}
          <Button
            isSecondary
            size="small"
            onClick={onToggleExpansion}
            className="boss-flex-shrink-0"
          >
            <Dashicon icon={isExpanded ? 'arrow-up-alt2' : 'arrow-down-alt2'} />
            {isExpanded ? __('Réduire', 'boss-seo') : __('Détails', 'boss-seo')}
          </Button>
        </div>

        {/* Actions rapides */}
        <div className="boss-flex boss-items-center boss-space-x-3 boss-mb-3">
          {/* Bouton explication IA */}
          {aiEnabled && (
            <Button
              isSecondary
              size="small"
              onClick={loadIAExplanation}
              disabled={isLoadingExplanation}
              className="boss-flex boss-items-center"
            >
              {isLoadingExplanation ? (
                <Spinner />
              ) : (
                <Dashicon icon="admin-comments" className="boss-mr-1" />
              )}
              {__('🧠 Comprendre avec l\'IA', 'boss-seo')}
            </Button>
          )}
          
          {/* Bouton suggestion de correction */}
          {aiEnabled && (
            <Button
              isPrimary
              size="small"
              onClick={loadCorrectionSuggestion}
              disabled={isLoadingSuggestion}
              className="boss-flex boss-items-center"
            >
              {isLoadingSuggestion ? (
                <Spinner />
              ) : (
                <Dashicon icon="admin-tools" className="boss-mr-1" />
              )}
              {__('Suggestion de correction', 'boss-seo')}
            </Button>
          )}
        </div>

        {/* Contenu étendu */}
        {isExpanded && (
          <div className="boss-border-t boss-border-gray-200 boss-pt-4 boss-mt-4">
            {/* Détails de l'erreur */}
            <div className="boss-mb-4">
              <h4 className="boss-text-md boss-font-semibold boss-mb-2">
                {__('Détails techniques', 'boss-seo')}
              </h4>
              
              {/* Localisation */}
              {error.location && (
                <div className="boss-mb-2">
                  <span className="boss-text-sm boss-font-medium boss-text-boss-dark">
                    {__('Localisation:', 'boss-seo')} 
                  </span>
                  <code className="boss-bg-gray-100 boss-px-2 boss-py-1 boss-rounded boss-text-sm boss-ml-2">
                    {error.location}
                  </code>
                </div>
              )}
              
              {/* Éléments affectés détaillés */}
              {error.elements && error.elements.length > 0 && (
                <div className="boss-mb-2">
                  <span className="boss-text-sm boss-font-medium boss-text-boss-dark boss-block boss-mb-1">
                    {__('Éléments concernés:', 'boss-seo')}
                  </span>
                  <div className="boss-max-h-32 boss-overflow-y-auto boss-bg-gray-50 boss-p-2 boss-rounded">
                    {error.elements.slice(0, 5).map((element, index) => (
                      <div key={index} className="boss-text-sm boss-text-boss-gray boss-mb-1">
                        <code className="boss-bg-white boss-px-1 boss-rounded">
                          {element}
                        </code>
                      </div>
                    ))}
                    {error.elements.length > 5 && (
                      <div className="boss-text-sm boss-text-boss-gray boss-italic">
                        {__('... et', 'boss-seo')} {error.elements.length - 5} {__('autres', 'boss-seo')}
                      </div>
                    )}
                  </div>
                </div>
              )}
              
              {/* Valeur actuelle vs recommandée */}
              {error.currentValue && (
                <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 boss-gap-4 boss-mt-3">
                  <div>
                    <span className="boss-text-sm boss-font-medium boss-text-boss-error boss-block boss-mb-1">
                      {__('Valeur actuelle:', 'boss-seo')}
                    </span>
                    <div className="boss-bg-red-50 boss-border boss-border-red-200 boss-p-2 boss-rounded boss-text-sm">
                      {error.currentValue}
                    </div>
                  </div>
                  
                  {error.recommendedValue && (
                    <div>
                      <span className="boss-text-sm boss-font-medium boss-text-boss-success boss-block boss-mb-1">
                        {__('Valeur recommandée:', 'boss-seo')}
                      </span>
                      <div className="boss-bg-green-50 boss-border boss-border-green-200 boss-p-2 boss-rounded boss-text-sm">
                        {error.recommendedValue}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Explication IA */}
        {showIAExplanation && explanation && (
          <IAExplanation
            explanation={explanation}
            error={error}
          />
        )}

        {/* Suggestion de correction */}
        {suggestion && (
          <CorrectionSuggestion
            suggestion={suggestion}
            error={error}
          />
        )}
      </CardBody>
    </Card>
  );
};

export default ErrorCard;
