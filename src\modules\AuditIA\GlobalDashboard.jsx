import { useState, useEffect } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  Card,
  CardBody,
  CardHeader,
  Dashicon,
  Spinner,
  ProgressBar
} from '@wordpress/components';

const GlobalDashboard = ({ auditService, onRefresh }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [stats, setStats] = useState({
    totalContent: 0,
    optimizedContent: 0,
    unoptimizedContent: 0,
    averageScore: 0,
    contentTypes: {},
    commonErrors: [],
    criticalIssues: 0,
    lastScanDate: null
  });

  useEffect(() => {
    loadGlobalStats();
  }, []);

  const loadGlobalStats = async () => {
    try {
      setIsLoading(true);
      const globalStats = await auditService.getGlobalStats();
      setStats(globalStats);
    } catch (error) {
      console.error('Erreur lors du chargement des statistiques:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getScoreColor = (score) => {
    if (score >= 80) return 'boss-text-green-600';
    if (score >= 60) return 'boss-text-yellow-600';
    return 'boss-text-red-600';
  };

  const getScoreBarColor = (score) => {
    if (score >= 80) return 'boss-bg-green-500';
    if (score >= 60) return 'boss-bg-yellow-500';
    return 'boss-bg-red-500';
  };

  const formatContentType = (type) => {
    const types = {
      'post': __('Articles', 'boss-seo'),
      'page': __('Pages', 'boss-seo'),
      'product': __('Produits', 'boss-seo')
    };
    return types[type] || type;
  };

  const getSeverityIcon = (severity) => {
    switch (severity) {
      case 'critical': return 'warning';
      case 'medium': return 'info';
      case 'low': return 'lightbulb';
      default: return 'admin-generic';
    }
  };

  const getSeverityColor = (severity) => {
    switch (severity) {
      case 'critical': return 'boss-text-red-600';
      case 'medium': return 'boss-text-yellow-600';
      case 'low': return 'boss-text-blue-600';
      default: return 'boss-text-gray-600';
    }
  };

  if (isLoading) {
    return (
      <div className="boss-flex boss-justify-center boss-items-center boss-py-12">
        <Spinner />
        <span className="boss-ml-3">{__('Chargement des statistiques...', 'boss-seo')}</span>
      </div>
    );
  }

  return (
    <div className="boss-space-y-6">
      {/* En-tête avec statistiques principales */}
      <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 lg:boss-grid-cols-4 boss-gap-6">
        {/* Total du contenu */}
        <Card>
          <CardBody>
            <div className="boss-flex boss-items-center">
              <div className="boss-bg-blue-100 boss-p-3 boss-rounded-lg boss-mr-4">
                <Dashicon icon="admin-post" className="boss-text-blue-600 boss-text-2xl" />
              </div>
              <div>
                <div className="boss-text-2xl boss-font-bold boss-text-boss-dark">
                  {stats.totalContent}
                </div>
                <div className="boss-text-sm boss-text-boss-gray">
                  {__('Total du contenu', 'boss-seo')}
                </div>
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Contenu optimisé */}
        <Card>
          <CardBody>
            <div className="boss-flex boss-items-center">
              <div className="boss-bg-green-100 boss-p-3 boss-rounded-lg boss-mr-4">
                <Dashicon icon="yes-alt" className="boss-text-green-600 boss-text-2xl" />
              </div>
              <div>
                <div className="boss-text-2xl boss-font-bold boss-text-boss-dark">
                  {stats.optimizedContent}
                </div>
                <div className="boss-text-sm boss-text-boss-gray">
                  {__('Optimisé', 'boss-seo')}
                </div>
                <div className="boss-text-xs boss-text-green-600">
                  {stats.totalContent > 0 ? Math.round((stats.optimizedContent / stats.totalContent) * 100) : 0}%
                </div>
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Contenu non optimisé */}
        <Card>
          <CardBody>
            <div className="boss-flex boss-items-center">
              <div className="boss-bg-red-100 boss-p-3 boss-rounded-lg boss-mr-4">
                <Dashicon icon="dismiss" className="boss-text-red-600 boss-text-2xl" />
              </div>
              <div>
                <div className="boss-text-2xl boss-font-bold boss-text-boss-dark">
                  {stats.unoptimizedContent}
                </div>
                <div className="boss-text-sm boss-text-boss-gray">
                  {__('Non optimisé', 'boss-seo')}
                </div>
                <div className="boss-text-xs boss-text-red-600">
                  {stats.totalContent > 0 ? Math.round((stats.unoptimizedContent / stats.totalContent) * 100) : 0}%
                </div>
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Score moyen */}
        <Card>
          <CardBody>
            <div className="boss-flex boss-items-center">
              <div className="boss-bg-purple-100 boss-p-3 boss-rounded-lg boss-mr-4">
                <Dashicon icon="chart-bar" className="boss-text-purple-600 boss-text-2xl" />
              </div>
              <div className="boss-flex-1">
                <div className={`boss-text-2xl boss-font-bold ${getScoreColor(stats.averageScore)}`}>
                  {stats.averageScore}%
                </div>
                <div className="boss-text-sm boss-text-boss-gray boss-mb-2">
                  {__('Score SEO moyen', 'boss-seo')}
                </div>
                <div className="boss-w-full boss-bg-gray-200 boss-rounded-full boss-h-2">
                  <div 
                    className={`boss-h-2 boss-rounded-full ${getScoreBarColor(stats.averageScore)}`}
                    style={{ width: `${stats.averageScore}%` }}
                  ></div>
                </div>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>

      {/* Répartition par type de contenu */}
      <Card>
        <CardHeader>
          <h3 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
            <Dashicon icon="category" className="boss-mr-2" />
            {__('Répartition par type de contenu', 'boss-seo')}
          </h3>
        </CardHeader>
        <CardBody>
          <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 lg:boss-grid-cols-3 boss-gap-4">
            {Object.entries(stats.contentTypes).map(([type, typeStats]) => (
              <div key={type} className="boss-bg-gray-50 boss-p-4 boss-rounded-lg">
                <div className="boss-flex boss-justify-between boss-items-center boss-mb-2">
                  <h4 className="boss-font-medium boss-text-boss-dark">
                    {formatContentType(type)}
                  </h4>
                  <span className="boss-text-sm boss-text-boss-gray">
                    {typeStats.total} {__('éléments', 'boss-seo')}
                  </span>
                </div>
                
                <div className="boss-flex boss-justify-between boss-text-sm boss-mb-2">
                  <span className="boss-text-green-600">
                    ✓ {typeStats.optimized} {__('optimisés', 'boss-seo')}
                  </span>
                  <span className="boss-text-red-600">
                    ✗ {typeStats.unoptimized} {__('non optimisés', 'boss-seo')}
                  </span>
                </div>
                
                <div className="boss-w-full boss-bg-gray-200 boss-rounded-full boss-h-2">
                  <div 
                    className="boss-h-2 boss-rounded-full boss-bg-green-500"
                    style={{ 
                      width: `${typeStats.total > 0 ? (typeStats.optimized / typeStats.total) * 100 : 0}%` 
                    }}
                  ></div>
                </div>
              </div>
            ))}
          </div>
        </CardBody>
      </Card>

      {/* Erreurs fréquentes */}
      <Card>
        <CardHeader>
          <h3 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
            <Dashicon icon="warning" className="boss-mr-2" />
            {__('Erreurs les plus fréquentes', 'boss-seo')}
          </h3>
        </CardHeader>
        <CardBody>
          {stats.commonErrors.length > 0 ? (
            <div className="boss-space-y-3">
              {stats.commonErrors.slice(0, 8).map((error, index) => (
                <div key={index} className="boss-flex boss-items-center boss-justify-between boss-p-3 boss-bg-gray-50 boss-rounded-lg">
                  <div className="boss-flex boss-items-center">
                    <Dashicon 
                      icon={getSeverityIcon(error.severity)} 
                      className={`boss-mr-3 ${getSeverityColor(error.severity)}`} 
                    />
                    <div>
                      <div className="boss-font-medium boss-text-boss-dark">
                        {error.title}
                      </div>
                      <div className="boss-text-sm boss-text-boss-gray">
                        {error.type}
                      </div>
                    </div>
                  </div>
                  <div className="boss-text-right">
                    <div className="boss-font-bold boss-text-boss-dark">
                      {error.count}
                    </div>
                    <div className="boss-text-xs boss-text-boss-gray">
                      {__('occurrences', 'boss-seo')}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="boss-text-center boss-py-8 boss-text-boss-gray">
              <Dashicon icon="smiley" className="boss-text-4xl boss-mb-2" />
              <p>{__('Aucune erreur fréquente détectée !', 'boss-seo')}</p>
            </div>
          )}
        </CardBody>
      </Card>

      {/* Problèmes critiques */}
      {stats.criticalIssues > 0 && (
        <Card className="boss-border-l-4 boss-border-red-500">
          <CardBody>
            <div className="boss-flex boss-items-center">
              <Dashicon icon="warning" className="boss-text-red-600 boss-text-2xl boss-mr-3" />
              <div>
                <h3 className="boss-text-lg boss-font-semibold boss-text-red-600">
                  {__('Attention : Problèmes critiques détectés', 'boss-seo')}
                </h3>
                <p className="boss-text-boss-gray">
                  {stats.criticalIssues} {__('problèmes critiques nécessitent une attention immédiate.', 'boss-seo')}
                </p>
              </div>
            </div>
          </CardBody>
        </Card>
      )}

      {/* Dernière analyse */}
      {stats.lastScanDate && (
        <div className="boss-text-center boss-text-sm boss-text-boss-gray">
          {__('Dernière analyse globale :', 'boss-seo')} {new Date(stats.lastScanDate).toLocaleString()}
        </div>
      )}
    </div>
  );
};

export default GlobalDashboard;
