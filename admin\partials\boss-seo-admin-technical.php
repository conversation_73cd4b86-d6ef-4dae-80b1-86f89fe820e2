<?php
/**
 * Provide a admin area view for the plugin - Technical Analysis v2.0
 *
 * This file is used to markup the admin-facing aspects of the plugin.
 * Version 2.0 with real Google PageSpeed API integration and AI suggestions.
 * ANCIENNE INTERFACE SUPPRIMÉE - SEULEMENT LA NOUVELLE v2.0
 *
 * @link       https://bossseo.com
 * @since      1.1.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/admin/partials
 */

// SYSTÈME D'INTÉGRATION DÉSACTIVÉ : Causait une duplication d'interface
// Le système Boss_Technical_Analysis_Integration créait une interface conflictuelle
// qui essayait de charger des fichiers JavaScript inexistants (technical-analysis-v2.js).
// L'interface fonctionnelle est maintenant directement intégrée ci-dessous.

?>

<div class="wrap">
    <h1><?php echo esc_html( get_admin_page_title() ); ?></h1>

    <!-- Nouveau module v2.0 avec API réelle - INTERFACE UNIQUE -->
    <div class="notice notice-success">
        <p>
            <strong><?php _e( 'Nouveau !', 'boss-seo' ); ?></strong>
            <?php _e( 'Module d\'analyse technique v2.0 avec intégration réelle Google PageSpeed API et suggestions IA.', 'boss-seo' ); ?>
        </p>
    </div>

    <?php
    // Vérifier la configuration API
    $external_services = get_option( 'boss_optimizer_external_services', array() );
    $api_key = isset( $external_services['google_pagespeed']['api_key'] ) ? $external_services['google_pagespeed']['api_key'] : '';

    if ( empty( $api_key ) ) {
        $api_key = get_option( 'boss_optimizer_pagespeed_api_key', '' );
    }

    if ( empty( $api_key ) ) :
    ?>
        <div class="notice notice-warning">
            <p>
                <strong><?php _e( 'Configuration requise', 'boss-seo' ); ?></strong><br>
                <?php _e( 'Pour utiliser l\'analyse technique avec des données réelles, vous devez configurer votre clé API Google PageSpeed Insights.', 'boss-seo' ); ?>
            </p>
            <p>
                <a href="<?php echo admin_url( 'admin.php?page=boss-seo-settings&tab=external-services' ); ?>" class="button button-primary">
                    <?php _e( 'Configurer l\'API PageSpeed', 'boss-seo' ); ?>
                </a>
                <a href="https://developers.google.com/speed/docs/insights/v5/get-started" target="_blank" class="button button-secondary">
                    <?php _e( 'Obtenir une clé API', 'boss-seo' ); ?>
                </a>
            </p>
        </div>
    <?php endif; ?>

    <!-- Interface temporaire d'analyse technique v2.0 -->
    <div id="boss-seo-technical-v2-interface">
        <div class="boss-seo-card">
            <h3><?php _e( 'Analyse Technique v2.0', 'boss-seo' ); ?></h3>

            <div class="boss-seo-form-group">
                <label for="page-selector"><?php _e( 'Sélectionner une page à analyser:', 'boss-seo' ); ?></label>
                <select id="page-selector" class="boss-seo-select">
                    <option value=""><?php _e( 'Chargement des pages...', 'boss-seo' ); ?></option>
                </select>
            </div>

            <div class="boss-seo-form-group">
                <label for="strategy-selector"><?php _e( 'Stratégie d\'analyse:', 'boss-seo' ); ?></label>
                <select id="strategy-selector" class="boss-seo-select">
                    <option value="mobile"><?php _e( 'Mobile', 'boss-seo' ); ?></option>
                    <option value="desktop"><?php _e( 'Desktop', 'boss-seo' ); ?></option>
                </select>
            </div>

            <div class="boss-seo-form-group">
                <button id="analyze-btn" class="boss-seo-btn boss-seo-btn-primary" disabled>
                    <?php _e( 'Analyser la page', 'boss-seo' ); ?>
                </button>
            </div>

            <div id="analysis-status" class="boss-seo-status" style="display: none;"></div>
            <div id="analysis-results" class="boss-seo-results" style="display: none;"></div>
        </div>
    </div>

    <style>
    .boss-seo-card {
        background: #fff;
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 20px;
        margin: 20px 0;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    .boss-seo-form-group {
        margin-bottom: 15px;
    }

    .boss-seo-form-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: 600;
    }

    .boss-seo-select {
        width: 100%;
        max-width: 400px;
        padding: 8px 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
    }

    .boss-seo-btn {
        padding: 10px 20px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        font-weight: 600;
        text-decoration: none;
        display: inline-block;
        transition: all 0.3s ease;
    }

    .boss-seo-btn-primary {
        background: #0073aa;
        color: white;
    }

    .boss-seo-btn-primary:hover:not(:disabled) {
        background: #005a87;
    }

    .boss-seo-btn:disabled {
        background: #ccc;
        cursor: not-allowed;
    }

    .boss-seo-status {
        padding: 10px;
        border-radius: 4px;
        margin: 15px 0;
    }

    .boss-seo-status.loading {
        background: #e7f3ff;
        border: 1px solid #0073aa;
        color: #0073aa;
    }

    .boss-seo-status.success {
        background: #d4edda;
        border: 1px solid #28a745;
        color: #155724;
    }

    .boss-seo-status.error {
        background: #f8d7da;
        border: 1px solid #dc3545;
        color: #721c24;
    }

    .boss-seo-results {
        margin-top: 20px;
        padding: 15px;
        background: #f9f9f9;
        border-radius: 4px;
        border: 1px solid #eee;
    }

    .score-display {
        display: inline-block;
        padding: 5px 10px;
        border-radius: 20px;
        color: white;
        font-weight: bold;
        margin: 5px;
    }

    .score-good { background: #28a745; }
    .score-average { background: #ffc107; color: #212529; }
    .score-poor { background: #dc3545; }
    </style>

    <noscript>
        <div class="notice notice-error">
            <p><?php _e( 'JavaScript est requis pour utiliser le module d\'analyse technique Boss SEO.', 'boss-seo' ); ?></p>
        </div>
    </noscript>
</div>

<?php
// Charger les assets du module d'analyse technique
add_action( 'admin_footer', function() {
    // Charger les assets existants de l'application Boss SEO
    wp_enqueue_script(
        'boss-seo-dashboard',
        plugin_dir_url( dirname( dirname( __FILE__ ) ) ) . 'assets/js/dashboard.js',
        array( 'wp-element', 'wp-components', 'wp-i18n', 'wp-api-fetch' ),
        '1.1.0',
        true
    );

    wp_enqueue_style(
        'boss-seo-dashboard',
        plugin_dir_url( dirname( dirname( __FILE__ ) ) ) . 'assets/js/dashboard.css',
        array( 'wp-components' ),
        '1.1.0'
    );

    // Configuration JavaScript pour l'API v2
    wp_localize_script(
        'boss-seo-dashboard',
        'bossSeoTechnicalV2',
        array(
            'apiUrl'       => rest_url( 'boss-seo/v2/' ),
            'nonce'        => wp_create_nonce( 'wp_rest' ),
            'siteUrl'      => home_url(),
            'version'      => '2.0',
            'strings'      => array(
                'analyzing'       => __( 'Analyse en cours...', 'boss-seo' ),
                'analyzeComplete' => __( 'Analyse terminée', 'boss-seo' ),
                'analyzeError'    => __( 'Erreur lors de l\'analyse', 'boss-seo' ),
                'selectPage'      => __( 'Sélectionnez une page', 'boss-seo' ),
                'configureApi'    => __( 'Configurer l\'API', 'boss-seo' ),
            ),
        )
    );

    // JavaScript pour l'interface d'analyse technique v2.0
    ?>
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Boss SEO Technical Analysis v2.0 interface ready');

        const pageSelector = document.getElementById('page-selector');
        const strategySelector = document.getElementById('strategy-selector');
        const analyzeBtn = document.getElementById('analyze-btn');
        const statusDiv = document.getElementById('analysis-status');
        const resultsDiv = document.getElementById('analysis-results');

        // Configuration de l'API
        const apiConfig = {
            baseUrl: '<?php echo rest_url( 'boss-seo/v2/' ); ?>',
            nonce: '<?php echo wp_create_nonce( 'wp_rest' ); ?>'
        };

        // Fonction pour faire des appels API
        async function apiCall(endpoint, method = 'GET', data = null) {
            const url = apiConfig.baseUrl + endpoint;
            const options = {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                    'X-WP-Nonce': apiConfig.nonce
                }
            };

            if (data && method !== 'GET') {
                options.body = JSON.stringify(data);
            }

            try {
                const response = await fetch(url, options);
                const result = await response.json();
                return result;
            } catch (error) {
                console.error('API Error:', error);
                throw error;
            }
        }

        // Charger la liste des pages
        async function loadPages() {
            try {
                const result = await apiCall('technical/pages');

                if (result.success && result.data) {
                    pageSelector.innerHTML = '<option value="">Sélectionnez une page</option>';

                    result.data.forEach(page => {
                        const option = document.createElement('option');
                        option.value = page.url;
                        option.textContent = `${page.title} (${page.type})`;
                        pageSelector.appendChild(option);
                    });

                    analyzeBtn.disabled = false;
                } else {
                    pageSelector.innerHTML = '<option value="">Erreur lors du chargement</option>';
                }
            } catch (error) {
                console.error('Erreur lors du chargement des pages:', error);
                pageSelector.innerHTML = '<option value="">Erreur lors du chargement</option>';
            }
        }

        // Afficher le statut
        function showStatus(message, type = 'loading') {
            statusDiv.className = `boss-seo-status ${type}`;
            statusDiv.textContent = message;
            statusDiv.style.display = 'block';
        }

        // Masquer le statut
        function hideStatus() {
            statusDiv.style.display = 'none';
        }

        // Afficher les résultats
        function showResults(data) {
            let html = '<h4>Résultats de l\'analyse</h4>';

            // Score global
            if (data.pagespeed && data.pagespeed.lighthouseResult) {
                const categories = data.pagespeed.lighthouseResult.categories;

                // Vérifier si l'API PageSpeed est configurée
                if (data.pagespeed.error) {
                    html += `<div style="margin: 15px 0; padding: 10px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px;">
                        <strong>⚠️ ${data.pagespeed.error}</strong><br>
                        <small>L'analyse technique de base a été effectuée. Pour des données PageSpeed complètes, configurez votre clé API dans Paramètres > Services externes.</small>
                    </div>`;
                }

                html += '<div style="margin: 15px 0;"><strong>Scores PageSpeed:</strong><br>';

                Object.entries(categories).forEach(([key, category]) => {
                    const score = Math.round(category.score * 100);
                    const scoreClass = score >= 80 ? 'score-good' : score >= 50 ? 'score-average' : 'score-poor';
                    const categoryName = {
                        'performance': 'Performance',
                        'seo': 'SEO',
                        'accessibility': 'Accessibilité',
                        'best-practices': 'Bonnes Pratiques'
                    }[key] || key;

                    if (data.pagespeed.error && score === 0) {
                        html += `<span class="score-display score-poor">${categoryName}: Non disponible</span>`;
                    } else {
                        html += `<span class="score-display ${scoreClass}">${categoryName}: ${score}/100</span>`;
                    }
                });

                html += '</div>';
            }

            // Informations techniques
            if (data.technical) {
                html += '<div style="margin: 15px 0;"><strong>Analyse technique:</strong><br>';

                if (data.technical.ssl_check) {
                    const sslStatus = data.technical.ssl_check.valid ? '✅ SSL valide' : '❌ SSL invalide';
                    html += `<p>🔒 ${sslStatus}</p>`;
                }

                if (data.technical.meta_tags) {
                    const meta = data.technical.meta_tags;
                    html += '<p>📝 Meta tags:</p><ul>';
                    if (meta.title) html += `<li>Titre: ${meta.title}</li>`;
                    if (meta.description) html += `<li>Description: ${meta.description}</li>`;
                    if (meta.viewport) html += '<li>✅ Viewport configuré</li>';
                    html += '</ul>';
                }

                html += '</div>';
            }

            // Suggestions IA
            if (data.ai_suggestions && data.ai_suggestions.performance) {
                html += '<div style="margin: 15px 0;"><strong>Suggestions IA:</strong><ul>';
                data.ai_suggestions.performance.forEach(suggestion => {
                    html += `<li>💡 ${suggestion}</li>`;
                });
                html += '</ul></div>';
            }

            // Temps d'analyse
            if (data.analysis_time) {
                html += `<p><small>⏱️ Analyse terminée en ${data.analysis_time}ms</small></p>`;
            }

            resultsDiv.innerHTML = html;
            resultsDiv.style.display = 'block';
        }

        // Analyser une page
        async function analyzePage() {
            const url = pageSelector.value;
            const strategy = strategySelector.value;

            if (!url) {
                alert('Veuillez sélectionner une page à analyser');
                return;
            }

            analyzeBtn.disabled = true;
            showStatus('Analyse en cours... Cela peut prendre quelques secondes.', 'loading');
            resultsDiv.style.display = 'none';

            try {
                const result = await apiCall('technical/analyze', 'POST', {
                    url: url,
                    strategy: strategy,
                    include_ai_suggestions: true
                });

                if (result.success) {
                    showStatus('Analyse terminée avec succès!', 'success');
                    setTimeout(hideStatus, 3000);
                    showResults(result);
                } else {
                    showStatus(`Erreur: ${result.message || 'Analyse échouée'}`, 'error');
                }
            } catch (error) {
                console.error('Erreur lors de l\'analyse:', error);
                showStatus('Erreur lors de l\'analyse. Vérifiez la console pour plus de détails.', 'error');
            } finally {
                analyzeBtn.disabled = false;
            }
        }

        // Event listeners
        analyzeBtn.addEventListener('click', analyzePage);

        // Charger les pages au démarrage
        loadPages();
    });
    </script>
    <?php
} );
?>
