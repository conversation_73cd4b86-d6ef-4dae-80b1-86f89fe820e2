import { useState, useEffect } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  Card,
  CardBody,
  CardHeader,
  TabPanel,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Notice
} from '@wordpress/components';
import { motion } from 'framer-motion';

// Services
import analyticsService from '../services/AnalyticsService';

// Composants
import AnalyticsSetup from '../components/analytics/AnalyticsSetup';
import AnalyticsDashboard from '../components/analytics/AnalyticsDashboard';
import KeywordPerformance from '../components/analytics/KeywordPerformance';
import PopularPages from '../components/analytics/PopularPages';
import OpportunityAnalysis from '../components/analytics/OpportunityAnalysis';
import TrendsAnalysis from '../components/analytics/TrendsAnalysis';
import ReportManager from '../components/analytics/ReportManager';

const AnalyticsManager = () => {
  // États
  const [activeTab, setActiveTab] = useState('dashboard');
  const [isLoading, setIsLoading] = useState(true);
  const [isConfigured, setIsConfigured] = useState(false);
  const [integrations, setIntegrations] = useState({
    ga4: { connected: false, properties: [] },
    gsc: { connected: false, properties: [] }
  });
  const [selectedPeriod, setSelectedPeriod] = useState('last30days');
  const [selectedProperty, setSelectedProperty] = useState(null);
  const [analyticsData, setAnalyticsData] = useState(null);
  const [error, setError] = useState(null);

  // Charger le statut d'authentification au démarrage
  useEffect(() => {
    const loadAuthStatus = async () => {
      try {
        setIsLoading(true);
        setError(null);

        console.log('🔄 Chargement du statut d\'authentification...');
        const status = await analyticsService.getAuthStatus();
        console.log('✅ Statut reçu du backend:', status);

        setIntegrations(status);
        setIsConfigured(status.ga4.connected || status.gsc.connected);

        // Si configuré, charger les données initiales
        if (status.ga4.connected || status.gsc.connected) {
          console.log('📊 Services connectés, chargement des données...');
          await loadAnalyticsData();
        } else {
          console.log('⚠️ Aucun service connecté');
        }

      } catch (error) {
        console.error('❌ Erreur lors du chargement du statut d\'authentification:', error);
        setError(analyticsService.formatError(error));

        // Garder les valeurs par défaut en cas d'erreur
        console.log('🔄 Utilisation des valeurs par défaut');
      } finally {
        setIsLoading(false);
      }
    };

    loadAuthStatus();
  }, []);

  // Charger les données Analytics
  const loadAnalyticsData = async () => {
    try {
      const data = await analyticsService.getDashboardData(selectedPeriod);
      setAnalyticsData(data);
    } catch (error) {
      console.error('Erreur lors du chargement des données Analytics:', error);
      setError(analyticsService.formatError(error));
    }
  };

  // Recharger les données quand la période change
  useEffect(() => {
    if (isConfigured && !isLoading) {
      loadAnalyticsData();
    }
  }, [selectedPeriod, isConfigured]);

  // Gestion des changements de période
  const handlePeriodChange = (newPeriod) => {
    setSelectedPeriod(newPeriod);
  };

  // Gestion des actions de connexion et déconnexion
  const handleConnectGA4 = () => {
    // Rediriger vers le proxy OAuth pour GA4
    const proxyUrl = window.bossAnalyticsConfig?.proxyUrl || 'https://oauth-proxy.bossseo.com';
    const callbackUrl = `${window.location.origin}/wp-admin/admin.php?page=boss-seo-analytics`;

    window.location.href = `${proxyUrl}/auth/ga4?callback=${encodeURIComponent(callbackUrl)}`;
  };

  const handleConnectGSC = () => {
    // Rediriger vers le proxy OAuth pour GSC
    const proxyUrl = window.bossAnalyticsConfig?.proxyUrl || 'https://oauth-proxy.bossseo.com';
    const callbackUrl = `${window.location.origin}/wp-admin/admin.php?page=boss-seo-analytics`;

    window.location.href = `${proxyUrl}/auth/gsc?callback=${encodeURIComponent(callbackUrl)}`;
  };

  const handleDisconnectService = async (service) => {
    try {
      setIsLoading(true);
      await analyticsService.disconnectService(service);

      // Recharger le statut d'authentification
      const status = await analyticsService.getAuthStatus();
      setIntegrations(status);
      setIsConfigured(status.ga4.connected || status.gsc.connected);

      // Vider les données si plus aucun service connecté
      if (!status.ga4.connected && !status.gsc.connected) {
        setAnalyticsData(null);
      }

    } catch (error) {
      console.error('Erreur lors de la déconnexion:', error);
      setError(analyticsService.formatError(error));
    } finally {
      setIsLoading(false);
    }
  };

  // Gestion du rafraîchissement des données
  const handleRefreshData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Forcer la synchronisation
      await analyticsService.forceSyncData();

      // Recharger les données
      await loadAnalyticsData();

    } catch (error) {
      console.error('Erreur lors du rafraîchissement:', error);
      setError(analyticsService.formatError(error));
    } finally {
      setIsLoading(false);
    }
  };

  // Gestion du vidage du cache
  const handleClearCache = async () => {
    try {
      setIsLoading(true);
      await analyticsService.clearCache();

      // Recharger les données
      await loadAnalyticsData();

    } catch (error) {
      console.error('Erreur lors du vidage du cache:', error);
      setError(analyticsService.formatError(error));
    } finally {
      setIsLoading(false);
    }
  };

  // Fonction pour gérer le changement de propriété
  const handlePropertyChange = (propertyId) => {
    setSelectedProperty(propertyId);
  };

  return (
    <div className="boss-flex boss-flex-col boss-min-h-screen">
      <div className="boss-p-6">
        <div className="boss-mb-6">
          <h1 className="boss-text-2xl boss-font-bold boss-text-boss-dark boss-mb-2">
            {__('Intégrations Analytics', 'boss-seo')}
          </h1>
          <p className="boss-text-boss-gray">
            {__('Connectez vos outils d\'analyse et visualisez vos performances SEO', 'boss-seo')}
          </p>
        </div>

        {error && (
          <Notice status="error" isDismissible={true} onRemove={() => setError(null)} className="boss-mb-6">
            <p><strong>{__('Erreur:', 'boss-seo')}</strong> {error.message}</p>
            {error.code && <p><small>{__('Code:', 'boss-seo')} {error.code}</small></p>}
          </Notice>
        )}

        {/* Bouton de test temporaire */}
        <div className="boss-mb-4 boss-p-4 boss-bg-blue-50 boss-border boss-border-blue-200 boss-rounded">
          <h3 className="boss-text-sm boss-font-semibold boss-text-blue-800 boss-mb-2">
            🧪 Test Backend Analytics
          </h3>
          <div className="boss-space-x-2">
            <Button
              isSecondary
              onClick={async () => {
                try {
                  const response = await fetch('/wp-json/boss-seo/v1/auth/status', {
                    headers: { 'X-WP-Nonce': window.bossAnalyticsConfig?.nonce }
                  });
                  const data = await response.json();
                  console.log('✅ Test API réussi:', data);
                  alert('✅ Backend fonctionne ! Voir console pour détails.');
                } catch (error) {
                  console.error('❌ Test API échoué:', error);
                  alert('❌ Backend ne fonctionne pas ! Voir console pour détails.');
                }
              }}
            >
              Tester API
            </Button>
            <Button
              isSecondary
              onClick={() => {
                console.log('Configuration:', window.bossAnalyticsConfig);
                alert('Configuration affichée dans la console');
              }}
            >
              Voir Config
            </Button>
            <Button
              isPrimary
              onClick={async () => {
                setIsLoading(true);
                try {
                  await loadAnalyticsData();
                  alert('✅ Données rechargées !');
                } catch (error) {
                  alert('❌ Erreur: ' + error.message);
                } finally {
                  setIsLoading(false);
                }
              }}
              disabled={isLoading}
            >
              {isLoading ? 'Chargement...' : 'Recharger Données'}
            </Button>
          </div>
        </div>

        {!isConfigured && !isLoading && !error && (
          <Notice status="warning" isDismissible={false} className="boss-mb-6">
            {__('Veuillez configurer vos intégrations Google Analytics 4 et Google Search Console pour accéder aux données.', 'boss-seo')}
          </Notice>
        )}

        {isLoading ? (
          <div className="boss-flex boss-justify-center boss-items-center boss-p-12">
            <Spinner />
          </div>
        ) : (
          <TabPanel
            className="boss-mb-6"
            activeClass="boss-bg-white boss-border-t boss-border-l boss-border-r boss-border-gray-200 boss-rounded-t-lg"
            tabs={[
              {
                name: 'dashboard',
                title: __('Tableau de bord', 'boss-seo'),
                className: 'boss-font-medium boss-px-4 boss-py-2'
              },
              {
                name: 'keywords',
                title: __('Mots-clés', 'boss-seo'),
                className: 'boss-font-medium boss-px-4 boss-py-2'
              },
              {
                name: 'pages',
                title: __('Pages populaires', 'boss-seo'),
                className: 'boss-font-medium boss-px-4 boss-py-2'
              },
              {
                name: 'opportunities',
                title: __('Opportunités', 'boss-seo'),
                className: 'boss-font-medium boss-px-4 boss-py-2'
              },
              {
                name: 'trends',
                title: __('Tendances', 'boss-seo'),
                className: 'boss-font-medium boss-px-4 boss-py-2'
              },
              {
                name: 'reports',
                title: __('Rapports', 'boss-seo'),
                className: 'boss-font-medium boss-px-4 boss-py-2'
              },
              {
                name: 'setup',
                title: __('Configuration', 'boss-seo'),
                className: 'boss-font-medium boss-px-4 boss-py-2'
              }
            ]}
            onSelect={(tabName) => setActiveTab(tabName)}
          >
            {(tab) => {
              if (tab.name === 'setup') {
                return (
                  <AnalyticsSetup
                    integrations={integrations}
                    onConnectGA4={handleConnectGA4}
                    onConnectGSC={handleConnectGSC}
                    onDisconnectService={handleDisconnectService}
                    onPropertyChange={handlePropertyChange}
                    onRefreshData={handleRefreshData}
                    onClearCache={handleClearCache}
                    isLoading={isLoading}
                  />
                );
              } else if (!isConfigured) {
                return (
                  <Card>
                    <CardBody>
                      <div className="boss-text-center boss-py-12">
                        <p className="boss-text-boss-gray boss-mb-6">
                          {__('Veuillez configurer vos intégrations pour accéder à cette section.', 'boss-seo')}
                        </p>
                        <Button
                          isPrimary
                          onClick={() => setActiveTab('setup')}
                        >
                          {__('Configurer les intégrations', 'boss-seo')}
                        </Button>
                      </div>
                    </CardBody>
                  </Card>
                );
              } else if (tab.name === 'dashboard') {
                return (
                  <AnalyticsDashboard
                    data={analyticsData}
                    selectedPeriod={selectedPeriod}
                    onPeriodChange={handlePeriodChange}
                  />
                );
              } else if (tab.name === 'keywords') {
                return (
                  <KeywordPerformance
                    data={analyticsData?.gsc?.keywords || []}
                    selectedPeriod={selectedPeriod}
                    onPeriodChange={handlePeriodChange}
                  />
                );
              } else if (tab.name === 'pages') {
                return (
                  <PopularPages
                    data={analyticsData?.ga4?.pages || []}
                    selectedPeriod={selectedPeriod}
                    onPeriodChange={handlePeriodChange}
                  />
                );
              } else if (tab.name === 'opportunities') {
                return (
                  <OpportunityAnalysis
                    data={analyticsData?.opportunities || []}
                  />
                );
              } else if (tab.name === 'trends') {
                return (
                  <TrendsAnalysis
                    data={analyticsData}
                    selectedPeriod={selectedPeriod}
                    onPeriodChange={handlePeriodChange}
                  />
                );
              } else if (tab.name === 'reports') {
                return (
                  <ReportManager
                    analyticsData={analyticsData}
                  />
                );
              }
            }}
          </TabPanel>
        )}
      </div>
    </div>
  );
};

export default AnalyticsManager;
