import { useState, useEffect } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  Card,
  CardBody,
  CardHeader,
  CardFooter,
  Button,
  Dashicon,
  TextControl,
  TextareaControl,
  SelectControl,
  ToggleControl,
  Notice,
  Spinner
} from '@wordpress/components';

// Importer le service
import LocalSeoService from '../../services/LocalSeoService';

const BusinessInfo = () => {
  // États
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [error, setError] = useState(null);
  const [businessInfo, setBusinessInfo] = useState({
    name: '',
    legalName: '',
    type: '',
    description: '',
    logo: '',
    foundingDate: '',
    address: {
      street: '',
      city: '',
      postalCode: '',
      region: '',
      country: 'FR'
    },
    contact: {
      phone: '',
      email: '',
      website: ''
    },
    socialProfiles: {
      facebook: '',
      twitter: '',
      instagram: '',
      linkedin: '',
      youtube: ''
    },
    openingHours: '',
    paymentAccepted: [],
    priceRange: '',
    hasMap: true,
    geo: {
      latitude: '',
      longitude: ''
    }
  });

  // Créer une instance du service
  const localSeoService = new LocalSeoService();

  // Charger les données
  useEffect(() => {
    const fetchBusinessInfo = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Récupérer les informations d'entreprise
        const response = await localSeoService.getBusinessInfo();

        // Formater les données si nécessaire
        const formattedInfo = {
          name: response.name || '',
          legalName: response.legalName || '',
          type: response.type || '',
          description: response.description || '',
          logo: response.logo || '',
          foundingDate: response.foundingDate || '',
          address: {
            street: response.address?.street || '',
            city: response.address?.city || '',
            postalCode: response.address?.postalCode || '',
            region: response.address?.region || '',
            country: response.address?.country || 'FR'
          },
          contact: {
            phone: response.contact?.phone || '',
            email: response.contact?.email || '',
            website: response.contact?.website || ''
          },
          socialProfiles: {
            facebook: response.socialProfiles?.facebook || '',
            twitter: response.socialProfiles?.twitter || '',
            instagram: response.socialProfiles?.instagram || '',
            linkedin: response.socialProfiles?.linkedin || '',
            youtube: response.socialProfiles?.youtube || ''
          },
          openingHours: response.openingHours || '',
          paymentAccepted: response.paymentAccepted || [],
          priceRange: response.priceRange || '',
          hasMap: response.hasMap !== undefined ? response.hasMap : true,
          geo: {
            latitude: response.geo?.latitude || '',
            longitude: response.geo?.longitude || ''
          }
        };

        // Mettre à jour l'état
        setBusinessInfo(formattedInfo);
      } catch (err) {
        console.error('Erreur lors du chargement des informations d\'entreprise:', err);
        setError(__('Erreur lors du chargement des informations d\'entreprise. Veuillez réessayer.', 'boss-seo'));

        // Initialiser avec des données vides en cas d'erreur
        const emptyBusinessInfo = {
          name: '',
          legalName: '',
          type: '',
          description: '',
          logo: '',
          foundingDate: '',
          address: {
            street: '',
            city: '',
            postalCode: '',
            region: '',
            country: 'FR'
          },
          contact: {
            phone: '',
            email: '',
            website: ''
          },
          socialProfiles: {
            facebook: '',
            twitter: '',
            instagram: '',
            linkedin: '',
            youtube: ''
          },
          openingHours: '',
          paymentAccepted: [],
          priceRange: '',
          hasMap: true,
          geo: {
            latitude: '',
            longitude: ''
          }
        };

        setBusinessInfo(emptyBusinessInfo);
      } finally {
        setIsLoading(false);
      }
    };

    fetchBusinessInfo();
  }, []);

  // Fonction pour mettre à jour les informations d'entreprise
  const updateBusinessInfo = (section, field, value) => {
    if (section) {
      setBusinessInfo({
        ...businessInfo,
        [section]: {
          ...businessInfo[section],
          [field]: value
        }
      });
    } else {
      setBusinessInfo({
        ...businessInfo,
        [field]: value
      });
    }
  };

  // Fonction pour gérer les méthodes de paiement
  const handlePaymentMethod = (method, isChecked) => {
    if (isChecked) {
      setBusinessInfo({
        ...businessInfo,
        paymentAccepted: [...businessInfo.paymentAccepted, method]
      });
    } else {
      setBusinessInfo({
        ...businessInfo,
        paymentAccepted: businessInfo.paymentAccepted.filter(m => m !== method)
      });
    }
  };

  // Fonction pour sauvegarder les informations
  const handleSave = async () => {
    try {
      setIsSaving(true);
      setError(null);

      // Appeler le service pour enregistrer les informations
      await localSeoService.saveBusinessInfo(businessInfo);

      // Afficher le message de succès
      setShowSuccess(true);

      // Masquer le message de succès après 3 secondes
      setTimeout(() => {
        setShowSuccess(false);
      }, 3000);
    } catch (err) {
      console.error('Erreur lors de l\'enregistrement des informations d\'entreprise:', err);
      setError(__('Erreur lors de l\'enregistrement des informations d\'entreprise. Veuillez réessayer.', 'boss-seo'));
    } finally {
      setIsSaving(false);
    }
  };

  // Fonction pour obtenir le score de complétude
  const getCompletenessScore = () => {
    const requiredFields = [
      businessInfo.name,
      businessInfo.type,
      businessInfo.description,
      businessInfo.address.street,
      businessInfo.address.city,
      businessInfo.address.postalCode,
      businessInfo.address.country,
      businessInfo.contact.phone,
      businessInfo.contact.email,
      businessInfo.contact.website,
      businessInfo.openingHours
    ];

    const filledFields = requiredFields.filter(field => field && field.trim() !== '').length;
    return Math.round((filledFields / requiredFields.length) * 100);
  };

  // Obtenir le score de complétude
  const completenessScore = getCompletenessScore();

  // Fonction pour obtenir la classe de couleur en fonction du score
  const getScoreColorClass = (score) => {
    if (score >= 80) return 'boss-text-green-600';
    if (score >= 60) return 'boss-text-yellow-600';
    return 'boss-text-red-600';
  };

  return (
    <div>
      {isLoading ? (
        <div className="boss-flex boss-justify-center boss-items-center boss-p-12">
          <Spinner />
        </div>
      ) : (
        <div>
          {error && (
            <Notice status="error" isDismissible={false} className="boss-mb-6">
              {error}
            </Notice>
          )}

          {showSuccess && (
            <Notice status="success" isDismissible={false} className="boss-mb-6">
              {__('Les informations de l\'entreprise ont été enregistrées avec succès.', 'boss-seo')}
            </Notice>
          )}

          <div className="boss-grid boss-grid-cols-1 lg:boss-grid-cols-3 boss-gap-6">
            {/* Informations principales */}
            <div className="lg:boss-col-span-2">
              <Card className="boss-mb-6">
                <CardHeader className="boss-border-b boss-border-gray-200">
                  <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                    {__('Informations principales', 'boss-seo')}
                  </h2>
                </CardHeader>
                <CardBody>
                  <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 boss-gap-4 boss-mb-4">
                    <TextControl
                      label={__('Nom de l\'entreprise', 'boss-seo')}
                      value={businessInfo.name}
                      onChange={(value) => updateBusinessInfo(null, 'name', value)}
                    />

                    <TextControl
                      label={__('Raison sociale', 'boss-seo')}
                      value={businessInfo.legalName}
                      onChange={(value) => updateBusinessInfo(null, 'legalName', value)}
                    />

                    <SelectControl
                      label={__('Type d\'entreprise', 'boss-seo')}
                      value={businessInfo.type}
                      options={[
                        { label: __('-- Sélectionner --', 'boss-seo'), value: '' },
                        { label: __('Entreprise locale', 'boss-seo'), value: 'LocalBusiness' },
                        { label: __('Restaurant', 'boss-seo'), value: 'Restaurant' },
                        { label: __('Magasin', 'boss-seo'), value: 'Store' },
                        { label: __('Service', 'boss-seo'), value: 'Service' },
                        { label: __('Organisation', 'boss-seo'), value: 'Organization' }
                      ]}
                      onChange={(value) => updateBusinessInfo(null, 'type', value)}
                    />

                    <TextControl
                      label={__('Date de création', 'boss-seo')}
                      type="date"
                      value={businessInfo.foundingDate}
                      onChange={(value) => updateBusinessInfo(null, 'foundingDate', value)}
                    />
                  </div>

                  <TextareaControl
                    label={__('Description', 'boss-seo')}
                    value={businessInfo.description}
                    onChange={(value) => updateBusinessInfo(null, 'description', value)}
                    className="boss-mb-4"
                  />

                  <TextControl
                    label={__('URL du logo', 'boss-seo')}
                    value={businessInfo.logo}
                    onChange={(value) => updateBusinessInfo(null, 'logo', value)}
                  />
                </CardBody>
              </Card>

              <Card className="boss-mb-6">
                <CardHeader className="boss-border-b boss-border-gray-200">
                  <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                    {__('Adresse', 'boss-seo')}
                  </h2>
                </CardHeader>
                <CardBody>
                  <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 boss-gap-4">
                    <div className="boss-col-span-full">
                      <TextControl
                        label={__('Rue', 'boss-seo')}
                        value={businessInfo.address.street}
                        onChange={(value) => updateBusinessInfo('address', 'street', value)}
                      />
                    </div>

                    <TextControl
                      label={__('Ville', 'boss-seo')}
                      value={businessInfo.address.city}
                      onChange={(value) => updateBusinessInfo('address', 'city', value)}
                    />

                    <TextControl
                      label={__('Code postal', 'boss-seo')}
                      value={businessInfo.address.postalCode}
                      onChange={(value) => updateBusinessInfo('address', 'postalCode', value)}
                    />

                    <TextControl
                      label={__('Région', 'boss-seo')}
                      value={businessInfo.address.region}
                      onChange={(value) => updateBusinessInfo('address', 'region', value)}
                    />

                    <SelectControl
                      label={__('Pays', 'boss-seo')}
                      value={businessInfo.address.country}
                      options={[
                        { label: 'France', value: 'FR' },
                        { label: 'Belgique', value: 'BE' },
                        { label: 'Suisse', value: 'CH' },
                        { label: 'Canada', value: 'CA' }
                      ]}
                      onChange={(value) => updateBusinessInfo('address', 'country', value)}
                    />
                  </div>
                </CardBody>
              </Card>

              <Card className="boss-mb-6">
                <CardHeader className="boss-border-b boss-border-gray-200">
                  <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                    {__('Contact', 'boss-seo')}
                  </h2>
                </CardHeader>
                <CardBody>
                  <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 boss-gap-4">
                    <TextControl
                      label={__('Téléphone', 'boss-seo')}
                      value={businessInfo.contact.phone}
                      onChange={(value) => updateBusinessInfo('contact', 'phone', value)}
                    />

                    <TextControl
                      label={__('Email', 'boss-seo')}
                      type="email"
                      value={businessInfo.contact.email}
                      onChange={(value) => updateBusinessInfo('contact', 'email', value)}
                    />

                    <div className="boss-col-span-full">
                      <TextControl
                        label={__('Site web', 'boss-seo')}
                        type="url"
                        value={businessInfo.contact.website}
                        onChange={(value) => updateBusinessInfo('contact', 'website', value)}
                      />
                    </div>
                  </div>
                </CardBody>
              </Card>

              <Card className="boss-mb-6">
                <CardHeader className="boss-border-b boss-border-gray-200">
                  <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                    {__('Réseaux sociaux', 'boss-seo')}
                  </h2>
                </CardHeader>
                <CardBody>
                  <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 boss-gap-4">
                    <TextControl
                      label={__('Facebook', 'boss-seo')}
                      type="url"
                      value={businessInfo.socialProfiles.facebook}
                      onChange={(value) => updateBusinessInfo('socialProfiles', 'facebook', value)}
                    />

                    <TextControl
                      label={__('Twitter', 'boss-seo')}
                      type="url"
                      value={businessInfo.socialProfiles.twitter}
                      onChange={(value) => updateBusinessInfo('socialProfiles', 'twitter', value)}
                    />

                    <TextControl
                      label={__('Instagram', 'boss-seo')}
                      type="url"
                      value={businessInfo.socialProfiles.instagram}
                      onChange={(value) => updateBusinessInfo('socialProfiles', 'instagram', value)}
                    />

                    <TextControl
                      label={__('LinkedIn', 'boss-seo')}
                      type="url"
                      value={businessInfo.socialProfiles.linkedin}
                      onChange={(value) => updateBusinessInfo('socialProfiles', 'linkedin', value)}
                    />

                    <TextControl
                      label={__('YouTube', 'boss-seo')}
                      type="url"
                      value={businessInfo.socialProfiles.youtube}
                      onChange={(value) => updateBusinessInfo('socialProfiles', 'youtube', value)}
                    />
                  </div>
                </CardBody>
              </Card>

              <Card className="boss-mb-6">
                <CardHeader className="boss-border-b boss-border-gray-200">
                  <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                    {__('Informations complémentaires', 'boss-seo')}
                  </h2>
                </CardHeader>
                <CardBody>
                  <div className="boss-mb-4">
                    <TextControl
                      label={__('Horaires d\'ouverture (format Schema.org)', 'boss-seo')}
                      help={__('Ex: Mo-Fr 09:00-18:00, Sa 10:00-17:00', 'boss-seo')}
                      value={businessInfo.openingHours}
                      onChange={(value) => updateBusinessInfo(null, 'openingHours', value)}
                    />
                  </div>

                  <div className="boss-mb-4">
                    <label className="boss-block boss-mb-2 boss-text-sm boss-font-medium">
                      {__('Moyens de paiement acceptés', 'boss-seo')}
                    </label>
                    <div className="boss-grid boss-grid-cols-2 md:boss-grid-cols-3 boss-gap-2">
                      <ToggleControl
                        label={__('Espèces', 'boss-seo')}
                        checked={businessInfo.paymentAccepted.includes('cash')}
                        onChange={(checked) => handlePaymentMethod('cash', checked)}
                      />
                      <ToggleControl
                        label={__('Carte de crédit', 'boss-seo')}
                        checked={businessInfo.paymentAccepted.includes('credit_card')}
                        onChange={(checked) => handlePaymentMethod('credit_card', checked)}
                      />
                      <ToggleControl
                        label={__('PayPal', 'boss-seo')}
                        checked={businessInfo.paymentAccepted.includes('paypal')}
                        onChange={(checked) => handlePaymentMethod('paypal', checked)}
                      />
                      <ToggleControl
                        label={__('Virement bancaire', 'boss-seo')}
                        checked={businessInfo.paymentAccepted.includes('bank_transfer')}
                        onChange={(checked) => handlePaymentMethod('bank_transfer', checked)}
                      />
                      <ToggleControl
                        label={__('Chèque', 'boss-seo')}
                        checked={businessInfo.paymentAccepted.includes('check')}
                        onChange={(checked) => handlePaymentMethod('check', checked)}
                      />
                    </div>
                  </div>

                  <div className="boss-mb-4">
                    <SelectControl
                      label={__('Gamme de prix', 'boss-seo')}
                      value={businessInfo.priceRange}
                      options={[
                        { label: __('-- Sélectionner --', 'boss-seo'), value: '' },
                        { label: '€ (Économique)', value: '€' },
                        { label: '€€ (Modéré)', value: '€€' },
                        { label: '€€€ (Élevé)', value: '€€€' },
                        { label: '€€€€ (Très élevé)', value: '€€€€' }
                      ]}
                      onChange={(value) => updateBusinessInfo(null, 'priceRange', value)}
                    />
                  </div>

                  <div className="boss-mb-4">
                    <ToggleControl
                      label={__('Afficher une carte sur la fiche Google', 'boss-seo')}
                      checked={businessInfo.hasMap}
                      onChange={(checked) => updateBusinessInfo(null, 'hasMap', checked)}
                    />
                  </div>

                  {businessInfo.hasMap && (
                    <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 boss-gap-4">
                      <TextControl
                        label={__('Latitude', 'boss-seo')}
                        value={businessInfo.geo.latitude}
                        onChange={(value) => updateBusinessInfo('geo', 'latitude', value)}
                      />

                      <TextControl
                        label={__('Longitude', 'boss-seo')}
                        value={businessInfo.geo.longitude}
                        onChange={(value) => updateBusinessInfo('geo', 'longitude', value)}
                      />
                    </div>
                  )}
                </CardBody>
                <CardFooter className="boss-border-t boss-border-gray-200">
                  <div className="boss-flex boss-justify-end">
                    <Button
                      isPrimary
                      onClick={handleSave}
                      isBusy={isSaving}
                      disabled={isSaving}
                    >
                      {isSaving ? __('Enregistrement...', 'boss-seo') : __('Enregistrer les informations', 'boss-seo')}
                    </Button>
                  </div>
                </CardFooter>
              </Card>
            </div>

            {/* Panneau latéral */}
            <div>
              <Card className="boss-mb-6">
                <CardHeader className="boss-border-b boss-border-gray-200">
                  <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                    {__('Complétude du profil', 'boss-seo')}
                  </h2>
                </CardHeader>
                <CardBody>
                  <div className="boss-text-center boss-mb-4">
                    <div className={`boss-text-4xl boss-font-bold boss-mb-2 ${getScoreColorClass(completenessScore)}`}>
                      {completenessScore}%
                    </div>
                    <p className="boss-text-boss-gray">
                      {completenessScore >= 80
                        ? __('Excellent ! Votre profil est bien renseigné.', 'boss-seo')
                        : completenessScore >= 60
                          ? __('Bien ! Complétez les informations manquantes pour améliorer votre score.', 'boss-seo')
                          : __('Attention ! Votre profil manque d\'informations importantes.', 'boss-seo')
                      }
                    </p>
                  </div>

                  <div className="boss-w-full boss-bg-gray-200 boss-rounded-full boss-h-2.5 boss-mb-6">
                    <div
                      className={`boss-h-2.5 boss-rounded-full ${
                        completenessScore >= 80
                          ? 'boss-bg-green-600'
                          : completenessScore >= 60
                            ? 'boss-bg-yellow-600'
                            : 'boss-bg-red-600'
                      }`}
                      style={{ width: `${completenessScore}%` }}
                    ></div>
                  </div>

                  <div className="boss-space-y-3">
                    <h3 className="boss-font-medium boss-text-boss-dark">
                      {__('Informations manquantes :', 'boss-seo')}
                    </h3>
                    <ul className="boss-space-y-1 boss-text-sm">
                      {!businessInfo.name && (
                        <li className="boss-flex boss-items-center boss-text-red-600">
                          <Dashicon icon="warning" className="boss-mr-1" />
                          {__('Nom de l\'entreprise', 'boss-seo')}
                        </li>
                      )}
                      {!businessInfo.type && (
                        <li className="boss-flex boss-items-center boss-text-red-600">
                          <Dashicon icon="warning" className="boss-mr-1" />
                          {__('Type d\'entreprise', 'boss-seo')}
                        </li>
                      )}
                      {!businessInfo.description && (
                        <li className="boss-flex boss-items-center boss-text-red-600">
                          <Dashicon icon="warning" className="boss-mr-1" />
                          {__('Description', 'boss-seo')}
                        </li>
                      )}
                      {!businessInfo.address.street && (
                        <li className="boss-flex boss-items-center boss-text-red-600">
                          <Dashicon icon="warning" className="boss-mr-1" />
                          {__('Adresse', 'boss-seo')}
                        </li>
                      )}
                      {!businessInfo.contact.phone && (
                        <li className="boss-flex boss-items-center boss-text-red-600">
                          <Dashicon icon="warning" className="boss-mr-1" />
                          {__('Téléphone', 'boss-seo')}
                        </li>
                      )}
                      {!businessInfo.contact.email && (
                        <li className="boss-flex boss-items-center boss-text-red-600">
                          <Dashicon icon="warning" className="boss-mr-1" />
                          {__('Email', 'boss-seo')}
                        </li>
                      )}
                      {!businessInfo.contact.website && (
                        <li className="boss-flex boss-items-center boss-text-red-600">
                          <Dashicon icon="warning" className="boss-mr-1" />
                          {__('Site web', 'boss-seo')}
                        </li>
                      )}
                      {!businessInfo.openingHours && (
                        <li className="boss-flex boss-items-center boss-text-red-600">
                          <Dashicon icon="warning" className="boss-mr-1" />
                          {__('Horaires d\'ouverture', 'boss-seo')}
                        </li>
                      )}
                      {!businessInfo.logo && (
                        <li className="boss-flex boss-items-center boss-text-yellow-600">
                          <Dashicon icon="info" className="boss-mr-1" />
                          {__('Logo', 'boss-seo')}
                        </li>
                      )}
                      {businessInfo.socialProfiles.facebook === '' &&
                       businessInfo.socialProfiles.twitter === '' &&
                       businessInfo.socialProfiles.instagram === '' &&
                       businessInfo.socialProfiles.linkedin === '' &&
                       businessInfo.socialProfiles.youtube === '' && (
                        <li className="boss-flex boss-items-center boss-text-yellow-600">
                          <Dashicon icon="info" className="boss-mr-1" />
                          {__('Réseaux sociaux', 'boss-seo')}
                        </li>
                      )}
                    </ul>
                  </div>
                </CardBody>
              </Card>

              <Card>
                <CardHeader className="boss-border-b boss-border-gray-200">
                  <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                    {__('Conseils SEO local', 'boss-seo')}
                  </h2>
                </CardHeader>
                <CardBody>
                  <div className="boss-space-y-4 boss-text-sm">
                    <div>
                      <h3 className="boss-font-medium boss-text-boss-dark boss-mb-1">
                        {__('Cohérence NAP', 'boss-seo')}
                      </h3>
                      <p className="boss-text-boss-gray">
                        {__('Assurez-vous que votre Nom, Adresse et Téléphone (NAP) sont identiques sur toutes les plateformes.', 'boss-seo')}
                      </p>
                    </div>

                    <div>
                      <h3 className="boss-font-medium boss-text-boss-dark boss-mb-1">
                        {__('Google Business Profile', 'boss-seo')}
                      </h3>
                      <p className="boss-text-boss-gray">
                        {__('Créez et optimisez votre profil Google Business pour améliorer votre visibilité locale.', 'boss-seo')}
                      </p>
                    </div>

                    <div>
                      <h3 className="boss-font-medium boss-text-boss-dark boss-mb-1">
                        {__('Avis clients', 'boss-seo')}
                      </h3>
                      <p className="boss-text-boss-gray">
                        {__('Encouragez vos clients à laisser des avis positifs sur Google, Facebook et d\'autres plateformes.', 'boss-seo')}
                      </p>
                    </div>

                    <div>
                      <h3 className="boss-font-medium boss-text-boss-dark boss-mb-1">
                        {__('Citations locales', 'boss-seo')}
                      </h3>
                      <p className="boss-text-boss-gray">
                        {__('Inscrivez votre entreprise dans les annuaires locaux pertinents pour renforcer votre présence en ligne.', 'boss-seo')}
                      </p>
                    </div>
                  </div>
                </CardBody>
              </Card>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default BusinessInfo;
