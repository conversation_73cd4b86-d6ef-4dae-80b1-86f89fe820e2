/**
 * Styles pour l'éditeur de texte riche Boss SEO
 */

/* Container principal de l'éditeur */
.boss-rich-text-editor {
  @apply boss-w-full;
}

/* <PERSON><PERSON> d'outils */
.boss-rich-text-editor .boss-toolbar {
  @apply boss-border boss-border-gray-300 boss-rounded-t-md boss-bg-gray-50 boss-p-2;
  border-bottom: none;
}

.boss-rich-text-editor .boss-toolbar .boss-button-group {
  @apply boss-inline-flex boss-rounded-md boss-shadow-sm;
}

.boss-rich-text-editor .boss-toolbar .boss-button-group .boss-button {
  @apply boss-relative boss-inline-flex boss-items-center boss-px-2 boss-py-1 boss-text-sm boss-font-medium boss-text-gray-700 boss-bg-white boss-border boss-border-gray-300;
  @apply hover:boss-bg-gray-50 focus:boss-z-10 focus:boss-outline-none focus:boss-ring-1 focus:boss-ring-blue-500 focus:boss-border-blue-500;
}

.boss-rich-text-editor .boss-toolbar .boss-button-group .boss-button:first-child {
  @apply boss-rounded-l-md;
}

.boss-rich-text-editor .boss-toolbar .boss-button-group .boss-button:last-child {
  @apply boss-rounded-r-md;
}

.boss-rich-text-editor .boss-toolbar .boss-button-group .boss-button:not(:first-child) {
  @apply -boss-ml-px;
}

.boss-rich-text-editor .boss-toolbar .boss-button-group .boss-button.boss-active {
  @apply boss-bg-blue-50 boss-text-blue-700 boss-border-blue-500;
}

/* Zone d'édition */
.boss-rich-text-editor textarea {
  @apply boss-w-full boss-border boss-border-t-0 boss-border-gray-300 boss-rounded-b-md boss-p-3 boss-text-sm boss-resize-y boss-min-h-[300px];
  @apply focus:boss-ring-2 focus:boss-ring-blue-500 focus:boss-border-blue-500 focus:boss-outline-none;
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  line-height: 1.6;
}

.boss-rich-text-editor textarea::placeholder {
  @apply boss-text-gray-400;
  font-style: italic;
}

/* Compteurs */
.boss-rich-text-editor .boss-counters {
  @apply boss-mt-2 boss-flex boss-justify-between boss-text-xs boss-text-gray-500;
}

/* Animations pour les boutons */
.boss-rich-text-editor .boss-toolbar .boss-button {
  transition: all 0.15s ease-in-out;
}

.boss-rich-text-editor .boss-toolbar .boss-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.boss-rich-text-editor .boss-toolbar .boss-button:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Responsive */
@media (max-width: 768px) {
  .boss-rich-text-editor .boss-toolbar {
    @apply boss-p-1;
  }
  
  .boss-rich-text-editor .boss-toolbar .boss-button-group {
    @apply boss-mb-1;
  }
  
  .boss-rich-text-editor .boss-toolbar .boss-button {
    @apply boss-px-1 boss-py-1 boss-text-xs;
  }
}

/* Styles pour les icônes Dashicon */
.boss-rich-text-editor .dashicon {
  width: 16px;
  height: 16px;
  font-size: 16px;
}

/* Amélioration de l'accessibilité */
.boss-rich-text-editor .boss-toolbar .boss-button:focus {
  @apply boss-ring-2 boss-ring-blue-500 boss-ring-offset-1;
}

.boss-rich-text-editor textarea:focus {
  @apply boss-ring-2 boss-ring-blue-500 boss-ring-offset-1;
}

/* Styles pour les groupes de boutons séparés */
.boss-rich-text-editor .boss-toolbar .boss-button-group + .boss-button-group {
  @apply boss-ml-2;
}

/* Indicateur de texte sélectionné */
.boss-rich-text-editor textarea::selection {
  @apply boss-bg-blue-200;
}

/* Styles pour les tooltips */
.boss-rich-text-editor .boss-toolbar .boss-button[title]:hover::after {
  content: attr(title);
  @apply boss-absolute boss-bottom-full boss-left-1/2 boss-transform boss--translate-x-1/2 boss-mb-1;
  @apply boss-px-2 boss-py-1 boss-text-xs boss-text-white boss-bg-gray-900 boss-rounded boss-whitespace-nowrap;
  @apply boss-opacity-0 boss-pointer-events-none;
  animation: boss-tooltip-fade-in 0.2s ease-in-out forwards;
  z-index: 1000;
}

@keyframes boss-tooltip-fade-in {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-4px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

/* Styles pour le mode sombre (si nécessaire) */
@media (prefers-color-scheme: dark) {
  .boss-rich-text-editor .boss-toolbar {
    @apply boss-bg-gray-800 boss-border-gray-600;
  }
  
  .boss-rich-text-editor .boss-toolbar .boss-button {
    @apply boss-text-gray-300 boss-bg-gray-700 boss-border-gray-600;
  }
  
  .boss-rich-text-editor .boss-toolbar .boss-button:hover {
    @apply boss-bg-gray-600;
  }
  
  .boss-rich-text-editor textarea {
    @apply boss-bg-gray-800 boss-text-gray-100 boss-border-gray-600;
  }
  
  .boss-rich-text-editor textarea::placeholder {
    @apply boss-text-gray-500;
  }
}
