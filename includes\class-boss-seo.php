<?php
/**
 * The core plugin class.
 *
 * @link       https://bossseo.com
 * @since      1.1.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 */

/**
 * The core plugin class.
 *
 * This is used to define internationalization, admin-specific hooks, and
 * public-facing site hooks.
 *
 * @since      1.1.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 * <AUTHOR> SEO Team
 */
class Boss_SEO {

    /**
     * The loader that's responsible for maintaining and registering all hooks that power
     * the plugin.
     *
     * @since    1.1.0
     * @access   protected
     * @var      Boss_SEO_Loader    $loader    Maintains and registers all hooks for the plugin.
     */
    protected $loader;

    /**
     * The unique identifier of this plugin.
     *
     * @since    1.1.0
     * @access   protected
     * @var      string    $plugin_name    The string used to uniquely identify this plugin.
     */
    protected $plugin_name;

    /**
     * The current version of the plugin.
     *
     * @since    1.1.0
     * @access   protected
     * @var      string    $version    The current version of the plugin.
     */
    protected $version;

    /**
     * Instance du module Boss Optimizer.
     *
     * @since    1.1.0
     * @access   protected
     * @var      Boss_Optimizer    $optimizer    Instance du module Boss Optimizer.
     */
    protected $optimizer;

    /**
     * Instance du module de gestion technique.
     *
     * @since    1.1.0
     * @access   protected
     * @var      Boss_Technical_Management    $technical_management    Instance du module de gestion technique.
     */
    protected $technical_management;

    /**
     * Instance du module SEO local.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Local    $local    Instance du module SEO local.
     */
    protected $local;

    /**
     * Instance du module Schémas structurés.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Structured_Schemas    $structured_schemas    Instance du module Schémas structurés.
     */
    protected $structured_schemas;

    /**
     * Instance du module E-commerce.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Ecommerce    $ecommerce    Instance du module E-commerce.
     */
    protected $ecommerce;

    /**
     * Instance du module de paramètres.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_SEO_Settings    $settings    Instance du module de paramètres.
     */
    protected $settings;

    /**
     * Instance du service d'IA.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Optimizer_AI    $ai    Instance du service d'IA.
     */
    protected $ai;

    /**
     * Instance du gestionnaire AJAX pour les paramètres.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_SEO_Settings_AJAX    $settings_ajax    Instance du gestionnaire AJAX pour les paramètres.
     */
    protected $settings_ajax;

    /**
     * Instance des scripts du module de paramètres.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_SEO_Settings_Scripts    $settings_scripts    Instance des scripts du module de paramètres.
     */
    protected $settings_scripts;

    /**
     * Instance du module de services externes.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_External_Services    $external_services    Instance du module de services externes.
     */
    protected $external_services;

    /**
     * Instance du module d'optimisation de contenu multistep.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_SEO_Content_Multistep    $content_multistep    Instance du module d'optimisation de contenu multistep.
     */
    protected $content_multistep;

    /**
     * Instance du module frontend.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_SEO_Frontend    $frontend    Instance du module frontend.
     */
    protected $frontend;

    /**
     * Instance du module d'analyse technique v2.0.
     *
     * @since    1.1.0
     * @access   protected
     * @var      Boss_Technical_Analysis_Integration    $technical_analysis_v2    Instance du module d'analyse technique v2.0.
     */
    protected $technical_analysis_v2;

    /**
     * Instance du gestionnaire de cache.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Cache_Manager    $cache_manager    Instance du gestionnaire de cache.
     */
    protected $cache_manager;

    /**
     * Instance de l'API de cache.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Cache_API    $cache_api    Instance de l'API de cache.
     */
    protected $cache_api;

    /**
     * Instance de la barre d'administration du cache.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Cache_Admin_Bar    $cache_admin_bar    Instance de la barre d'administration du cache.
     */
    protected $cache_admin_bar;

    /**
     * Instance du module d'analyse technique professionnel.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Technical_Analysis_Module    $technical_analysis_module    Instance du module d'analyse technique.
     */
    protected $technical_analysis_module;

    /**
     * Instance du module Audit SEO assisté par IA.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Audit_IA_API    $audit_ia_api    Instance du module Audit SEO assisté par IA.
     */
    protected $audit_ia_api;

    /**
     * Define the core functionality of the plugin.
     *
     * @since    1.1.0
     */
    public function __construct() {
        if ( defined( 'BOSS_SEO_VERSION' ) ) {
            $this->version = BOSS_SEO_VERSION;
        } else {
            $this->version = '1.1.0';
        }
        $this->plugin_name = 'boss-seo';

        $this->load_dependencies();
        $this->set_locale();
        $this->define_admin_hooks();
    }

    /**
     * Load the required dependencies for this plugin.
     *
     * @since    1.1.0
     * @access   private
     */
    private function load_dependencies() {
        /**
         * The class responsible for orchestrating the actions and filters of the
         * core plugin.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-boss-seo-loader.php';

        /**
         * The class responsible for defining internationalization functionality
         * of the plugin.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-boss-seo-i18n.php';

        /**
         * The class responsible for defining all actions that occur in the admin area.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'admin/class-boss-seo-admin.php';

        /**
         * Le module Boss Optimizer.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-boss-optimizer.php';

        /**
         * Le module de gestion technique.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-boss-technical-management.php';

        /**
         * Le module SEO local.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-boss-local.php';

        /**
         * Le module Schémas structurés.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/structured-schemas/class-boss-structured-schemas.php';

        /**
         * Le module E-commerce.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/ecommerce/class-boss-ecommerce.php';

        /**
         * Le module de paramètres.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-boss-seo-settings.php';

        /**
         * Le gestionnaire AJAX.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-boss-seo-ajax.php';

        /**
         * Les scripts du module de paramètres.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'admin/class-boss-seo-settings-scripts.php';

        /**
         * Le module de services externes.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/settings/class-boss-external-services.php';

        /**
         * Le module d'optimisation de contenu multistep.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'admin/class-boss-seo-content-multistep.php';

        /**
         * Le module frontend.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-boss-seo-frontend.php';

        /**
         * Le module d'analyse technique v2.0.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-boss-technical-analysis-integration.php';

        $this->loader = new Boss_SEO_Loader();

        // Initialiser le module Boss Optimizer
        $this->optimizer = new Boss_Optimizer( $this->plugin_name, $this->version, $this->loader );

        // Initialiser le module de gestion technique
        $this->technical_management = new Boss_Technical_Management( $this->plugin_name, $this->version );

        // Initialiser le module SEO local
        $this->local = new Boss_Local( $this->plugin_name, $this->version );

        // Initialiser le module de paramètres EN PREMIER (requis par les autres modules)
        $this->settings = new Boss_SEO_Settings( $this->plugin_name, $this->version );

        // Initialiser l'IA (requis par le module e-commerce)
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-boss-optimizer-settings.php';
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-boss-optimizer-ai.php';
        $optimizer_settings = new Boss_Optimizer_Settings( $this->plugin_name );
        $this->ai = new Boss_Optimizer_AI( $this->plugin_name, $optimizer_settings );

        // Initialiser le module Schémas structurés (avec les paramètres)
        $this->structured_schemas = new Boss_Structured_Schemas( $this->plugin_name, $this->version, $this->settings );

        // Initialiser le module E-commerce (avec les paramètres et l'IA)
        $this->ecommerce = new Boss_Ecommerce( $this->plugin_name, $this->version, $optimizer_settings, $this->ai );

        // Initialiser le gestionnaire de cache
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-boss-cache-manager.php';
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/api/class-boss-cache-api.php';
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-boss-cache-admin-bar.php';
        $this->cache_manager = Boss_Cache_Manager::get_instance();
        $this->cache_api = new Boss_Cache_API( $this->plugin_name, $this->version );
        $this->cache_api->register_hooks();
        $this->cache_admin_bar = new Boss_Cache_Admin_Bar( $this->plugin_name, $this->version );
        $this->cache_admin_bar->register_hooks();

        // Initialiser le gestionnaire AJAX pour les paramètres
        $this->settings_ajax = new Boss_SEO_Settings_AJAX( $this->plugin_name, $this->version, $this->settings );

        // Initialiser les scripts du module de paramètres
        $this->settings_scripts = new Boss_SEO_Settings_Scripts( $this->plugin_name, $this->version );

        // Initialiser le module de services externes
        $this->external_services = new Boss_External_Services();

        // Initialiser le module d'optimisation de contenu multistep
        $this->content_multistep = new Boss_SEO_Content_Multistep( $this->plugin_name, $this->version );

        // Initialiser le module frontend
        $this->frontend = new Boss_SEO_Frontend( $this->plugin_name, $this->version );

        // Initialiser le module d'analyse technique v2.0
        // Charger le nouveau module d'analyse technique professionnel
        if ( ! class_exists( 'Boss_Technical_Analysis_Module' ) ) {
            require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/modules/technical-analysis/class-boss-technical-analysis-module.php';
        }

        // Charger le module Audit SEO assisté par IA
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-boss-seo-audit-api.php';

        // Initialiser l'API d'audit avec les dépendances nécessaires
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-boss-optimizer-analysis.php';
        $optimizer_analysis = new Boss_Optimizer_Analysis( $this->plugin_name );
        $this->audit_ia_api = new Boss_SEO_Audit_API( $this->plugin_name, $this->version, $optimizer_analysis, $this->ai );
    }

    /**
     * Define the locale for this plugin for internationalization.
     *
     * @since    1.1.0
     * @access   private
     */
    private function set_locale() {
        $plugin_i18n = new Boss_SEO_i18n();

        $this->loader->add_action( 'init', $plugin_i18n, 'load_plugin_textdomain' );
    }

    /**
     * Register all of the hooks related to the admin area functionality
     * of the plugin.
     *
     * @since    1.1.0
     * @access   private
     */
    private function define_admin_hooks() {
        $plugin_admin = new Boss_SEO_Admin( $this->get_plugin_name(), $this->get_version() );

        $this->loader->add_action( 'admin_enqueue_scripts', $plugin_admin, 'enqueue_styles' );
        $this->loader->add_action( 'admin_enqueue_scripts', $plugin_admin, 'enqueue_scripts' );
        $this->loader->add_action( 'admin_menu', $plugin_admin, 'add_plugin_admin_menu' );

        // Enregistrer les hooks pour le module de gestion technique
        $this->technical_management->register_hooks();

        // Enregistrer les routes REST API pour le module de gestion technique
        add_action( 'rest_api_init', array( $this->technical_management, 'register_rest_routes' ), 10 );

        // Ajouter un log pour le débogage
        error_log('Boss_SEO: rest_api_init hook added for technical_management');

        // Enregistrer les hooks pour le module SEO local
        $this->local->register_hooks();

        // Enregistrer les routes REST API pour le module SEO local
        add_action( 'rest_api_init', array( $this->local, 'register_rest_routes' ) );

        // Enregistrer les hooks pour le module Schémas structurés
        $this->structured_schemas->register_hooks();

        // Enregistrer les hooks pour le module E-commerce
        $this->ecommerce->register_hooks();

        // Enregistrer les routes REST API pour le module E-commerce
        add_action( 'rest_api_init', array( $this->ecommerce, 'register_rest_routes' ) );

        // Enregistrer les hooks pour le module de paramètres
        $this->settings->register_hooks();

        // Enregistrer les hooks pour le gestionnaire AJAX des paramètres
        $this->settings_ajax->register_hooks();

        // Enregistrer les hooks pour les scripts du module de paramètres
        $this->settings_scripts->register_hooks();

        // Enregistrer les hooks pour le module de services externes
        $this->external_services->register_hooks();

        // Enregistrer les routes REST API pour le module de services externes
        add_action( 'rest_api_init', array( $this->external_services, 'register_rest_routes' ) );

        // Enregistrer les hooks pour le module d'optimisation de contenu multistep
        $this->loader->add_action( 'admin_enqueue_scripts', $this->content_multistep, 'enqueue_scripts' );

        // Enregistrer les hooks pour le module frontend
        $this->frontend->register_hooks();

        // Initialiser le nouveau module d'analyse technique professionnel
        $this->technical_analysis_module = new Boss_Technical_Analysis_Module( $this->plugin_name, $this->version );
        $this->technical_analysis_module->register_hooks();

        // Enregistrer les routes REST API pour le module Audit SEO assisté par IA
        add_action( 'rest_api_init', array( $this->audit_ia_api, 'register_routes' ) );
    }

    // Méthode register_technical_analysis_v2_routes supprimée - Gérée par le module maintenant

    /**
     * Run the loader to execute all of the hooks with WordPress.
     *
     * @since    1.1.0
     */
    public function run() {
        $this->loader->run();
    }

    /**
     * The name of the plugin used to uniquely identify it within the context of
     * WordPress and to define internationalization functionality.
     *
     * @since     1.1.0
     * @return    string    The name of the plugin.
     */
    public function get_plugin_name() {
        return $this->plugin_name;
    }

    /**
     * The reference to the class that orchestrates the hooks with the plugin.
     *
     * @since     1.1.0
     * @return    Boss_SEO_Loader    Orchestrates the hooks of the plugin.
     */
    public function get_loader() {
        return $this->loader;
    }

    /**
     * Retrieve the version number of the plugin.
     *
     * @since     1.1.0
     * @return    string    The version number of the plugin.
     */
    public function get_version() {
        return $this->version;
    }
}
