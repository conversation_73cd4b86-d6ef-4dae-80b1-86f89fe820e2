/**
 * Composant pour la gestion des liens cassés
 */
import { useState, useEffect } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  Card,
  CardBody,
  CardHeader,
  Button,
  Spinner,
  Notice,
  TabPanel,
  TextControl,
  SelectControl,
  ToggleControl,
  Dashicon
} from '@wordpress/components';

// Services
import BrokenLinksService from '../../services/BrokenLinksService';

/**
 * Composant pour afficher un lien cassé
 */
const BrokenLinkItem = ({ link, onRecheck, onIgnore, onFix }) => {
  const [isRechecking, setIsRechecking] = useState(false);
  const [isIgnoring, setIsIgnoring] = useState(false);

  const handleRecheck = async () => {
    setIsRechecking(true);
    try {
      await onRecheck(link.id);
    } finally {
      setIsRechecking(false);
    }
  };

  const handleIgnore = async () => {
    setIsIgnoring(true);
    try {
      await onIgnore(link.id);
    } finally {
      setIsIgnoring(false);
    }
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      404: { color: 'red', text: __('404 - Non trouvé', 'boss-seo') },
      403: { color: 'orange', text: __('403 - Accès refusé', 'boss-seo') },
      500: { color: 'red', text: __('500 - Erreur serveur', 'boss-seo') },
      timeout: { color: 'yellow', text: __('Timeout', 'boss-seo') },
      dns: { color: 'red', text: __('Erreur DNS', 'boss-seo') },
      ssl: { color: 'orange', text: __('Erreur SSL', 'boss-seo') },
      other: { color: 'gray', text: __('Autre erreur', 'boss-seo') }
    };

    const config = statusConfig[status] || statusConfig.other;

    return (
      <span className={`boss-inline-flex boss-items-center boss-px-2 boss-py-1 boss-rounded-full boss-text-xs boss-font-medium boss-bg-${config.color}-100 boss-text-${config.color}-800 boss-border boss-border-${config.color}-200`}>
        {config.text}
      </span>
    );
  };

  return (
    <Card className="boss-mb-4 boss-border-l-4 boss-border-l-red-500">
      <CardBody>
        <div className="boss-flex boss-justify-between boss-items-start boss-mb-4">
          <div className="boss-flex-1">
            <div className="boss-flex boss-items-center boss-gap-2 boss-mb-2">
              <Dashicon icon="admin-links" className="boss-text-red-500" />
              <h4 className="boss-font-semibold boss-text-boss-dark">
                {link.url}
              </h4>
              {getStatusBadge(link.status)}
            </div>

            <div className="boss-text-sm boss-text-boss-gray boss-mb-2">
              <strong>{__('Trouvé dans:', 'boss-seo')}</strong> {link.source_title}
              <br />
              <strong>{__('URL source:', 'boss-seo')}</strong>
              <a href={link.source_url} target="_blank" rel="noopener noreferrer" className="boss-text-blue-600 boss-underline">
                {link.source_url}
              </a>
            </div>

            <div className="boss-text-sm boss-text-boss-gray">
              <strong>{__('Type:', 'boss-seo')}</strong> {link.link_type === 'internal' ? __('Interne', 'boss-seo') : __('Externe', 'boss-seo')}
              {link.anchor_text && (
                <>
                  <br />
                  <strong>{__('Texte d\'ancrage:', 'boss-seo')}</strong> "{link.anchor_text}"
                </>
              )}
              <br />
              <strong>{__('Dernière vérification:', 'boss-seo')}</strong> {new Date(link.last_checked).toLocaleString()}
            </div>
          </div>

          <div className="boss-flex boss-gap-2 boss-ml-4">
            <Button
              isSecondary
              isSmall
              onClick={handleRecheck}
              disabled={isRechecking}
              isBusy={isRechecking}
            >
              {__('Revérifier', 'boss-seo')}
            </Button>

            <Button
              isSecondary
              isSmall
              onClick={handleIgnore}
              disabled={isIgnoring}
              isBusy={isIgnoring}
            >
              {__('Ignorer', 'boss-seo')}
            </Button>

            <Button
              isPrimary
              isSmall
              onClick={() => onFix(link)}
            >
              {__('Corriger', 'boss-seo')}
            </Button>
          </div>
        </div>

        {link.error_message && (
          <div className="boss-p-3 boss-bg-red-50 boss-border boss-border-red-200 boss-rounded boss-text-sm">
            <strong>{__('Erreur:', 'boss-seo')}</strong> {link.error_message}
          </div>
        )}
      </CardBody>
    </Card>
  );
};

/**
 * Composant pour les statistiques des liens cassés
 */
const BrokenLinksStats = ({ stats }) => {
  return (
    <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-4 boss-gap-4 boss-mb-6">
      <Card>
        <CardBody className="boss-text-center">
          <div className="boss-text-2xl boss-font-bold boss-text-red-600 boss-mb-1">
            {stats.total_broken || 0}
          </div>
          <div className="boss-text-sm boss-text-boss-gray">
            {__('Liens cassés', 'boss-seo')}
          </div>
        </CardBody>
      </Card>

      <Card>
        <CardBody className="boss-text-center">
          <div className="boss-text-2xl boss-font-bold boss-text-orange-600 boss-mb-1">
            {stats.internal_broken || 0}
          </div>
          <div className="boss-text-sm boss-text-boss-gray">
            {__('Liens internes', 'boss-seo')}
          </div>
        </CardBody>
      </Card>

      <Card>
        <CardBody className="boss-text-center">
          <div className="boss-text-2xl boss-font-bold boss-text-blue-600 boss-mb-1">
            {stats.external_broken || 0}
          </div>
          <div className="boss-text-sm boss-text-boss-gray">
            {__('Liens externes', 'boss-seo')}
          </div>
        </CardBody>
      </Card>

      <Card>
        <CardBody className="boss-text-center">
          <div className="boss-text-2xl boss-font-bold boss-text-green-600 boss-mb-1">
            {stats.ignored || 0}
          </div>
          <div className="boss-text-sm boss-text-boss-gray">
            {__('Ignorés', 'boss-seo')}
          </div>
        </CardBody>
      </Card>
    </div>
  );
};

/**
 * Composant principal pour la gestion des liens cassés
 */
const BrokenLinks = () => {
  // États
  const [brokenLinks, setBrokenLinks] = useState([]);
  const [stats, setStats] = useState({});
  const [isLoading, setIsLoading] = useState(true);
  const [isScanning, setIsScanning] = useState(false);
  const [error, setError] = useState('');
  const [filters, setFilters] = useState({
    type: 'all', // all, internal, external
    status: 'all', // all, 404, 403, 500, timeout, dns, ssl
    search: ''
  });
  const [settings, setSettings] = useState({
    auto_scan: true,
    scan_frequency: 'weekly',
    check_external: true,
    timeout: 30,
    user_agent: 'Boss SEO Link Checker'
  });

  // Charger les données initiales
  useEffect(() => {
    loadBrokenLinks();
    loadStats();
    loadSettings();
  }, []);

  // Charger les liens cassés
  const loadBrokenLinks = async () => {
    try {
      setIsLoading(true);
      const response = await BrokenLinksService.getBrokenLinks(filters);
      setBrokenLinks(response.links || []);
    } catch (err) {
      setError(__('Erreur lors du chargement des liens cassés.', 'boss-seo'));
      console.error('Erreur:', err);
    } finally {
      setIsLoading(false);
    }
  };

  // Charger les statistiques
  const loadStats = async () => {
    try {
      const response = await BrokenLinksService.getStats();
      setStats(response.stats || {});
    } catch (err) {
      console.error('Erreur lors du chargement des statistiques:', err);
    }
  };

  // Charger les paramètres
  const loadSettings = async () => {
    try {
      const response = await BrokenLinksService.getSettings();
      setSettings(response.settings || settings);
    } catch (err) {
      console.error('Erreur lors du chargement des paramètres:', err);
    }
  };

  // Lancer un scan
  const startScan = async () => {
    try {
      setIsScanning(true);
      setError('');
      await BrokenLinksService.startScan();

      // Recharger les données après le scan
      setTimeout(() => {
        loadBrokenLinks();
        loadStats();
        setIsScanning(false);
      }, 2000);
    } catch (err) {
      setError(__('Erreur lors du lancement du scan.', 'boss-seo'));
      setIsScanning(false);
      console.error('Erreur:', err);
    }
  };

  // Revérifier un lien
  const recheckLink = async (linkId) => {
    try {
      await BrokenLinksService.recheckLink(linkId);
      loadBrokenLinks();
      loadStats();
    } catch (err) {
      setError(__('Erreur lors de la revérification du lien.', 'boss-seo'));
      console.error('Erreur:', err);
    }
  };

  // Ignorer un lien
  const ignoreLink = async (linkId) => {
    try {
      await BrokenLinksService.ignoreLink(linkId);
      loadBrokenLinks();
      loadStats();
    } catch (err) {
      setError(__('Erreur lors de l\'ajout du lien à la liste d\'ignorés.', 'boss-seo'));
      console.error('Erreur:', err);
    }
  };

  // Corriger un lien (ouvrir l'éditeur)
  const fixLink = (link) => {
    // Ouvrir l'éditeur WordPress avec la page/post contenant le lien cassé
    const editUrl = `${window.location.origin}/wp-admin/post.php?post=${link.source_post_id}&action=edit`;
    window.open(editUrl, '_blank');
  };

  // Sauvegarder les paramètres
  const saveSettings = async () => {
    try {
      await BrokenLinksService.saveSettings(settings);
      setError('');
    } catch (err) {
      setError(__('Erreur lors de la sauvegarde des paramètres.', 'boss-seo'));
      console.error('Erreur:', err);
    }
  };

  // Filtrer les liens
  const filteredLinks = brokenLinks.filter(link => {
    if (filters.type !== 'all' && link.link_type !== filters.type) return false;
    if (filters.status !== 'all' && link.status !== filters.status) return false;
    if (filters.search && !link.url.toLowerCase().includes(filters.search.toLowerCase())) return false;
    return true;
  });

  return (
    <div className="boss-space-y-6">
      {error && (
        <Notice status="error" isDismissible onRemove={() => setError('')}>
          {error}
        </Notice>
      )}

      <BrokenLinksStats stats={stats} />

      <TabPanel
        className="boss-mb-6"
        activeClass="boss-bg-white boss-border-t boss-border-l boss-border-r boss-border-gray-200 boss-rounded-t-lg"
        tabs={[
          {
            name: 'links',
            title: __('Liens cassés', 'boss-seo'),
            className: 'boss-font-medium boss-px-4 boss-py-2'
          },
          {
            name: 'settings',
            title: __('Paramètres', 'boss-seo'),
            className: 'boss-font-medium boss-px-4 boss-py-2'
          }
        ]}
      >
        {(tab) => {
          if (tab.name === 'links') {
            return (
              <Card>
                <CardHeader>
                  <div className="boss-flex boss-justify-between boss-items-center">
                    <h3 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                      <Dashicon icon="admin-links" className="boss-mr-2" />
                      {__('Liens cassés détectés', 'boss-seo')}
                    </h3>

                    <Button
                      isPrimary
                      onClick={startScan}
                      disabled={isScanning}
                      isBusy={isScanning}
                    >
                      {isScanning ? __('Scan en cours...', 'boss-seo') : __('Lancer un scan', 'boss-seo')}
                    </Button>
                  </div>
                </CardHeader>

                <CardBody>
                  {/* Filtres */}
                  <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-3 boss-gap-4 boss-mb-6">
                    <TextControl
                      label={__('Rechercher', 'boss-seo')}
                      value={filters.search}
                      onChange={(value) => setFilters({ ...filters, search: value })}
                      placeholder={__('Rechercher par URL...', 'boss-seo')}
                    />

                    <SelectControl
                      label={__('Type de lien', 'boss-seo')}
                      value={filters.type}
                      options={[
                        { label: __('Tous', 'boss-seo'), value: 'all' },
                        { label: __('Internes', 'boss-seo'), value: 'internal' },
                        { label: __('Externes', 'boss-seo'), value: 'external' }
                      ]}
                      onChange={(value) => setFilters({ ...filters, type: value })}
                    />

                    <SelectControl
                      label={__('Statut d\'erreur', 'boss-seo')}
                      value={filters.status}
                      options={[
                        { label: __('Tous', 'boss-seo'), value: 'all' },
                        { label: __('404 - Non trouvé', 'boss-seo'), value: '404' },
                        { label: __('403 - Accès refusé', 'boss-seo'), value: '403' },
                        { label: __('500 - Erreur serveur', 'boss-seo'), value: '500' },
                        { label: __('Timeout', 'boss-seo'), value: 'timeout' },
                        { label: __('Erreur DNS', 'boss-seo'), value: 'dns' },
                        { label: __('Erreur SSL', 'boss-seo'), value: 'ssl' }
                      ]}
                      onChange={(value) => setFilters({ ...filters, status: value })}
                    />
                  </div>

                  {/* Liste des liens cassés */}
                  {isLoading ? (
                    <div className="boss-text-center boss-py-12">
                      <Spinner />
                      <p className="boss-mt-4 boss-text-boss-gray">
                        {__('Chargement des liens cassés...', 'boss-seo')}
                      </p>
                    </div>
                  ) : filteredLinks.length > 0 ? (
                    <div>
                      <div className="boss-mb-4 boss-text-sm boss-text-boss-gray">
                        {filteredLinks.length} {__('lien(s) cassé(s) trouvé(s)', 'boss-seo')}
                      </div>

                      {filteredLinks.map((link) => (
                        <BrokenLinkItem
                          key={link.id}
                          link={link}
                          onRecheck={recheckLink}
                          onIgnore={ignoreLink}
                          onFix={fixLink}
                        />
                      ))}
                    </div>
                  ) : (
                    <div className="boss-text-center boss-py-12">
                      <Dashicon icon="yes-alt" className="boss-text-6xl boss-text-green-500 boss-mb-4" />
                      <h3 className="boss-text-lg boss-font-semibold boss-text-boss-dark boss-mb-2">
                        {__('Aucun lien cassé détecté !', 'boss-seo')}
                      </h3>
                      <p className="boss-text-boss-gray">
                        {__('Tous vos liens semblent fonctionner correctement. Lancez un nouveau scan pour vérifier.', 'boss-seo')}
                      </p>
                    </div>
                  )}
                </CardBody>
              </Card>
            );
          }

          // Onglet Paramètres
          return (
            <Card>
              <CardHeader>
                <h3 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                  <Dashicon icon="admin-settings" className="boss-mr-2" />
                  {__('Paramètres de détection', 'boss-seo')}
                </h3>
              </CardHeader>

              <CardBody>
                <div className="boss-space-y-6">
                  <ToggleControl
                    label={__('Scan automatique', 'boss-seo')}
                    help={__('Lancer automatiquement un scan des liens cassés selon la fréquence définie.', 'boss-seo')}
                    checked={settings.auto_scan}
                    onChange={(value) => setSettings({ ...settings, auto_scan: value })}
                  />

                  <SelectControl
                    label={__('Fréquence de scan', 'boss-seo')}
                    value={settings.scan_frequency}
                    options={[
                      { label: __('Quotidien', 'boss-seo'), value: 'daily' },
                      { label: __('Hebdomadaire', 'boss-seo'), value: 'weekly' },
                      { label: __('Mensuel', 'boss-seo'), value: 'monthly' }
                    ]}
                    onChange={(value) => setSettings({ ...settings, scan_frequency: value })}
                    disabled={!settings.auto_scan}
                  />

                  <ToggleControl
                    label={__('Vérifier les liens externes', 'boss-seo')}
                    help={__('Inclure les liens externes dans la vérification (peut ralentir le scan).', 'boss-seo')}
                    checked={settings.check_external}
                    onChange={(value) => setSettings({ ...settings, check_external: value })}
                  />

                  <TextControl
                    label={__('Timeout (secondes)', 'boss-seo')}
                    help={__('Délai d\'attente maximum pour vérifier un lien.', 'boss-seo')}
                    type="number"
                    value={settings.timeout}
                    onChange={(value) => setSettings({ ...settings, timeout: parseInt(value) || 30 })}
                    min="5"
                    max="120"
                  />

                  <TextControl
                    label={__('User Agent', 'boss-seo')}
                    help={__('Identifiant utilisé lors de la vérification des liens.', 'boss-seo')}
                    value={settings.user_agent}
                    onChange={(value) => setSettings({ ...settings, user_agent: value })}
                  />

                  <Button isPrimary onClick={saveSettings}>
                    {__('Sauvegarder les paramètres', 'boss-seo')}
                  </Button>
                </div>
              </CardBody>
            </Card>
          );
        }}
      </TabPanel>
    </div>
  );
};

export default BrokenLinks;
