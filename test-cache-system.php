<?php
/**
 * Script de test pour le système de cache Boss SEO.
 * 
 * Ce script teste toutes les fonctionnalités du nouveau système de cache.
 * 
 * Usage: wp eval-file test-cache-system.php
 */

// Vérifier que nous sommes dans WordPress
if ( ! defined( 'ABSPATH' ) ) {
    die( 'Ce script doit être exécuté dans WordPress.' );
}

echo "🧪 DÉBUT DES TESTS DU SYSTÈME DE CACHE BOSS SEO\n";
echo "================================================\n\n";

$tests_passed = 0;
$tests_failed = 0;

/**
 * Fonction helper pour afficher les résultats des tests
 */
function test_result( $test_name, $success, $message = '' ) {
    global $tests_passed, $tests_failed;
    
    if ( $success ) {
        echo "✅ {$test_name}\n";
        if ( $message ) echo "   → {$message}\n";
        $tests_passed++;
    } else {
        echo "❌ {$test_name}\n";
        if ( $message ) echo "   → {$message}\n";
        $tests_failed++;
    }
    echo "\n";
}

// Test 1: Vérifier que les classes existent
echo "📋 Test 1: Vérification des classes\n";
echo "-----------------------------------\n";

$cache_manager_file = plugin_dir_path( __FILE__ ) . 'includes/class-boss-cache-manager.php';
$cache_api_file = plugin_dir_path( __FILE__ ) . 'includes/api/class-boss-cache-api.php';
$cache_admin_bar_file = plugin_dir_path( __FILE__ ) . 'includes/class-boss-cache-admin-bar.php';

test_result(
    'Fichier Boss_Cache_Manager existe',
    file_exists( $cache_manager_file ),
    $cache_manager_file
);

test_result(
    'Fichier Boss_Cache_API existe',
    file_exists( $cache_api_file ),
    $cache_api_file
);

test_result(
    'Fichier Boss_Cache_Admin_Bar existe',
    file_exists( $cache_admin_bar_file ),
    $cache_admin_bar_file
);

// Charger les classes
if ( file_exists( $cache_manager_file ) ) {
    require_once $cache_manager_file;
}
if ( file_exists( $cache_api_file ) ) {
    require_once $cache_api_file;
}
if ( file_exists( $cache_admin_bar_file ) ) {
    require_once $cache_admin_bar_file;
}

test_result(
    'Classe Boss_Cache_Manager chargée',
    class_exists( 'Boss_Cache_Manager' )
);

test_result(
    'Classe Boss_Cache_API chargée',
    class_exists( 'Boss_Cache_API' )
);

test_result(
    'Classe Boss_Cache_Admin_Bar chargée',
    class_exists( 'Boss_Cache_Admin_Bar' )
);

// Test 2: Tester le gestionnaire de cache
echo "🗂️ Test 2: Gestionnaire de cache\n";
echo "--------------------------------\n";

if ( class_exists( 'Boss_Cache_Manager' ) ) {
    try {
        $cache_manager = Boss_Cache_Manager::get_instance();
        
        test_result(
            'Instance du gestionnaire de cache créée',
            $cache_manager instanceof Boss_Cache_Manager
        );
        
        // Tester la version des assets
        $assets_version = $cache_manager->get_assets_version();
        test_result(
            'Version des assets récupérée',
            ! empty( $assets_version ),
            "Version: {$assets_version}"
        );
        
        // Tester le forçage du rechargement
        $old_version = $assets_version;
        $refresh_result = $cache_manager->force_assets_refresh();
        $new_version = $cache_manager->get_assets_version();
        
        test_result(
            'Forçage du rechargement des assets',
            $refresh_result && $new_version !== $old_version,
            "Ancienne: {$old_version}, Nouvelle: {$new_version}"
        );
        
        // Tester les statistiques
        $stats = $cache_manager->get_cache_stats();
        test_result(
            'Statistiques du cache récupérées',
            is_array( $stats ) && isset( $stats['assets_version'] ),
            "Transients: {$stats['transients_count']}, Debug: " . ( $stats['debug_mode'] ? 'Oui' : 'Non' )
        );
        
    } catch ( Exception $e ) {
        test_result(
            'Gestionnaire de cache',
            false,
            'Erreur: ' . $e->getMessage()
        );
    }
} else {
    test_result(
        'Gestionnaire de cache',
        false,
        'Classe Boss_Cache_Manager non disponible'
    );
}

// Test 3: Tester l'API REST
echo "🌐 Test 3: API REST du cache\n";
echo "----------------------------\n";

if ( class_exists( 'Boss_Cache_API' ) ) {
    try {
        $cache_api = new Boss_Cache_API( 'boss-seo', '1.1.0' );
        
        test_result(
            'Instance de l\'API cache créée',
            $cache_api instanceof Boss_Cache_API
        );
        
        // Vérifier que les routes sont enregistrées
        $cache_api->register_routes();
        
        // Simuler une requête pour les statistiques
        $request = new WP_REST_Request( 'GET', '/boss-seo/v1/cache/stats' );
        
        // Vérifier les permissions (doit échouer si pas admin)
        $permission_check = $cache_api->check_permissions( $request );
        test_result(
            'Vérification des permissions',
            is_bool( $permission_check ),
            $permission_check ? 'Autorisé' : 'Non autorisé (normal si pas admin)'
        );
        
        // Tester la validation des modules
        $valid_module = $cache_api->validate_module_name( 'optimizer', $request, 'module' );
        $invalid_module = $cache_api->validate_module_name( 'invalid_module', $request, 'module' );
        
        test_result(
            'Validation des noms de modules',
            $valid_module === true && $invalid_module === false,
            'optimizer: valide, invalid_module: invalide'
        );
        
    } catch ( Exception $e ) {
        test_result(
            'API REST du cache',
            false,
            'Erreur: ' . $e->getMessage()
        );
    }
} else {
    test_result(
        'API REST du cache',
        false,
        'Classe Boss_Cache_API non disponible'
    );
}

// Test 4: Tester la barre d'administration
echo "🔧 Test 4: Barre d'administration\n";
echo "---------------------------------\n";

if ( class_exists( 'Boss_Cache_Admin_Bar' ) ) {
    try {
        $cache_admin_bar = new Boss_Cache_Admin_Bar( 'boss-seo', '1.1.0' );
        
        test_result(
            'Instance de la barre d\'administration créée',
            $cache_admin_bar instanceof Boss_Cache_Admin_Bar
        );
        
        // Les hooks ne peuvent pas être testés facilement ici
        test_result(
            'Méthodes de la barre d\'administration',
            method_exists( $cache_admin_bar, 'register_hooks' ) &&
            method_exists( $cache_admin_bar, 'add_cache_menu' ) &&
            method_exists( $cache_admin_bar, 'handle_cache_actions' ),
            'Toutes les méthodes requises sont présentes'
        );
        
    } catch ( Exception $e ) {
        test_result(
            'Barre d\'administration',
            false,
            'Erreur: ' . $e->getMessage()
        );
    }
} else {
    test_result(
        'Barre d\'administration',
        false,
        'Classe Boss_Cache_Admin_Bar non disponible'
    );
}

// Test 5: Tester l'intégration avec les modules existants
echo "🔗 Test 5: Intégration avec les modules\n";
echo "---------------------------------------\n";

// Vérifier si les classes de cache des modules existent
$module_caches = array(
    'Boss_Optimizer_Cache' => 'includes/class-boss-optimizer-cache.php',
    'Boss_Ecommerce_Cache' => 'includes/ecommerce/class-boss-ecommerce-cache.php'
);

foreach ( $module_caches as $class_name => $file_path ) {
    $full_path = plugin_dir_path( __FILE__ ) . $file_path;
    $file_exists = file_exists( $full_path );
    
    if ( $file_exists ) {
        require_once $full_path;
        $class_exists = class_exists( $class_name );
        test_result(
            "Module de cache {$class_name}",
            $class_exists,
            $class_exists ? 'Classe chargée avec succès' : 'Classe non trouvée'
        );
    } else {
        test_result(
            "Module de cache {$class_name}",
            false,
            "Fichier non trouvé: {$file_path}"
        );
    }
}

// Test 6: Test de performance
echo "⚡ Test 6: Performance du cache\n";
echo "------------------------------\n";

if ( class_exists( 'Boss_Cache_Manager' ) ) {
    $cache_manager = Boss_Cache_Manager::get_instance();
    
    // Mesurer le temps de récupération de la version
    $start_time = microtime( true );
    for ( $i = 0; $i < 100; $i++ ) {
        $cache_manager->get_assets_version();
    }
    $end_time = microtime( true );
    $execution_time = ( $end_time - $start_time ) * 1000; // en millisecondes
    
    test_result(
        'Performance - 100 appels get_assets_version()',
        $execution_time < 100, // Moins de 100ms pour 100 appels
        sprintf( '%.2f ms (%.4f ms par appel)', $execution_time, $execution_time / 100 )
    );
}

// Résumé des tests
echo "📊 RÉSUMÉ DES TESTS\n";
echo "==================\n";
echo "✅ Tests réussis: {$tests_passed}\n";
echo "❌ Tests échoués: {$tests_failed}\n";
echo "📈 Taux de réussite: " . round( ( $tests_passed / ( $tests_passed + $tests_failed ) ) * 100, 1 ) . "%\n\n";

if ( $tests_failed === 0 ) {
    echo "🎉 TOUS LES TESTS SONT PASSÉS !\n";
    echo "Le système de cache Boss SEO est prêt à être utilisé.\n\n";
    
    echo "🚀 PROCHAINES ÉTAPES:\n";
    echo "1. Testez le bouton de cache dans la barre d'administration WordPress\n";
    echo "2. Vérifiez que les assets se rechargent correctement\n";
    echo "3. Testez les endpoints API REST avec un client REST\n";
    echo "4. Vérifiez les logs d'erreur pour d'éventuels problèmes\n";
} else {
    echo "⚠️  CERTAINS TESTS ONT ÉCHOUÉ\n";
    echo "Veuillez corriger les problèmes avant de continuer.\n";
}

echo "\n🏁 FIN DES TESTS\n";
?>
