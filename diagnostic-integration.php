<?php
/**
 * Script de diagnostic pour vérifier l'intégration complète du module technique v2
 */

echo "🔍 DIAGNOSTIC INTÉGRATION MODULE TECHNIQUE v2.0\n";
echo "===============================================\n\n";

$checks_passed = 0;
$checks_failed = 0;

function diagnostic_check($check_name, $success, $message = '', $fix_suggestion = '') {
    global $checks_passed, $checks_failed;
    
    if ($success) {
        echo "✅ {$check_name}\n";
        if ($message) echo "   → {$message}\n";
        $checks_passed++;
    } else {
        echo "❌ {$check_name}\n";
        if ($message) echo "   → {$message}\n";
        if ($fix_suggestion) echo "   💡 {$fix_suggestion}\n";
        $checks_failed++;
    }
    echo "\n";
}

// Check 1: Fichiers principaux
echo "📁 Check 1: Fichiers Principaux\n";
echo "-------------------------------\n";

$main_files = [
    'includes/class-boss-seo.php' => 'Fichier principal du plugin',
    'includes/class-boss-technical-analysis-integration.php' => 'Intégration module technique v2',
    'includes/class-boss-technical-analyzer-v2.php' => 'Analyseur technique v2',
    'includes/class-boss-ai-service.php' => 'Service IA',
    'includes/class-boss-ai-suggestions-generator.php' => 'Générateur suggestions IA',
    'admin/partials/boss-seo-admin-technical.php' => 'Interface admin technique',
    'src/pages/TechnicalAnalysis.js' => 'Composant React v2',
    'src/pages/TechnicalAnalysis.js.backup' => 'Ancien composant (sauvegarde)'
];

foreach ($main_files as $file => $description) {
    $exists = file_exists($file);
    diagnostic_check($description, $exists, $exists ? "Fichier présent: {$file}" : "Fichier manquant: {$file}");
}

// Check 2: Intégration dans le fichier principal
echo "🔗 Check 2: Intégration dans le Système Principal\n";
echo "-------------------------------------------------\n";

$boss_seo_content = file_get_contents('includes/class-boss-seo.php');

$integration_checks = [
    'protected $technical_analysis_v2;' => 'Propriété technical_analysis_v2 déclarée',
    'class-boss-technical-analysis-integration.php' => 'Fichier d\'intégration requis',
    'new Boss_Technical_Analysis_Integration' => 'Instance d\'intégration créée',
    '$this->technical_analysis_v2->register_hooks()' => 'Hooks d\'intégration enregistrés'
];

foreach ($integration_checks as $pattern => $description) {
    $found = strpos($boss_seo_content, $pattern) !== false;
    diagnostic_check($description, $found, $found ? "Trouvé dans class-boss-seo.php" : "Non trouvé dans class-boss-seo.php");
}

// Check 3: Structure des routes API
echo "🌐 Check 3: Structure des Routes API\n";
echo "------------------------------------\n";

$analyzer_content = file_get_contents('includes/class-boss-technical-analyzer-v2.php');

$api_checks = [
    'register_rest_route' => 'Enregistrement des routes REST',
    'boss-seo/v2' => 'Namespace API v2',
    '/technical/pages' => 'Route pages disponibles',
    '/technical/analyze' => 'Route analyse de page',
    '/technical/history' => 'Route historique',
    '/technical/ai-suggestions' => 'Route suggestions IA'
];

foreach ($api_checks as $pattern => $description) {
    $found = strpos($analyzer_content, $pattern) !== false;
    diagnostic_check($description, $found, $found ? "Trouvé dans l'analyseur v2" : "Non trouvé dans l'analyseur v2");
}

// Check 4: Interface utilisateur
echo "🎨 Check 4: Interface Utilisateur\n";
echo "---------------------------------\n";

$admin_content = file_get_contents('admin/partials/boss-seo-admin-technical.php');

$ui_checks = [
    'boss-seo-technical-analysis-v2-app' => 'Container React v2',
    'Boss_Technical_Analysis_Integration' => 'Chargement de l\'intégration',
    'register_hooks()' => 'Enregistrement des hooks',
    'wp_enqueue_script' => 'Chargement des scripts',
    'bossSeoTechnicalV2' => 'Configuration JavaScript'
];

foreach ($ui_checks as $pattern => $description) {
    $found = strpos($admin_content, $pattern) !== false;
    diagnostic_check($description, $found, $found ? "Trouvé dans l'interface admin" : "Non trouvé dans l'interface admin");
}

// Check 5: Composant React
echo "⚛️ Check 5: Composant React\n";
echo "---------------------------\n";

if (file_exists('src/pages/TechnicalAnalysis.js')) {
    $react_content = file_get_contents('src/pages/TechnicalAnalysis.js');
    
    $react_checks = [
        'TechnicalAnalysisV2' => 'Composant v2 (nom correct)',
        '/boss-seo/v2/technical/pages' => 'Appel API pages',
        '/boss-seo/v2/technical/analyze' => 'Appel API analyse',
        'boss-seo-technical-analysis-v2-app' => 'Container ID correct',
        'useState' => 'Hooks React utilisés',
        'useEffect' => 'Effects React utilisés'
    ];
    
    foreach ($react_checks as $pattern => $description) {
        $found = strpos($react_content, $pattern) !== false;
        diagnostic_check($description, $found, $found ? "Trouvé dans le composant React" : "Non trouvé dans le composant React");
    }
} else {
    diagnostic_check('Composant React v2', false, 'Fichier TechnicalAnalysis.js manquant');
}

// Check 6: Configuration et dépendances
echo "⚙️ Check 6: Configuration et Dépendances\n";
echo "----------------------------------------\n";

$config_checks = [
    'includes/class-boss-optimizer-settings.php' => 'Classe paramètres existante',
    'includes/class-boss-optimizer-ai.php' => 'Service IA existant',
    'includes/class-boss-pagespeed-manager.php' => 'Gestionnaire PageSpeed'
];

foreach ($config_checks as $file => $description) {
    $exists = file_exists($file);
    diagnostic_check($description, $exists, $exists ? "Dépendance disponible" : "Dépendance manquante", 
        !$exists ? "Vérifiez que le fichier {$file} existe" : '');
}

// Check 7: Syntaxe PHP
echo "🔧 Check 7: Syntaxe PHP\n";
echo "-----------------------\n";

$php_files_to_check = [
    'includes/class-boss-seo.php',
    'includes/class-boss-technical-analysis-integration.php',
    'includes/class-boss-technical-analyzer-v2.php',
    'includes/class-boss-ai-service.php',
    'admin/partials/boss-seo-admin-technical.php'
];

foreach ($php_files_to_check as $file) {
    if (file_exists($file)) {
        $output = shell_exec("php -l {$file} 2>&1");
        $syntax_ok = strpos($output, 'No syntax errors') !== false;
        diagnostic_check("Syntaxe {$file}", $syntax_ok, 
            $syntax_ok ? "Syntaxe PHP valide" : "Erreur de syntaxe détectée",
            !$syntax_ok ? "Corrigez les erreurs de syntaxe avant de continuer" : '');
    }
}

// Résumé et recommandations
echo "📊 RÉSUMÉ DU DIAGNOSTIC\n";
echo "=======================\n";
echo "✅ Checks réussis: {$checks_passed}\n";
echo "❌ Checks échoués: {$checks_failed}\n";

$success_rate = $checks_passed / ($checks_passed + $checks_failed) * 100;
echo "📈 Taux de réussite: " . round($success_rate, 1) . "%\n\n";

if ($checks_failed === 0) {
    echo "🎉 INTÉGRATION COMPLÈTE ET FONCTIONNELLE !\n";
    echo "==========================================\n";
    echo "✅ Tous les fichiers sont présents\n";
    echo "✅ Intégration dans le système principal réussie\n";
    echo "✅ Routes API v2 correctement définies\n";
    echo "✅ Interface utilisateur mise à jour\n";
    echo "✅ Composant React v2 opérationnel\n";
    echo "✅ Dépendances disponibles\n";
    echo "✅ Syntaxe PHP valide\n\n";
    
    echo "🚀 PRÊT POUR LA PRODUCTION !\n";
    echo "Le module d'analyse technique v2.0 est complètement intégré\n";
    echo "et devrait maintenant fonctionner sans erreur 404.\n\n";
    
    echo "🔗 ROUTES API DISPONIBLES:\n";
    echo "• GET  /wp-json/boss-seo/v2/technical/pages\n";
    echo "• POST /wp-json/boss-seo/v2/technical/analyze\n";
    echo "• GET  /wp-json/boss-seo/v2/technical/history\n";
    echo "• POST /wp-json/boss-seo/v2/technical/ai-suggestions\n\n";
    
} else if ($checks_failed <= 3) {
    echo "⚠️ INTÉGRATION PRESQUE COMPLÈTE\n";
    echo "===============================\n";
    echo "La plupart des éléments sont en place.\n";
    echo "Corrigez les problèmes identifiés ci-dessus.\n\n";
    
} else {
    echo "🔧 INTÉGRATION INCOMPLÈTE\n";
    echo "=========================\n";
    echo "Plusieurs éléments nécessitent une attention.\n";
    echo "Suivez les suggestions de correction ci-dessus.\n\n";
}

echo "📞 ÉTAPES DE VÉRIFICATION:\n";
echo "1. Uploadez tous les fichiers modifiés sur le serveur\n";
echo "2. Accédez à l'interface d'analyse technique\n";
echo "3. Vérifiez que les routes API ne retournent plus 404\n";
echo "4. Testez une analyse complète\n";
echo "5. Contrôlez les logs d'erreur WordPress\n\n";

echo "🏁 FIN DU DIAGNOSTIC\n";
?>
