<?php
/**
 * Script pour créer manuellement les tables manquantes du plugin Boss SEO.
 */

// Charger WordPress
require_once( dirname( __FILE__ ) . '/wp-load.php' );

// Vérifier si l'utilisateur est connecté et est administrateur
if ( ! current_user_can( 'manage_options' ) ) {
    die( 'Vous devez être connecté en tant qu\'administrateur pour exécuter ce script.' );
}

// Charger les classes nécessaires
require_once plugin_dir_path( __FILE__ ) . 'includes/technical/class-boss-redirections.php';
require_once plugin_dir_path( __FILE__ ) . 'includes/technical/class-boss-media-optimization.php';
require_once plugin_dir_path( __FILE__ ) . 'includes/technical/class-boss-broken-links.php';

// Créer une instance temporaire pour accéder aux méthodes
$plugin_name = 'boss-seo';
$version = '1.1.0';

// Créer les tables pour les redirections
$redirections = new Boss_Redirections( $plugin_name, $version );
$redirections->create_tables();

// Créer les tables pour l'optimisation des médias
$media_optimization = new Boss_Media_Optimization( $plugin_name, $version );
$media_optimization->create_tables();

// Créer les tables pour les liens cassés
$broken_links = new Boss_Broken_Links( $plugin_name, $version );
$broken_links->create_table();

echo 'Les tables ont été créées avec succès.';
