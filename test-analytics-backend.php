<?php
/**
 * Script de test pour vérifier l'activation du backend Analytics
 * 
 * À exécuter depuis l'admin WordPress : /wp-admin/admin.php?page=test-analytics-backend
 */

// Empêcher l'accès direct
if (!defined('ABSPATH')) {
    exit;
}

echo '<div class="wrap">';
echo '<h1>🧪 Test Backend Analytics Boss SEO</h1>';

// Test 1: Vérifier si les classes existent
echo '<h2>1. Vérification des classes</h2>';
$classes_to_check = [
    'Boss_Analytics_Init',
    'Boss_Analytics_Manager', 
    'Boss_Analytics_Auth',
    'Boss_GA4_Manager',
    'Boss_GSC_Manager',
    'Boss_Analytics_Cache'
];

foreach ($classes_to_check as $class) {
    if (class_exists($class)) {
        echo "✅ Classe <code>$class</code> : TROUVÉE<br>";
    } else {
        echo "❌ Classe <code>$class</code> : MANQUANTE<br>";
    }
}

// Test 2: Vérifier les routes API REST
echo '<h2>2. Vérification des routes API REST</h2>';
$rest_server = rest_get_server();
$routes = $rest_server->get_routes();

$expected_routes = [
    '/boss-seo/v1/auth/status',
    '/boss-seo/v1/auth/callback',
    '/boss-seo/v1/ga4/overview',
    '/boss-seo/v1/gsc/keywords'
];

foreach ($expected_routes as $route) {
    if (isset($routes[$route])) {
        echo "✅ Route <code>$route</code> : ENREGISTRÉE<br>";
    } else {
        echo "❌ Route <code>$route</code> : MANQUANTE<br>";
    }
}

// Test 3: Vérifier les options
echo '<h2>3. Vérification des options</h2>';
$analytics_options = get_option('boss_analytics_options');
if ($analytics_options) {
    echo "✅ Options Analytics : TROUVÉES<br>";
    echo "<pre>" . print_r($analytics_options, true) . "</pre>";
} else {
    echo "❌ Options Analytics : MANQUANTES<br>";
}

// Test 4: Test d'appel API simple
echo '<h2>4. Test d\'appel API</h2>';
if (class_exists('Boss_Analytics_Auth')) {
    try {
        $auth_manager = new Boss_Analytics_Auth();
        $ga4_connected = $auth_manager->is_service_connected('ga4');
        $gsc_connected = $auth_manager->is_service_connected('gsc');
        
        echo "✅ Test Auth Manager : SUCCÈS<br>";
        echo "📊 GA4 connecté : " . ($ga4_connected ? 'OUI' : 'NON') . "<br>";
        echo "🔍 GSC connecté : " . ($gsc_connected ? 'OUI' : 'NON') . "<br>";
        
    } catch (Exception $e) {
        echo "❌ Test Auth Manager : ERREUR - " . $e->getMessage() . "<br>";
    }
} else {
    echo "❌ Impossible de tester - Classe Auth manquante<br>";
}

// Test 5: Vérifier les CRON
echo '<h2>5. Vérification des tâches CRON</h2>';
$cron_jobs = _get_cron_array();
$analytics_crons = [];

foreach ($cron_jobs as $timestamp => $jobs) {
    foreach ($jobs as $hook => $job_data) {
        if (strpos($hook, 'boss_analytics') !== false) {
            $analytics_crons[] = $hook;
        }
    }
}

if (!empty($analytics_crons)) {
    echo "✅ Tâches CRON Analytics trouvées :<br>";
    foreach ($analytics_crons as $cron) {
        echo "- <code>$cron</code><br>";
    }
} else {
    echo "❌ Aucune tâche CRON Analytics trouvée<br>";
}

// Test 6: Vérifier la configuration JavaScript
echo '<h2>6. Configuration JavaScript</h2>';
echo '<script>';
echo 'if (typeof bossAnalyticsConfig !== "undefined") {';
echo '  document.write("✅ Configuration JavaScript : TROUVÉE<br>");';
echo '  document.write("📡 API URL : " + bossAnalyticsConfig.apiUrl + "<br>");';
echo '} else {';
echo '  document.write("❌ Configuration JavaScript : MANQUANTE<br>");';
echo '}';
echo '</script>';

// Instructions de correction
echo '<h2>🛠️ Instructions de correction</h2>';
echo '<div style="background: #f0f8ff; padding: 15px; border-left: 4px solid #0073aa;">';
echo '<p><strong>Si vous voyez des erreurs :</strong></p>';
echo '<ol>';
echo '<li>Vérifiez que le fichier <code>boss-seo.php</code> contient bien :<br>';
echo '<code>require plugin_dir_path(__FILE__) . "includes/class-boss-analytics-init.php";</code></li>';
echo '<li>Désactivez et réactivez le plugin Boss SEO</li>';
echo '<li>Videz tous les caches (plugin + serveur)</li>';
echo '<li>Vérifiez les logs d\'erreurs PHP</li>';
echo '</ol>';
echo '</div>';

echo '</div>';
?>
