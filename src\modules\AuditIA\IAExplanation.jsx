import { __ } from '@wordpress/i18n';
import {
  Card,
  CardBody,
  Dashicon,
  __experimentalSpacer as Spacer
} from '@wordpress/components';

const IAExplanation = ({ explanation, error }) => {
  // Obtenir l'icône d'impact
  const getImpactIcon = (impact) => {
    if (impact.includes('élevé') || impact.includes('critique')) {
      return { icon: 'warning', color: 'boss-error' };
    } else if (impact.includes('modéré') || impact.includes('moyen')) {
      return { icon: 'info', color: 'boss-warning' };
    } else {
      return { icon: 'lightbulb', color: 'boss-gray' };
    }
  };

  const impactInfo = getImpactIcon(explanation.impact);

  return (
    <div className="boss-border-t boss-border-gray-200 boss-pt-4 boss-mt-4">
      <div className="boss-bg-gradient-to-r boss-from-indigo-50 boss-to-purple-50 boss-border boss-border-indigo-200 boss-rounded-lg boss-p-4">
        {/* En-tête IA */}
        <div className="boss-flex boss-items-center boss-mb-3">
          <div className="boss-bg-gradient-to-r boss-from-indigo-500 boss-to-purple-600 boss-p-2 boss-rounded-lg boss-mr-3">
            <Dashicon icon="admin-comments" className="boss-text-white boss-text-lg" />
          </div>
          <div>
            <h4 className="boss-text-md boss-font-semibold boss-text-boss-dark">
              {__('🧠 Explication par l\'IA', 'boss-seo')}
            </h4>
            <p className="boss-text-sm boss-text-boss-gray">
              {__('Analyse intelligente de cette erreur SEO', 'boss-seo')}
            </p>
          </div>
        </div>

        {/* Description de l'erreur */}
        <div className="boss-mb-4">
          <h5 className="boss-text-sm boss-font-semibold boss-text-boss-dark boss-mb-2 boss-flex boss-items-center">
            <Dashicon icon="info" className="boss-mr-2 boss-text-blue-600" />
            {__('Qu\'est-ce que cette erreur ?', 'boss-seo')}
          </h5>
          <div className="boss-bg-white boss-border boss-border-blue-200 boss-rounded boss-p-3">
            <p className="boss-text-sm boss-text-boss-gray boss-leading-relaxed">
              {explanation.description}
            </p>
          </div>
        </div>

        {/* Impact sur le SEO */}
        <div className="boss-mb-4">
          <h5 className="boss-text-sm boss-font-semibold boss-text-boss-dark boss-mb-2 boss-flex boss-items-center">
            <Dashicon icon={impactInfo.icon} className={`boss-mr-2 boss-text-${impactInfo.color}`} />
            {__('Impact sur votre SEO', 'boss-seo')}
          </h5>
          <div className={`boss-bg-white boss-border boss-border-${impactInfo.color}/20 boss-rounded boss-p-3`}>
            <div className="boss-flex boss-items-center boss-mb-2">
              <span className={`boss-px-2 boss-py-1 boss-rounded boss-text-xs boss-font-medium boss-bg-${impactInfo.color}/10 boss-text-${impactInfo.color}`}>
                {explanation.impact}
              </span>
            </div>
            <p className="boss-text-sm boss-text-boss-gray boss-leading-relaxed">
              {explanation.recommendation}
            </p>
          </div>
        </div>

        {/* Pourquoi c'est important */}
        <div className="boss-mb-4">
          <h5 className="boss-text-sm boss-font-semibold boss-text-boss-dark boss-mb-2 boss-flex boss-items-center">
            <Dashicon icon="star-filled" className="boss-mr-2 boss-text-yellow-500" />
            {__('Pourquoi c\'est important ?', 'boss-seo')}
          </h5>
          <div className="boss-bg-white boss-border boss-border-yellow-200 boss-rounded boss-p-3">
            <div className="boss-space-y-2">
              {/* Raisons basées sur la catégorie d'erreur */}
              {error.category === 'meta' && (
                <div className="boss-flex boss-items-start">
                  <Dashicon icon="search" className="boss-mr-2 boss-text-green-600 boss-mt-0.5 boss-flex-shrink-0" />
                  <p className="boss-text-sm boss-text-boss-gray">
                    {__('Les balises meta aident les moteurs de recherche à comprendre le contenu de votre page et influencent directement votre classement dans les résultats de recherche.', 'boss-seo')}
                  </p>
                </div>
              )}
              
              {error.category === 'content' && (
                <div className="boss-flex boss-items-start">
                  <Dashicon icon="edit" className="boss-mr-2 boss-text-green-600 boss-mt-0.5 boss-flex-shrink-0" />
                  <p className="boss-text-sm boss-text-boss-gray">
                    {__('La qualité et la structure du contenu sont des facteurs clés pour le référencement et l\'expérience utilisateur.', 'boss-seo')}
                  </p>
                </div>
              )}
              
              {error.category === 'technical' && (
                <div className="boss-flex boss-items-start">
                  <Dashicon icon="admin-tools" className="boss-mr-2 boss-text-green-600 boss-mt-0.5 boss-flex-shrink-0" />
                  <p className="boss-text-sm boss-text-boss-gray">
                    {__('Les aspects techniques affectent la capacité des moteurs de recherche à explorer et indexer votre site efficacement.', 'boss-seo')}
                  </p>
                </div>
              )}
              
              {error.category === 'performance' && (
                <div className="boss-flex boss-items-start">
                  <Dashicon icon="performance" className="boss-mr-2 boss-text-green-600 boss-mt-0.5 boss-flex-shrink-0" />
                  <p className="boss-text-sm boss-text-boss-gray">
                    {__('La vitesse de chargement est un facteur de classement important et améliore l\'expérience utilisateur.', 'boss-seo')}
                  </p>
                </div>
              )}
              
              {error.category === 'accessibility' && (
                <div className="boss-flex boss-items-start">
                  <Dashicon icon="universal-access" className="boss-mr-2 boss-text-green-600 boss-mt-0.5 boss-flex-shrink-0" />
                  <p className="boss-text-sm boss-text-boss-gray">
                    {__('L\'accessibilité améliore l\'expérience pour tous les utilisateurs et peut influencer positivement votre SEO.', 'boss-seo')}
                  </p>
                </div>
              )}
              
              {error.category === 'images' && (
                <div className="boss-flex boss-items-start">
                  <Dashicon icon="format-image" className="boss-mr-2 boss-text-green-600 boss-mt-0.5 boss-flex-shrink-0" />
                  <p className="boss-text-sm boss-text-boss-gray">
                    {__('L\'optimisation des images améliore les performances et permet un meilleur référencement dans la recherche d\'images.', 'boss-seo')}
                  </p>
                </div>
              )}
              
              {error.category === 'links' && (
                <div className="boss-flex boss-items-start">
                  <Dashicon icon="admin-links" className="boss-mr-2 boss-text-green-600 boss-mt-0.5 boss-flex-shrink-0" />
                  <p className="boss-text-sm boss-text-boss-gray">
                    {__('Les liens internes et externes contribuent à la structure de votre site et à l\'autorité de vos pages.', 'boss-seo')}
                  </p>
                </div>
              )}
              
              {error.category === 'structure' && (
                <div className="boss-flex boss-items-start">
                  <Dashicon icon="editor-code" className="boss-mr-2 boss-text-green-600 boss-mt-0.5 boss-flex-shrink-0" />
                  <p className="boss-text-sm boss-text-boss-gray">
                    {__('Une structure HTML claire aide les moteurs de recherche à comprendre la hiérarchie et l\'importance du contenu.', 'boss-seo')}
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Conseils d'action */}
        <div>
          <h5 className="boss-text-sm boss-font-semibold boss-text-boss-dark boss-mb-2 boss-flex boss-items-center">
            <Dashicon icon="yes" className="boss-mr-2 boss-text-green-600" />
            {__('Prochaines étapes recommandées', 'boss-seo')}
          </h5>
          <div className="boss-bg-white boss-border boss-border-green-200 boss-rounded boss-p-3">
            <div className="boss-space-y-2">
              <div className="boss-flex boss-items-start">
                <span className="boss-bg-green-100 boss-text-green-800 boss-text-xs boss-font-medium boss-px-2 boss-py-1 boss-rounded boss-mr-2 boss-flex-shrink-0">
                  1
                </span>
                <p className="boss-text-sm boss-text-boss-gray">
                  {__('Utilisez la suggestion de correction automatique ci-dessous', 'boss-seo')}
                </p>
              </div>
              
              <div className="boss-flex boss-items-start">
                <span className="boss-bg-green-100 boss-text-green-800 boss-text-xs boss-font-medium boss-px-2 boss-py-1 boss-rounded boss-mr-2 boss-flex-shrink-0">
                  2
                </span>
                <p className="boss-text-sm boss-text-boss-gray">
                  {__('Testez les modifications sur une page de test avant de les appliquer en production', 'boss-seo')}
                </p>
              </div>
              
              <div className="boss-flex boss-items-start">
                <span className="boss-bg-green-100 boss-text-green-800 boss-text-xs boss-font-medium boss-px-2 boss-py-1 boss-rounded boss-mr-2 boss-flex-shrink-0">
                  3
                </span>
                <p className="boss-text-sm boss-text-boss-gray">
                  {__('Relancez un audit après correction pour vérifier l\'amélioration', 'boss-seo')}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Note sur l'IA */}
        <div className="boss-mt-4 boss-pt-3 boss-border-t boss-border-indigo-200">
          <div className="boss-flex boss-items-center boss-text-xs boss-text-boss-gray">
            <Dashicon icon="admin-comments" className="boss-mr-1 boss-text-indigo-500" />
            <span>
              {__('Cette explication a été générée par l\'intelligence artificielle basée sur les meilleures pratiques SEO actuelles.', 'boss-seo')}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default IAExplanation;
