import { useState, useEffect } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  Card,
  CardBody,
  CardHeader,
  Button,
  Dashicon,
  CheckboxControl,
  SelectControl,
  DropdownMenu,
  MenuGroup,
  MenuItem,
  Notice,
  __experimentalSpacer as Spacer
} from '@wordpress/components';

const TodoPlan = ({ errors, aiEnabled }) => {
  const [tasks, setTasks] = useState([]);
  const [filter, setFilter] = useState('all');
  const [sortBy, setSortBy] = useState('priority');
  const [completedTasks, setCompletedTasks] = useState(new Set());

  // Générer les tâches à partir des erreurs
  useEffect(() => {
    generateTasks();
  }, [errors]);

  const generateTasks = () => {
    const allTasks = [];
    let taskId = 1;

    // Traiter les erreurs critiques
    errors.critical.forEach(error => {
      allTasks.push({
        id: taskId++,
        title: error.title,
        description: error.description,
        category: error.category,
        priority: 'high',
        severity: 'critical',
        estimatedTime: getEstimatedTime(error),
        difficulty: getDifficulty(error),
        impact: 'Élevé',
        errorRef: error
      });
    });

    // Traiter les erreurs moyennes
    errors.medium.forEach(error => {
      allTasks.push({
        id: taskId++,
        title: error.title,
        description: error.description,
        category: error.category,
        priority: 'medium',
        severity: 'medium',
        estimatedTime: getEstimatedTime(error),
        difficulty: getDifficulty(error),
        impact: 'Moyen',
        errorRef: error
      });
    });

    // Traiter les erreurs faibles
    errors.low.forEach(error => {
      allTasks.push({
        id: taskId++,
        title: error.title,
        description: error.description,
        category: error.category,
        priority: 'low',
        severity: 'low',
        estimatedTime: getEstimatedTime(error),
        difficulty: getDifficulty(error),
        impact: 'Faible',
        errorRef: error
      });
    });

    setTasks(allTasks);
  };

  // Estimer le temps nécessaire
  const getEstimatedTime = (error) => {
    const timeMap = {
      'meta': '5-10 min',
      'content': '15-30 min',
      'technical': '30-60 min',
      'performance': '45-90 min',
      'accessibility': '10-20 min',
      'images': '5-15 min',
      'links': '10-30 min',
      'structure': '20-45 min'
    };
    return timeMap[error.category] || '15-30 min';
  };

  // Déterminer la difficulté
  const getDifficulty = (error) => {
    const difficultyMap = {
      'meta': 'Facile',
      'content': 'Moyen',
      'technical': 'Difficile',
      'performance': 'Difficile',
      'accessibility': 'Moyen',
      'images': 'Facile',
      'links': 'Moyen',
      'structure': 'Moyen'
    };
    return difficultyMap[error.category] || 'Moyen';
  };

  // Filtrer et trier les tâches
  const getFilteredAndSortedTasks = () => {
    let filteredTasks = tasks;

    // Appliquer le filtre
    if (filter === 'completed') {
      filteredTasks = tasks.filter(task => completedTasks.has(task.id));
    } else if (filter === 'pending') {
      filteredTasks = tasks.filter(task => !completedTasks.has(task.id));
    } else if (filter !== 'all') {
      filteredTasks = tasks.filter(task => task.priority === filter);
    }

    // Appliquer le tri
    return filteredTasks.sort((a, b) => {
      switch (sortBy) {
        case 'priority':
          const priorityOrder = { 'high': 3, 'medium': 2, 'low': 1 };
          return priorityOrder[b.priority] - priorityOrder[a.priority];
        case 'category':
          return a.category.localeCompare(b.category);
        case 'difficulty':
          const difficultyOrder = { 'Facile': 1, 'Moyen': 2, 'Difficile': 3 };
          return difficultyOrder[a.difficulty] - difficultyOrder[b.difficulty];
        default:
          return 0;
      }
    });
  };

  // Basculer l'état d'une tâche
  const toggleTask = (taskId) => {
    const newCompletedTasks = new Set(completedTasks);
    if (newCompletedTasks.has(taskId)) {
      newCompletedTasks.delete(taskId);
    } else {
      newCompletedTasks.add(taskId);
    }
    setCompletedTasks(newCompletedTasks);
  };

  // Obtenir la couleur de priorité
  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high':
        return 'boss-error';
      case 'medium':
        return 'boss-warning';
      case 'low':
        return 'boss-gray';
      default:
        return 'boss-gray';
    }
  };

  // Obtenir l'icône de catégorie
  const getCategoryIcon = (category) => {
    const iconMap = {
      'meta': 'tag',
      'content': 'edit',
      'technical': 'admin-tools',
      'performance': 'performance',
      'accessibility': 'universal-access',
      'images': 'format-image',
      'links': 'admin-links',
      'structure': 'editor-code'
    };
    return iconMap[category] || 'info';
  };

  // Exporter le plan d'action
  const exportPlan = (format) => {
    const pendingTasks = tasks.filter(task => !completedTasks.has(task.id));

    if (format === 'csv') {
      exportToCSV(pendingTasks);
    } else if (format === 'pdf') {
      exportToPDF(pendingTasks);
    }
  };

  // Exporter en CSV
  const exportToCSV = (tasksToExport) => {
    const headers = ['Tâche', 'Description', 'Catégorie', 'Priorité', 'Difficulté', 'Temps estimé', 'Impact'];
    const csvContent = [
      headers.join(','),
      ...tasksToExport.map(task => [
        `"${task.title}"`,
        `"${task.description}"`,
        task.category,
        task.priority,
        task.difficulty,
        `"${task.estimatedTime}"`,
        task.impact
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = 'plan-action-seo.csv';
    link.click();
  };

  // Exporter en PDF (simulation)
  const exportToPDF = (tasksToExport) => {
    // Dans une vraie implémentation, on utiliserait une bibliothèque comme jsPDF
    const content = [
      '=== PLAN D\'ACTION SEO ===\n',
      `Date: ${new Date().toLocaleDateString('fr-FR')}\n`,
      `Nombre de tâches: ${tasksToExport.length}\n\n`,
      ...tasksToExport.map((task, index) =>
        `${index + 1}. ${task.title}\n` +
        `   Catégorie: ${task.category}\n` +
        `   Priorité: ${task.priority}\n` +
        `   Difficulté: ${task.difficulty}\n` +
        `   Temps estimé: ${task.estimatedTime}\n` +
        `   Description: ${task.description}\n\n`
      )
    ].join('');

    const blob = new Blob([content], { type: 'text/plain;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = 'plan-action-seo.txt';
    link.click();
  };

  const filteredTasks = getFilteredAndSortedTasks();
  const completionRate = tasks.length > 0 ? (completedTasks.size / tasks.length) * 100 : 0;

  return (
    <div className="boss-space-y-6">
      {/* En-tête avec statistiques */}
      <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-4 boss-gap-4">
        <Card>
          <CardBody>
            <div className="boss-text-center">
              <div className="boss-text-2xl boss-font-bold boss-text-boss-primary boss-mb-1">
                {tasks.length}
              </div>
              <div className="boss-text-sm boss-text-boss-gray">
                {__('Tâches totales', 'boss-seo')}
              </div>
            </div>
          </CardBody>
        </Card>

        <Card>
          <CardBody>
            <div className="boss-text-center">
              <div className="boss-text-2xl boss-font-bold boss-text-boss-success boss-mb-1">
                {completedTasks.size}
              </div>
              <div className="boss-text-sm boss-text-boss-gray">
                {__('Terminées', 'boss-seo')}
              </div>
            </div>
          </CardBody>
        </Card>

        <Card>
          <CardBody>
            <div className="boss-text-center">
              <div className="boss-text-2xl boss-font-bold boss-text-boss-warning boss-mb-1">
                {tasks.length - completedTasks.size}
              </div>
              <div className="boss-text-sm boss-text-boss-gray">
                {__('En attente', 'boss-seo')}
              </div>
            </div>
          </CardBody>
        </Card>

        <Card>
          <CardBody>
            <div className="boss-text-center">
              <div className="boss-text-2xl boss-font-bold boss-text-boss-primary boss-mb-1">
                {Math.round(completionRate)}%
              </div>
              <div className="boss-text-sm boss-text-boss-gray">
                {__('Progression', 'boss-seo')}
              </div>
            </div>
          </CardBody>
        </Card>
      </div>

      {/* Barre de progression */}
      {tasks.length > 0 && (
        <Card>
          <CardBody>
            <div className="boss-flex boss-items-center boss-justify-between boss-mb-2">
              <h3 className="boss-text-lg boss-font-semibold">
                {__('Progression du plan d\'action', 'boss-seo')}
              </h3>
              <span className="boss-text-sm boss-text-boss-gray">
                {completedTasks.size}/{tasks.length} {__('tâches terminées', 'boss-seo')}
              </span>
            </div>
            <div className="boss-w-full boss-bg-gray-200 boss-rounded-full boss-h-3">
              <div
                className="boss-bg-boss-success boss-h-3 boss-rounded-full boss-transition-all boss-duration-300"
                style={{ width: `${completionRate}%` }}
              ></div>
            </div>
          </CardBody>
        </Card>
      )}

      {/* Contrôles de filtrage et tri */}
      <Card>
        <CardBody>
          <div className="boss-flex boss-flex-wrap boss-items-center boss-justify-between boss-gap-4">
            <div className="boss-flex boss-items-center boss-space-x-4">
              <SelectControl
                label={__('Filtrer par', 'boss-seo')}
                value={filter}
                options={[
                  { label: __('Toutes les tâches', 'boss-seo'), value: 'all' },
                  { label: __('En attente', 'boss-seo'), value: 'pending' },
                  { label: __('Terminées', 'boss-seo'), value: 'completed' },
                  { label: __('Priorité élevée', 'boss-seo'), value: 'high' },
                  { label: __('Priorité moyenne', 'boss-seo'), value: 'medium' },
                  { label: __('Priorité faible', 'boss-seo'), value: 'low' }
                ]}
                onChange={setFilter}
              />

              <SelectControl
                label={__('Trier par', 'boss-seo')}
                value={sortBy}
                options={[
                  { label: __('Priorité', 'boss-seo'), value: 'priority' },
                  { label: __('Catégorie', 'boss-seo'), value: 'category' },
                  { label: __('Difficulté', 'boss-seo'), value: 'difficulty' }
                ]}
                onChange={setSortBy}
              />
            </div>

            <div className="boss-flex boss-items-center boss-space-x-2">
              <DropdownMenu
                icon="download"
                label={__('Exporter le plan', 'boss-seo')}
                className="boss-flex boss-items-center"
              >
                {() => (
                  <MenuGroup>
                    <MenuItem
                      icon="media-spreadsheet"
                      onClick={() => exportPlan('csv')}
                    >
                      {__('Exporter en CSV', 'boss-seo')}
                    </MenuItem>
                    <MenuItem
                      icon="media-document"
                      onClick={() => exportPlan('pdf')}
                    >
                      {__('Exporter en PDF', 'boss-seo')}
                    </MenuItem>
                  </MenuGroup>
                )}
              </DropdownMenu>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Liste des tâches */}
      {filteredTasks.length > 0 ? (
        <div className="boss-space-y-4">
          {filteredTasks.map(task => (
            <Card
              key={task.id}
              className={`boss-border-l-4 boss-border-${getPriorityColor(task.priority)} ${
                completedTasks.has(task.id) ? 'boss-opacity-60' : ''
              }`}
            >
              <CardBody>
                <div className="boss-flex boss-items-start boss-space-x-4">
                  {/* Checkbox */}
                  <div className="boss-flex-shrink-0 boss-mt-1">
                    <CheckboxControl
                      checked={completedTasks.has(task.id)}
                      onChange={() => toggleTask(task.id)}
                    />
                  </div>

                  {/* Icône de catégorie */}
                  <div className={`boss-bg-${getPriorityColor(task.priority)}/10 boss-p-2 boss-rounded-lg boss-flex-shrink-0`}>
                    <Dashicon
                      icon={getCategoryIcon(task.category)}
                      className={`boss-text-${getPriorityColor(task.priority)} boss-text-lg`}
                    />
                  </div>

                  {/* Contenu de la tâche */}
                  <div className="boss-flex-1">
                    <div className="boss-flex boss-items-start boss-justify-between boss-mb-2">
                      <h3 className={`boss-text-lg boss-font-semibold ${
                        completedTasks.has(task.id) ? 'boss-line-through boss-text-boss-gray' : 'boss-text-boss-dark'
                      }`}>
                        {task.title}
                      </h3>

                      <div className="boss-flex boss-items-center boss-space-x-2 boss-flex-shrink-0 boss-ml-4">
                        <span className={`boss-px-2 boss-py-1 boss-rounded boss-text-xs boss-font-medium boss-bg-${getPriorityColor(task.priority)}/10 boss-text-${getPriorityColor(task.priority)}`}>
                          {task.priority === 'high' ? __('Élevée', 'boss-seo') :
                           task.priority === 'medium' ? __('Moyenne', 'boss-seo') :
                           __('Faible', 'boss-seo')}
                        </span>
                      </div>
                    </div>

                    <p className="boss-text-boss-gray boss-text-sm boss-mb-3">
                      {task.description}
                    </p>

                    {/* Métadonnées de la tâche */}
                    <div className="boss-flex boss-flex-wrap boss-items-center boss-gap-4 boss-text-sm boss-text-boss-gray">
                      <div className="boss-flex boss-items-center">
                        <Dashicon icon="clock" className="boss-mr-1" />
                        {task.estimatedTime}
                      </div>

                      <div className="boss-flex boss-items-center">
                        <Dashicon icon="chart-bar" className="boss-mr-1" />
                        {task.difficulty}
                      </div>

                      <div className="boss-flex boss-items-center">
                        <Dashicon icon="star-filled" className="boss-mr-1" />
                        {__('Impact:', 'boss-seo')} {task.impact}
                      </div>

                      <div className="boss-flex boss-items-center">
                        <Dashicon icon="category" className="boss-mr-1" />
                        {task.category}
                      </div>
                    </div>
                  </div>
                </div>
              </CardBody>
            </Card>
          ))}
        </div>
      ) : (
        <Card>
          <CardBody>
            <div className="boss-text-center boss-py-8">
              {tasks.length === 0 ? (
                <>
                  <Dashicon icon="yes-alt" className="boss-text-boss-success boss-text-4xl boss-mb-4" />
                  <h3 className="boss-text-lg boss-font-semibold boss-mb-2">
                    {__('Aucune tâche à effectuer !', 'boss-seo')}
                  </h3>
                  <p className="boss-text-boss-gray">
                    {__('Votre site semble bien optimisé. Lancez un nouvel audit pour vérifier.', 'boss-seo')}
                  </p>
                </>
              ) : (
                <>
                  <Dashicon icon="filter" className="boss-text-boss-gray boss-text-4xl boss-mb-4" />
                  <h3 className="boss-text-lg boss-font-semibold boss-mb-2">
                    {__('Aucune tâche ne correspond aux filtres', 'boss-seo')}
                  </h3>
                  <p className="boss-text-boss-gray">
                    {__('Modifiez les filtres pour voir d\'autres tâches.', 'boss-seo')}
                  </p>
                </>
              )}
            </div>
          </CardBody>
        </Card>
      )}

      {/* Conseils IA */}
      {aiEnabled && tasks.length > 0 && (
        <Card className="boss-bg-gradient-to-r boss-from-blue-50 boss-to-indigo-50 boss-border boss-border-blue-200">
          <CardBody>
            <div className="boss-flex boss-items-start">
              <div className="boss-bg-gradient-to-r boss-from-blue-500 boss-to-indigo-600 boss-p-2 boss-rounded-lg boss-mr-3 boss-flex-shrink-0">
                <Dashicon icon="lightbulb" className="boss-text-white boss-text-lg" />
              </div>
              <div>
                <h4 className="boss-text-md boss-font-semibold boss-text-boss-dark boss-mb-2">
                  {__('💡 Conseil de l\'IA', 'boss-seo')}
                </h4>
                <p className="boss-text-sm boss-text-boss-gray boss-leading-relaxed">
                  {completionRate < 25 ?
                    __('Commencez par les tâches critiques pour obtenir un impact maximal sur votre SEO. Concentrez-vous sur les balises meta et la structure technique.', 'boss-seo') :
                    completionRate < 75 ?
                    __('Excellent progrès ! Continuez avec les tâches de priorité moyenne. N\'oubliez pas de tester vos modifications avant de les appliquer en production.', 'boss-seo') :
                    __('Félicitations ! Vous avez presque terminé. Les dernières tâches concernent principalement l\'optimisation fine et les améliorations mineures.', 'boss-seo')
                  }
                </p>
              </div>
            </div>
          </CardBody>
        </Card>
      )}
    </div>
  );
};

export default TodoPlan;
