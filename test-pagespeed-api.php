<?php
/**
 * Script de test pour vérifier l'API Google PageSpeed Insights
 * 
 * Ce script teste si votre clé API PageSpeed fonctionne correctement
 * et si le module Analyse technique peut l'utiliser.
 */

// Charger WordPress
require_once('wp-config.php');
require_once(ABSPATH . 'wp-load.php');

// Vérifier les permissions
if (!current_user_can('manage_options')) {
    die('❌ Accès refusé. Vous devez être administrateur.');
}

echo "<h1>🔍 DIAGNOSTIC API GOOGLE PAGESPEED INSIGHTS</h1>\n";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;} .success{color:green;} .error{color:red;} .warning{color:orange;} .info{color:blue;}</style>\n";

// 1. Vérifier la configuration de la clé API
echo "<h2>1. 📋 Vérification de la configuration</h2>\n";

$external_services = get_option('boss_optimizer_external_services', array());
$api_key = isset($external_services['google_pagespeed']['api_key']) ? $external_services['google_pagespeed']['api_key'] : '';

if (empty($api_key)) {
    // Fallback vers l'ancienne structure
    $api_key = get_option('boss_optimizer_pagespeed_api_key', '');
}

if (empty($api_key)) {
    echo "<p class='error'>❌ <strong>PROBLÈME TROUVÉ :</strong> Aucune clé API PageSpeed Insights configurée</p>\n";
    echo "<p class='info'>💡 <strong>Solution :</strong> Allez dans Boss SEO > Paramètres > Services externes > Google PageSpeed Insights et ajoutez votre clé API</p>\n";
    echo "<p class='info'>🔗 <strong>Obtenir une clé API :</strong> <a href='https://developers.google.com/speed/docs/insights/v5/get-started' target='_blank'>https://developers.google.com/speed/docs/insights/v5/get-started</a></p>\n";
} else {
    echo "<p class='success'>✅ Clé API trouvée : " . substr($api_key, 0, 10) . "..." . substr($api_key, -5) . "</p>\n";
    echo "<p class='info'>📍 Source : boss_optimizer_external_services['google_pagespeed']['api_key']</p>\n";
}

// 2. Tester la clé API directement
if (!empty($api_key)) {
    echo "<h2>2. 🧪 Test direct de l'API PageSpeed</h2>\n";
    
    $test_url = home_url();
    $api_url = add_query_arg(
        array(
            'url' => urlencode($test_url),
            'key' => $api_key,
            'strategy' => 'mobile',
            'category' => 'performance',
            'locale' => 'fr_FR',
        ),
        'https://www.googleapis.com/pagespeedonline/v5/runPagespeed'
    );
    
    echo "<p class='info'>🎯 Test de l'URL : <strong>$test_url</strong></p>\n";
    echo "<p class='info'>⏳ Appel de l'API en cours...</p>\n";
    
    $response = wp_remote_get($api_url, array(
        'timeout' => 45,
        'headers' => array(
            'User-Agent' => 'Boss SEO WordPress Plugin Test/1.0'
        )
    ));
    
    if (is_wp_error($response)) {
        echo "<p class='error'>❌ <strong>ERREUR RÉSEAU :</strong> " . $response->get_error_message() . "</p>\n";
    } else {
        $status_code = wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);
        
        if ($status_code === 200) {
            $data = json_decode($body, true);
            
            if (json_last_error() === JSON_ERROR_NONE && isset($data['lighthouseResult'])) {
                echo "<p class='success'>✅ <strong>API FONCTIONNE PARFAITEMENT !</strong></p>\n";
                
                // Afficher les scores
                if (isset($data['lighthouseResult']['categories'])) {
                    echo "<h3>📊 Scores obtenus :</h3>\n";
                    foreach ($data['lighthouseResult']['categories'] as $category_id => $category) {
                        $score = round($category['score'] * 100);
                        $color = $score >= 90 ? 'success' : ($score >= 50 ? 'warning' : 'error');
                        echo "<p class='$color'>• <strong>" . $category['title'] . " :</strong> $score/100</p>\n";
                    }
                }
                
                // Vérifier les Core Web Vitals
                if (isset($data['lighthouseResult']['audits'])) {
                    echo "<h3>⚡ Core Web Vitals :</h3>\n";
                    $audits = $data['lighthouseResult']['audits'];
                    
                    if (isset($audits['largest-contentful-paint'])) {
                        $lcp = round($audits['largest-contentful-paint']['numericValue'] / 1000, 1);
                        $lcp_status = $lcp <= 2.5 ? 'success' : ($lcp <= 4.0 ? 'warning' : 'error');
                        echo "<p class='$lcp_status'>• <strong>LCP :</strong> {$lcp}s</p>\n";
                    }
                    
                    if (isset($audits['cumulative-layout-shift'])) {
                        $cls = round($audits['cumulative-layout-shift']['numericValue'], 3);
                        $cls_status = $cls <= 0.1 ? 'success' : ($cls <= 0.25 ? 'warning' : 'error');
                        echo "<p class='$cls_status'>• <strong>CLS :</strong> $cls</p>\n";
                    }
                }
                
            } else {
                echo "<p class='error'>❌ <strong>RÉPONSE API INVALIDE :</strong> Structure JSON inattendue</p>\n";
                echo "<pre>" . substr($body, 0, 500) . "...</pre>\n";
            }
        } else {
            echo "<p class='error'>❌ <strong>ERREUR API (HTTP $status_code) :</strong></p>\n";
            $error_data = json_decode($body, true);
            if (isset($error_data['error']['message'])) {
                echo "<p class='error'>Message : " . $error_data['error']['message'] . "</p>\n";
            } else {
                echo "<pre>" . substr($body, 0, 500) . "...</pre>\n";
            }
        }
    }
}

// 3. Tester le gestionnaire Boss PageSpeed
if (!empty($api_key)) {
    echo "<h2>3. 🔧 Test du gestionnaire Boss PageSpeed</h2>\n";
    
    try {
        require_once(ABSPATH . 'wp-content/plugins/boss-seo/includes/class-boss-pagespeed-manager.php');
        
        $pagespeed_manager = new Boss_PageSpeed_Manager();
        echo "<p class='success'>✅ Boss_PageSpeed_Manager instancié avec succès</p>\n";
        
        // Vider le cache pour forcer un nouvel appel API
        $pagespeed_manager->clear_cache();
        echo "<p class='info'>🗑️ Cache PageSpeed vidé</p>\n";
        
        // Tester l'analyse
        echo "<p class='info'>⏳ Test d'analyse avec Boss_PageSpeed_Manager...</p>\n";
        $results = $pagespeed_manager->analyze_url(home_url(), 'mobile', array('performance'));
        
        if ($results) {
            echo "<p class='success'>✅ <strong>BOSS PAGESPEED MANAGER FONCTIONNE !</strong></p>\n";
            echo "<p class='info'>📊 Données récupérées : " . count($results) . " éléments</p>\n";
            
            if (isset($results['scores'])) {
                echo "<h4>Scores via Boss Manager :</h4>\n";
                foreach ($results['scores'] as $category => $score_data) {
                    echo "<p class='info'>• <strong>" . $score_data['title'] . " :</strong> " . $score_data['score'] . "/100</p>\n";
                }
            }
        } else {
            echo "<p class='error'>❌ <strong>ÉCHEC DU BOSS PAGESPEED MANAGER</strong></p>\n";
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ <strong>ERREUR BOSS PAGESPEED MANAGER :</strong> " . $e->getMessage() . "</p>\n";
    }
}

// 4. Vérifier le cache
echo "<h2>4. 💾 Vérification du cache</h2>\n";

global $wpdb;
$cache_entries = $wpdb->get_results(
    "SELECT option_name, option_value FROM {$wpdb->options} 
     WHERE option_name LIKE '_transient_boss_pagespeed_%' 
     ORDER BY option_name"
);

if (empty($cache_entries)) {
    echo "<p class='info'>📭 Aucune entrée de cache PageSpeed trouvée</p>\n";
} else {
    echo "<p class='info'>📦 " . count($cache_entries) . " entrées de cache PageSpeed trouvées</p>\n";
    foreach ($cache_entries as $entry) {
        $cache_key = str_replace('_transient_', '', $entry->option_name);
        $data = maybe_unserialize($entry->option_value);
        $url = isset($data['url']) ? $data['url'] : 'URL inconnue';
        $date = isset($data['date']) ? $data['date'] : 'Date inconnue';
        echo "<p class='info'>• <strong>$cache_key :</strong> $url ($date)</p>\n";
    }
}

// 5. Recommandations
echo "<h2>5. 💡 Recommandations</h2>\n";

if (empty($api_key)) {
    echo "<p class='error'>🔑 <strong>PRIORITÉ HAUTE :</strong> Configurez votre clé API Google PageSpeed Insights</p>\n";
    echo "<p class='info'>1. Allez sur <a href='https://console.developers.google.com/' target='_blank'>Google Cloud Console</a></p>\n";
    echo "<p class='info'>2. Créez un projet ou sélectionnez un projet existant</p>\n";
    echo "<p class='info'>3. Activez l'API PageSpeed Insights</p>\n";
    echo "<p class='info'>4. Créez une clé API</p>\n";
    echo "<p class='info'>5. Ajoutez la clé dans Boss SEO > Paramètres > Services externes</p>\n";
} else {
    echo "<p class='success'>✅ Votre configuration semble correcte !</p>\n";
    echo "<p class='info'>💡 Si l'analyse technique ne montre pas de données PageSpeed :</p>\n";
    echo "<p class='info'>1. Videz le cache PageSpeed dans l'interface</p>\n";
    echo "<p class='info'>2. Relancez une analyse technique</p>\n";
    echo "<p class='info'>3. Vérifiez les logs d'erreur WordPress</p>\n";
}

echo "<hr>\n";
echo "<p class='info'>🔧 <strong>Script de diagnostic terminé</strong> - " . date('Y-m-d H:i:s') . "</p>\n";
echo "<p class='warning'>⚠️ <strong>Supprimez ce fichier après utilisation pour des raisons de sécurité</strong></p>\n";
?>
