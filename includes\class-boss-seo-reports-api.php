<?php

/**
 * API REST pour les rapports Boss SEO avec données réelles.
 * 
 * Cette classe fournit les endpoints REST API pour le module
 * de rapports avec des données réelles collectées depuis les
 * modules existants.
 *
 * @link       https://boss-seo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 */

/**
 * API REST des rapports.
 *
 * Endpoints disponibles :
 * - GET /reports/data/{type} - Récupère les données pour un type de rapport
 * - POST /reports/generate - Génère un nouveau rapport
 * - GET /reports/history - Récupère l'historique des rapports
 * - DELETE /reports/{id} - Supprime un rapport
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 * <AUTHOR> SEO Team
 */
class Boss_SEO_Reports_API {

    /**
     * Le nom du plugin.
     *
     * @since    1.2.0
     * @access   private
     * @var      string    $plugin_name    Le nom du plugin.
     */
    private $plugin_name;

    /**
     * La version du plugin.
     *
     * @since    1.2.0
     * @access   private
     * @var      string    $version    La version du plugin.
     */
    private $version;

    /**
     * Instance du collecteur de données.
     *
     * @since    1.2.0
     * @access   private
     * @var      Boss_SEO_Reports_Data_Collector    $data_collector    Collecteur de données.
     */
    private $data_collector;

    /**
     * Instance du système de stockage.
     *
     * @since    1.2.0
     * @access   private
     * @var      Boss_SEO_Reports_Storage    $storage    Système de stockage.
     */
    private $storage;

    /**
     * Instance du générateur de performance.
     *
     * @since    1.2.0
     * @access   private
     * @var      Boss_SEO_Performance_Report_Generator    $performance_generator    Générateur de performance.
     */
    private $performance_generator;

    /**
     * Instance du générateur de templates.
     *
     * @since    1.2.0
     * @access   private
     * @var      Boss_SEO_Report_Template_Generator    $template_generator    Générateur de templates.
     */
    private $template_generator;

    /**
     * Instance du générateur de mots-clés.
     *
     * @since    1.2.0
     * @access   private
     * @var      Boss_SEO_Keywords_Report_Generator    $keywords_generator    Générateur de mots-clés.
     */
    private $keywords_generator;

    /**
     * Instance du générateur PDF.
     *
     * @since    1.2.0
     * @access   private
     * @var      Boss_SEO_PDF_Generator    $pdf_generator    Générateur PDF.
     */
    private $pdf_generator;

    /**
     * Instance Google Search Console.
     *
     * @since    1.2.0
     * @access   private
     * @var      Boss_SEO_Google_Search_Console    $gsc    Instance GSC.
     */
    private $gsc;

    /**
     * Namespace de l'API.
     *
     * @since    1.2.0
     * @access   private
     * @var      string    $namespace    Namespace de l'API.
     */
    private $namespace = 'boss-seo/v1';

    /**
     * Initialise la classe.
     *
     * @since    1.2.0
     * @param    string                           $plugin_name      Le nom du plugin.
     * @param    string                           $version          La version du plugin.
     * @param    Boss_SEO_Reports_Data_Collector  $data_collector   Collecteur de données.
     * @param    Boss_SEO_Reports_Storage         $storage          Système de stockage.
     */
    public function __construct( $plugin_name, $version, $data_collector, $storage ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
        $this->data_collector = $data_collector;
        $this->storage = $storage;

        // Initialiser les générateurs avancés (Phase 2)
        $this->performance_generator = new Boss_SEO_Performance_Report_Generator( $data_collector );
        $this->template_generator = new Boss_SEO_Report_Template_Generator();

        // Initialiser les intégrations externes (Phase 3)
        $this->gsc = new Boss_SEO_Google_Search_Console( $plugin_name );
        $this->keywords_generator = new Boss_SEO_Keywords_Report_Generator( $data_collector, $this->gsc );
        $this->pdf_generator = new Boss_SEO_PDF_Generator( $this->template_generator );
    }

    /**
     * Enregistre les routes de l'API REST.
     *
     * @since    1.2.0
     */
    public function register_routes() {
        // Route pour récupérer les données d'un type de rapport
        register_rest_route( $this->namespace, '/reports/data/(?P<type>[a-zA-Z0-9_-]+)', array(
            'methods' => WP_REST_Server::READABLE,
            'callback' => array( $this, 'get_report_data' ),
            'permission_callback' => array( $this, 'check_permissions' ),
            'args' => array(
                'type' => array(
                    'required' => true,
                    'validate_callback' => array( $this, 'validate_report_type' )
                )
            )
        ) );

        // Route pour générer un rapport
        register_rest_route( $this->namespace, '/reports/generate', array(
            'methods' => WP_REST_Server::CREATABLE,
            'callback' => array( $this, 'generate_report' ),
            'permission_callback' => array( $this, 'check_permissions' ),
            'args' => array(
                'type' => array(
                    'required' => true,
                    'validate_callback' => array( $this, 'validate_report_type' )
                ),
                'title' => array(
                    'required' => true,
                    'sanitize_callback' => 'sanitize_text_field'
                ),
                'format' => array(
                    'default' => 'pdf',
                    'validate_callback' => array( $this, 'validate_format' )
                ),
                'period_start' => array(
                    'validate_callback' => array( $this, 'validate_date' )
                ),
                'period_end' => array(
                    'validate_callback' => array( $this, 'validate_date' )
                )
            )
        ) );

        // Route pour récupérer l'historique des rapports
        register_rest_route( $this->namespace, '/reports/history', array(
            'methods' => WP_REST_Server::READABLE,
            'callback' => array( $this, 'get_reports_history' ),
            'permission_callback' => array( $this, 'check_permissions' ),
            'args' => array(
                'page' => array(
                    'default' => 1,
                    'validate_callback' => array( $this, 'validate_positive_integer' )
                ),
                'per_page' => array(
                    'default' => 10,
                    'validate_callback' => array( $this, 'validate_positive_integer' )
                ),
                'type' => array(
                    'validate_callback' => array( $this, 'validate_report_type' )
                ),
                'search' => array(
                    'sanitize_callback' => 'sanitize_text_field'
                )
            )
        ) );

        // Route pour supprimer un rapport
        register_rest_route( $this->namespace, '/reports/(?P<id>\d+)', array(
            'methods' => WP_REST_Server::DELETABLE,
            'callback' => array( $this, 'delete_report' ),
            'permission_callback' => array( $this, 'check_permissions' ),
            'args' => array(
                'id' => array(
                    'required' => true,
                    'validate_callback' => array( $this, 'validate_positive_integer' )
                )
            )
        ) );

        // Route pour les statistiques de stockage
        register_rest_route( $this->namespace, '/reports/stats', array(
            'methods' => WP_REST_Server::READABLE,
            'callback' => array( $this, 'get_storage_stats' ),
            'permission_callback' => array( $this, 'check_permissions' )
        ) );

        // Route pour vider le cache
        register_rest_route( $this->namespace, '/reports/cache/clear', array(
            'methods' => WP_REST_Server::CREATABLE,
            'callback' => array( $this, 'clear_cache' ),
            'permission_callback' => array( $this, 'check_permissions' )
        ) );
    }

    /**
     * Récupère les données pour un type de rapport.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Requête REST.
     * @return   WP_REST_Response               Réponse avec les données.
     */
    public function get_report_data( $request ) {
        try {
            $type = $request->get_param( 'type' );
            
            switch ( $type ) {
                case 'performance':
                    $data = $this->data_collector->get_performance_data();
                    break;
                    
                case 'content':
                    $data = $this->data_collector->get_content_audit_data();
                    break;
                    
                case 'technical':
                    $data = $this->data_collector->get_technical_audit_data();
                    break;
                    
                default:
                    return new WP_Error( 'invalid_report_type', 'Type de rapport non supporté', array( 'status' => 400 ) );
            }
            
            return new WP_REST_Response( array(
                'success' => true,
                'data' => $data,
                'type' => $type,
                'generated_at' => current_time( 'mysql' )
            ), 200 );
            
        } catch ( Exception $e ) {
            return new WP_Error( 'data_collection_error', $e->getMessage(), array( 'status' => 500 ) );
        }
    }

    /**
     * Génère un nouveau rapport.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Requête REST.
     * @return   WP_REST_Response               Réponse avec le résultat.
     */
    public function generate_report( $request ) {
        try {
            $type = $request->get_param( 'type' );
            $title = $request->get_param( 'title' );
            $format = $request->get_param( 'format' );
            
            // Collecter les données
            $data = $this->get_report_data_by_type( $type );
            
            if ( is_wp_error( $data ) ) {
                return $data;
            }
            
            // Générer le contenu du rapport selon le format
            $content = $this->generate_report_content( $data, $format, $type );
            
            if ( is_wp_error( $content ) ) {
                return $content;
            }
            
            // Stocker le rapport
            $report_data = array(
                'title' => $title,
                'type' => $type,
                'period_start' => $request->get_param( 'period_start' ),
                'period_end' => $request->get_param( 'period_end' ),
                'scheduled' => 0,
                'frequency' => null,
                'recipients' => null
            );
            
            $report_id = $this->storage->store_report( $report_data, $content, $format );
            
            if ( ! $report_id ) {
                return new WP_Error( 'storage_error', 'Erreur lors du stockage du rapport', array( 'status' => 500 ) );
            }
            
            // Récupérer les informations du rapport créé
            $stored_report = $this->storage->get_report( $report_id );
            
            return new WP_REST_Response( array(
                'success' => true,
                'message' => 'Rapport généré avec succès',
                'report' => $stored_report
            ), 201 );
            
        } catch ( Exception $e ) {
            return new WP_Error( 'generation_error', $e->getMessage(), array( 'status' => 500 ) );
        }
    }

    /**
     * Récupère l'historique des rapports.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Requête REST.
     * @return   WP_REST_Response               Réponse avec l'historique.
     */
    public function get_reports_history( $request ) {
        $page = $request->get_param( 'page' );
        $per_page = $request->get_param( 'per_page' );
        $type = $request->get_param( 'type' );
        $search = $request->get_param( 'search' );

        $args = array(
            'limit' => $per_page,
            'offset' => ( $page - 1 ) * $per_page,
            'type' => $type,
            'search' => $search
        );

        $reports = $this->storage->get_reports( $args );

        return new WP_REST_Response( array(
            'success' => true,
            'reports' => $reports,
            'pagination' => array(
                'page' => $page,
                'per_page' => $per_page,
                'total' => count( $reports ) // À améliorer avec un count séparé
            )
        ), 200 );
    }

    /**
     * Supprime un rapport.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Requête REST.
     * @return   WP_REST_Response               Réponse de suppression.
     */
    public function delete_report( $request ) {
        $report_id = $request->get_param( 'id' );

        $success = $this->storage->delete_report( $report_id );

        if ( $success ) {
            return new WP_REST_Response( array(
                'success' => true,
                'message' => 'Rapport supprimé avec succès'
            ), 200 );
        } else {
            return new WP_Error( 'deletion_error', 'Erreur lors de la suppression du rapport', array( 'status' => 500 ) );
        }
    }

    /**
     * Récupère les statistiques de stockage.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Requête REST.
     * @return   WP_REST_Response               Réponse avec les statistiques.
     */
    public function get_storage_stats( $request ) {
        $stats = $this->storage->get_storage_stats();

        return new WP_REST_Response( array(
            'success' => true,
            'stats' => $stats
        ), 200 );
    }

    /**
     * Vide le cache des rapports.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Requête REST.
     * @return   WP_REST_Response               Réponse de vidage du cache.
     */
    public function clear_cache( $request ) {
        $this->data_collector->clear_cache();

        return new WP_REST_Response( array(
            'success' => true,
            'message' => 'Cache vidé avec succès'
        ), 200 );
    }

    /**
     * Récupère les données par type de rapport.
     *
     * @since    1.2.0
     * @param    string    $type    Type de rapport.
     * @return   array|WP_Error     Données ou erreur.
     */
    private function get_report_data_by_type( $type ) {
        switch ( $type ) {
            case 'performance':
                return $this->data_collector->get_performance_data();

            case 'content':
                return $this->data_collector->get_content_audit_data();

            case 'technical':
                return $this->data_collector->get_technical_audit_data();

            default:
                return new WP_Error( 'invalid_type', 'Type de rapport non supporté' );
        }
    }

    /**
     * Génère le contenu du rapport selon le format.
     *
     * @since    1.2.0
     * @param    array     $data      Données du rapport.
     * @param    string    $format    Format de sortie.
     * @param    string    $type      Type de rapport.
     * @return   string|WP_Error      Contenu généré ou erreur.
     */
    private function generate_report_content( $data, $format, $type ) {
        try {
            // Enrichir les données avec les générateurs avancés
            $enriched_data = $this->enrich_report_data( $data, $type );

            switch ( $format ) {
                case 'html':
                    return $this->template_generator->generate_html_report( $enriched_data, $type );

                case 'csv':
                    return $this->generate_csv_report( $enriched_data, $type );

                case 'pdf':
                    return $this->generate_pdf_report( $enriched_data, $type );

                default:
                    return new WP_Error( 'invalid_format', 'Format non supporté' );
            }
        } catch ( Exception $e ) {
            return new WP_Error( 'generation_error', 'Erreur lors de la génération: ' . $e->getMessage() );
        }
    }

    /**
     * Enrichit les données du rapport avec les générateurs avancés.
     *
     * @since    1.2.0
     * @param    array     $data    Données de base.
     * @param    string    $type    Type de rapport.
     * @return   array              Données enrichies.
     */
    private function enrich_report_data( $data, $type ) {
        switch ( $type ) {
            case 'performance':
                // Utiliser le générateur de performance avancé (Phase 2)
                return $this->performance_generator->generate();

            case 'keywords':
                // Utiliser le générateur de mots-clés avec GSC (Phase 3)
                return $this->keywords_generator->generate();

            case 'content':
                // Enrichir avec des données de contenu avancées
                return $this->enrich_content_data( $data );

            case 'technical':
                // Enrichir avec des données techniques avancées
                return $this->enrich_technical_data( $data );

            case 'local':
                // Générateur SEO local (à implémenter)
                return $this->enrich_local_data( $data );

            case 'ecommerce':
                // Générateur e-commerce (à implémenter)
                return $this->enrich_ecommerce_data( $data );

            default:
                // Retourner les données de base
                return $data;
        }
    }

    /**
     * Enrichit les données de contenu.
     *
     * @since    1.2.0
     * @param    array    $data    Données de base.
     * @return   array             Données enrichies.
     */
    private function enrich_content_data( $data ) {
        // Ajouter des métadonnées
        $data['metadata'] = array(
            'title' => 'Rapport d\'Audit de Contenu',
            'subtitle' => 'Analyse complète du contenu et des méta-données',
            'generated_at' => current_time( 'mysql' ),
            'generated_by' => wp_get_current_user()->display_name,
            'site_url' => home_url(),
            'site_name' => get_bloginfo( 'name' ),
            'period' => array(
                'start' => date( 'Y-m-d', strtotime( '-30 days' ) ),
                'end' => date( 'Y-m-d' ),
                'label' => '30 derniers jours'
            )
        );

        return $data;
    }

    /**
     * Enrichit les données techniques.
     *
     * @since    1.2.0
     * @param    array    $data    Données de base.
     * @return   array             Données enrichies.
     */
    private function enrich_technical_data( $data ) {
        // Ajouter des métadonnées
        $data['metadata'] = array(
            'title' => 'Rapport d\'Audit Technique',
            'subtitle' => 'Analyse technique et performance du site',
            'generated_at' => current_time( 'mysql' ),
            'generated_by' => wp_get_current_user()->display_name,
            'site_url' => home_url(),
            'site_name' => get_bloginfo( 'name' ),
            'period' => array(
                'start' => date( 'Y-m-d', strtotime( '-30 days' ) ),
                'end' => date( 'Y-m-d' ),
                'label' => '30 derniers jours'
            )
        );

        return $data;
    }

    /**
     * Enrichit les données SEO local.
     *
     * @since    1.2.0
     * @param    array    $data    Données de base.
     * @return   array             Données enrichies.
     */
    private function enrich_local_data( $data ) {
        // À implémenter dans une version future
        $data['metadata'] = array(
            'title' => 'Rapport SEO Local',
            'subtitle' => 'Analyse du référencement local',
            'generated_at' => current_time( 'mysql' ),
            'generated_by' => wp_get_current_user()->display_name,
            'site_url' => home_url(),
            'site_name' => get_bloginfo( 'name' )
        );

        return $data;
    }

    /**
     * Enrichit les données e-commerce.
     *
     * @since    1.2.0
     * @param    array    $data    Données de base.
     * @return   array             Données enrichies.
     */
    private function enrich_ecommerce_data( $data ) {
        // À implémenter dans une version future
        $data['metadata'] = array(
            'title' => 'Rapport SEO E-commerce',
            'subtitle' => 'Analyse SEO des produits et catégories',
            'generated_at' => current_time( 'mysql' ),
            'generated_by' => wp_get_current_user()->display_name,
            'site_url' => home_url(),
            'site_name' => get_bloginfo( 'name' )
        );

        return $data;
    }

    /**
     * Génère un rapport HTML.
     *
     * @since    1.2.0
     * @param    array     $data    Données du rapport.
     * @param    string    $type    Type de rapport.
     * @return   string             Contenu HTML.
     */
    private function generate_html_report( $data, $type ) {
        $html = '<!DOCTYPE html><html><head>';
        $html .= '<meta charset="UTF-8">';
        $html .= '<title>Rapport Boss SEO - ' . ucfirst( $type ) . '</title>';
        $html .= '<style>body{font-family:Arial,sans-serif;margin:40px;} table{border-collapse:collapse;width:100%;} th,td{border:1px solid #ddd;padding:8px;text-align:left;} th{background-color:#f2f2f2;}</style>';
        $html .= '</head><body>';

        $html .= '<h1>Rapport Boss SEO - ' . ucfirst( $type ) . '</h1>';
        $html .= '<p>Généré le : ' . current_time( 'Y-m-d H:i:s' ) . '</p>';

        // Contenu spécifique selon le type
        if ( $type === 'performance' && isset( $data['global_stats'] ) ) {
            $stats = $data['global_stats'];
            $html .= '<h2>Statistiques Globales</h2>';
            $html .= '<table>';
            $html .= '<tr><th>Métrique</th><th>Valeur</th></tr>';
            $html .= '<tr><td>Contenu Total</td><td>' . ( $stats['totalContent'] ?? 0 ) . '</td></tr>';
            $html .= '<tr><td>Contenu Optimisé</td><td>' . ( $stats['optimizedContent'] ?? 0 ) . '</td></tr>';
            $html .= '<tr><td>Score Moyen</td><td>' . ( $stats['averageScore'] ?? 0 ) . '%</td></tr>';
            $html .= '</table>';
        }

        $html .= '</body></html>';

        return $html;
    }

    /**
     * Génère un rapport CSV.
     *
     * @since    1.2.0
     * @param    array     $data    Données du rapport.
     * @param    string    $type    Type de rapport.
     * @return   string             Contenu CSV.
     */
    private function generate_csv_report( $data, $type ) {
        $csv = "Rapport Boss SEO - " . ucfirst( $type ) . "\n";
        $csv .= "Généré le," . current_time( 'Y-m-d H:i:s' ) . "\n\n";

        if ( $type === 'performance' && isset( $data['global_stats'] ) ) {
            $stats = $data['global_stats'];
            $csv .= "Métrique,Valeur\n";
            $csv .= "Contenu Total," . ( $stats['totalContent'] ?? 0 ) . "\n";
            $csv .= "Contenu Optimisé," . ( $stats['optimizedContent'] ?? 0 ) . "\n";
            $csv .= "Score Moyen," . ( $stats['averageScore'] ?? 0 ) . "%\n";
        }

        return $csv;
    }

    /**
     * Génère un rapport PDF (version basique).
     *
     * @since    1.2.0
     * @param    array     $data    Données du rapport.
     * @param    string    $type    Type de rapport.
     * @return   string             Contenu PDF (pour l'instant HTML).
     */
    private function generate_pdf_report( $data, $type ) {
        // Pour l'instant, retourner du HTML
        // Dans la Phase 2, on intégrera une vraie librairie PDF
        return $this->generate_html_report( $data, $type );
    }

    /**
     * Vérifie les permissions.
     *
     * @since    1.2.0
     * @return   bool    Permissions accordées.
     */
    public function check_permissions() {
        return current_user_can( 'manage_options' );
    }

    /**
     * Valide le type de rapport.
     *
     * @since    1.2.0
     * @param    string    $value    Valeur à valider.
     * @return   bool               Validation réussie.
     */
    public function validate_report_type( $value ) {
        $allowed_types = array( 'performance', 'content', 'technical', 'keywords', 'local', 'ecommerce' );
        return in_array( $value, $allowed_types );
    }

    /**
     * Valide le format.
     *
     * @since    1.2.0
     * @param    string    $value    Valeur à valider.
     * @return   bool               Validation réussie.
     */
    public function validate_format( $value ) {
        $allowed_formats = array( 'pdf', 'csv', 'html' );
        return in_array( $value, $allowed_formats );
    }

    /**
     * Valide une date.
     *
     * @since    1.2.0
     * @param    string    $value    Valeur à valider.
     * @return   bool               Validation réussie.
     */
    public function validate_date( $value ) {
        return strtotime( $value ) !== false;
    }

    /**
     * Valide un entier positif.
     *
     * @since    1.2.0
     * @param    mixed     $value    Valeur à valider.
     * @return   bool               Validation réussie.
     */
    public function validate_positive_integer( $value ) {
        return is_numeric( $value ) && intval( $value ) > 0;
    }
}
