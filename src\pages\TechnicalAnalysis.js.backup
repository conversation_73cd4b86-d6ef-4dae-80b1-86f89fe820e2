import { useState, useEffect } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  <PERSON><PERSON>,
  <PERSON>,
  CardB<PERSON>,
  Card<PERSON>eader,
  <PERSON><PERSON>ooter,
  Dashicon,
  Notice,
  Panel,
  PanelBody,
  PanelRow,
  Spinner,
  TabPanel,
  ToggleControl,
  Tooltip
} from '@wordpress/components';

// Composants internes
import OverviewSection from '../components/technical/OverviewSection';
import IssuesSection from '../components/technical/IssuesSection';
import PerformanceSection from '../components/technical/PerformanceSection';
import HistorySection from '../components/technical/HistorySection';
import CoreWebVitalsCard from '../components/technical/CoreWebVitalsCard';
import SchemaAnalysisSection from '../components/technical/SchemaAnalysisSection';
import HreflangAnalysisSection from '../components/technical/HreflangAnalysisSection';

// Services
import TechnicalAnalysisService from '../services/TechnicalAnalysisService';
import PerformanceService from '../services/PerformanceService';

/**
 * Composant principal du module d'analyse technique
 */
const TechnicalAnalysis = () => {
  // États pour les données et l'interface
  const [isLoading, setIsLoading] = useState(false);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');
  const [analysisData, setAnalysisData] = useState(null);
  const [analysisHistory, setAnalysisHistory] = useState([]);
  const [expandedIssues, setExpandedIssues] = useState({});

  // Données fictives pour l'analyse technique
  const mockAnalysisData = {
    score: 72,
    lastAnalysis: '2023-06-15T14:30:00',
    categories: {
      performance: {
        score: 65,
        issues: 4,
        color: 'yellow'
      },
      seo: {
        score: 82,
        issues: 2,
        color: 'green'
      },
      accessibility: {
        score: 78,
        issues: 3,
        color: 'green'
      },
      bestPractices: {
        score: 60,
        issues: 5,
        color: 'yellow'
      },
      security: {
        score: 90,
        issues: 1,
        color: 'green'
      }
    },
    issues: {
      critical: [
        {
          id: 'c1',
          title: 'Temps de chargement trop long',
          description: 'Le temps de chargement moyen de votre site est de 5.2 secondes, ce qui est considéré comme lent.',
          impact: 'Un temps de chargement lent affecte négativement l\'expérience utilisateur et le référencement. Google favorise les sites rapides dans ses résultats de recherche.',
          solution: 'Optimisez les images, utilisez la mise en cache du navigateur, minimisez les fichiers CSS et JavaScript, et envisagez d\'utiliser un CDN.',
          category: 'performance',
          severity: 'critical',
          status: 'open',
          url: '/performance-optimization'
        },
        {
          id: 'c2',
          title: 'Certificat SSL manquant ou invalide',
          description: 'Votre site n\'utilise pas HTTPS ou le certificat SSL est invalide.',
          impact: 'Les sites sans HTTPS sont marqués comme non sécurisés par les navigateurs, ce qui peut réduire la confiance des utilisateurs et affecter le référencement.',
          solution: 'Installez un certificat SSL valide et configurez votre site pour utiliser HTTPS par défaut. Redirigez tout le trafic HTTP vers HTTPS.',
          category: 'security',
          severity: 'critical',
          status: 'open',
          url: '/security-settings'
        }
      ],
      errors: [
        {
          id: 'e1',
          title: 'Images sans attributs alt',
          description: '12 images sur votre site n\'ont pas d\'attributs alt définis.',
          impact: 'Les attributs alt sont essentiels pour l\'accessibilité et le référencement des images. Sans eux, les moteurs de recherche ne peuvent pas comprendre le contenu de vos images.',
          solution: 'Ajoutez des attributs alt descriptifs à toutes vos images. Utilisez des descriptions concises qui reflètent le contenu de l\'image.',
          category: 'seo',
          severity: 'error',
          status: 'open',
          url: '/image-optimization'
        },
        {
          id: 'e2',
          title: 'Liens brisés détectés',
          description: '5 liens sur votre site pointent vers des pages qui n\'existent plus ou retournent des erreurs.',
          impact: 'Les liens brisés créent une mauvaise expérience utilisateur et peuvent affecter négativement votre référencement.',
          solution: 'Identifiez et corrigez tous les liens brisés. Redirigez les anciennes URLs vers de nouvelles pages pertinentes ou supprimez les liens obsolètes.',
          category: 'seo',
          severity: 'error',
          status: 'open',
          url: '/broken-links'
        },
        {
          id: 'e3',
          title: 'Fichiers JavaScript bloquant le rendu',
          description: 'Plusieurs fichiers JavaScript bloquent le rendu de votre page.',
          impact: 'Les scripts bloquants ralentissent le chargement de votre page, ce qui affecte l\'expérience utilisateur et les métriques de performance.',
          solution: 'Chargez les scripts JavaScript de manière asynchrone ou différée. Placez les scripts critiques en ligne et les scripts non critiques en bas de page.',
          category: 'performance',
          severity: 'error',
          status: 'open',
          url: '/javascript-optimization'
        }
      ],
      warnings: [
        {
          id: 'w1',
          title: 'Balises meta description manquantes',
          description: '8 pages n\'ont pas de balises meta description définies.',
          impact: 'Les meta descriptions sont utilisées par les moteurs de recherche pour afficher des extraits dans les résultats de recherche. Sans elles, Google générera automatiquement un extrait qui peut ne pas être optimal.',
          solution: 'Ajoutez des meta descriptions uniques et attrayantes à toutes vos pages. Limitez-les à environ 155-160 caractères.',
          category: 'seo',
          severity: 'warning',
          status: 'open',
          url: '/meta-descriptions'
        },
        {
          id: 'w2',
          title: 'Texte trop petit pour être lisible',
          description: 'Certaines parties de votre site utilisent une taille de police inférieure à 12px, ce qui peut être difficile à lire.',
          impact: 'Un texte trop petit affecte l\'accessibilité et l\'expérience utilisateur, particulièrement sur les appareils mobiles.',
          solution: 'Augmentez la taille de police à au moins 16px pour le texte principal et 14px pour le texte secondaire. Utilisez des unités relatives comme em ou rem.',
          category: 'accessibility',
          severity: 'warning',
          status: 'open',
          url: '/typography-guidelines'
        },
        {
          id: 'w3',
          title: 'Contraste de couleur insuffisant',
          description: 'Certains éléments de votre site n\'ont pas un contraste de couleur suffisant entre le texte et l\'arrière-plan.',
          impact: 'Un faible contraste rend le contenu difficile à lire pour de nombreux utilisateurs, en particulier ceux ayant une déficience visuelle.',
          solution: 'Assurez-vous que tous les textes ont un rapport de contraste d\'au moins 4.5:1 pour le texte normal et 3:1 pour le grand texte.',
          category: 'accessibility',
          severity: 'warning',
          status: 'open',
          url: '/accessibility-guidelines'
        },
        {
          id: 'w4',
          title: 'Taille de police non responsive',
          description: 'Votre site utilise des tailles de police fixes qui ne s\'adaptent pas aux différentes tailles d\'écran.',
          impact: 'Les tailles de police non responsives peuvent rendre votre site difficile à lire sur certains appareils.',
          solution: 'Utilisez des unités relatives comme em ou rem pour les tailles de police, et ajustez-les avec des media queries pour différentes tailles d\'écran.',
          category: 'accessibility',
          severity: 'warning',
          status: 'open',
          url: '/responsive-design'
        }
      ],
      improvements: [
        {
          id: 'i1',
          title: 'Optimisation des images',
          description: 'Les images de votre site pourraient être davantage optimisées pour réduire leur taille sans perte de qualité visible.',
          impact: 'Des images plus légères améliorent le temps de chargement de la page et réduisent la consommation de données pour vos utilisateurs.',
          solution: 'Compressez vos images, utilisez des formats modernes comme WebP, et implémentez le chargement paresseux (lazy loading).',
          category: 'performance',
          severity: 'improvement',
          status: 'open',
          url: '/image-optimization'
        },
        {
          id: 'i2',
          title: 'Mise en cache du navigateur',
          description: 'Votre site ne définit pas correctement les en-têtes de mise en cache pour les ressources statiques.',
          impact: 'Sans mise en cache appropriée, les visiteurs récurrents doivent télécharger à nouveau toutes les ressources, ce qui ralentit le chargement des pages.',
          solution: 'Configurez les en-têtes de cache appropriés pour les ressources statiques comme les images, CSS et JavaScript.',
          category: 'performance',
          severity: 'improvement',
          status: 'open',
          url: '/caching-optimization'
        },
        {
          id: 'i3',
          title: 'Structure des titres incohérente',
          description: 'La hiérarchie des titres (H1, H2, H3, etc.) n\'est pas cohérente sur toutes les pages.',
          impact: 'Une structure de titres incohérente peut confondre les moteurs de recherche quant à l\'importance relative du contenu.',
          solution: 'Assurez-vous que chaque page a un seul H1, suivi de H2 pour les sections principales, puis H3 pour les sous-sections, etc.',
          category: 'seo',
          severity: 'improvement',
          status: 'open',
          url: '/content-structure'
        }
      ],
      successes: [
        {
          id: 's1',
          title: 'Site mobile-friendly',
          description: 'Votre site s\'adapte bien aux appareils mobiles.',
          impact: 'Un site responsive améliore l\'expérience utilisateur sur tous les appareils et est favorisé par Google pour le référencement mobile.',
          category: 'seo',
          severity: 'success',
          status: 'success'
        },
        {
          id: 's2',
          title: 'Sitemap XML valide',
          description: 'Votre sitemap XML est correctement configuré et accessible.',
          impact: 'Un sitemap bien structuré aide les moteurs de recherche à découvrir et indexer votre contenu plus efficacement.',
          category: 'seo',
          severity: 'success',
          status: 'success'
        },
        {
          id: 's3',
          title: 'Fichier robots.txt bien configuré',
          description: 'Votre fichier robots.txt est correctement configuré pour guider les robots des moteurs de recherche.',
          impact: 'Un fichier robots.txt bien configuré aide à contrôler quelles parties de votre site sont explorées et indexées par les moteurs de recherche.',
          category: 'seo',
          severity: 'success',
          status: 'success'
        }
      ]
    },
    performance: {
      coreWebVitals: {
        lcp: {
          name: 'LCP',
          value: 3.2,
          unit: 's',
          status: 'needs-improvement',
          description: 'Largest Contentful Paint',
          score: 65
        },
        inp: {
          name: 'INP',
          value: 180,
          unit: 'ms',
          status: 'good',
          description: 'Interaction to Next Paint',
          score: 85
        },
        fid: {
          name: 'FID',
          value: 85,
          unit: 'ms',
          status: 'good',
          description: 'First Input Delay (obsolète)',
          score: 80
        },
        cls: {
          name: 'CLS',
          value: 0.12,
          unit: '',
          status: 'needs-improvement',
          description: 'Cumulative Layout Shift',
          score: 70
        },
        ttfb: {
          name: 'TTFB',
          value: 520,
          unit: 'ms',
          status: 'needs-improvement',
          description: 'Time to First Byte',
          score: 75
        },
        fcp: {
          name: 'FCP',
          value: 1.8,
          unit: 's',
          status: 'good',
          description: 'First Contentful Paint',
          score: 90
        }
      },
      history: [
        { date: '2023-06-15', score: 65 },
        { date: '2023-05-15', score: 62 },
        { date: '2023-04-15', score: 58 },
        { date: '2023-03-15', score: 55 },
        { date: '2023-02-15', score: 50 }
      ]
    }
  };

  // Données fictives pour l'historique des analyses
  const mockAnalysisHistory = [
    {
      id: 'a1',
      date: '2023-06-15T14:30:00',
      score: 72,
      issues: {
        critical: 2,
        errors: 3,
        warnings: 4,
        improvements: 3,
        successes: 3
      }
    },
    {
      id: 'a2',
      date: '2023-05-15T10:15:00',
      score: 68,
      issues: {
        critical: 3,
        errors: 4,
        warnings: 5,
        improvements: 3,
        successes: 2
      }
    },
    {
      id: 'a3',
      date: '2023-04-15T09:45:00',
      score: 65,
      issues: {
        critical: 3,
        errors: 5,
        warnings: 6,
        improvements: 4,
        successes: 2
      }
    },
    {
      id: 'a4',
      date: '2023-03-15T11:20:00',
      score: 60,
      issues: {
        critical: 4,
        errors: 6,
        warnings: 7,
        improvements: 5,
        successes: 1
      }
    }
  ];

  // Charger les données au montage du composant
  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true);

        // Charger les données de la dernière analyse
        const analysisResponse = await TechnicalAnalysisService.getLatestAnalysis();
        setAnalysisData(analysisResponse);

        // Charger l'historique des analyses
        const historyResponse = await TechnicalAnalysisService.getAnalysisHistory();
        setAnalysisHistory(historyResponse);

        setIsLoading(false);
      } catch (error) {
        console.error('Erreur lors du chargement des données:', error);

        // En cas d'erreur, utiliser les données fictives pour la démo
        setAnalysisData(mockAnalysisData);
        setAnalysisHistory(mockAnalysisHistory);
        setIsLoading(false);
      }
    };

    loadData();
  }, []);

  // Fonction pour lancer une nouvelle analyse
  const handleStartAnalysis = async () => {
    try {
      setIsAnalyzing(true);

      // Lancer une nouvelle analyse
      const analysisResponse = await TechnicalAnalysisService.startAnalysis();

      if (analysisResponse.success) {
        // Récupérer les résultats de l'analyse
        const newAnalysisData = await TechnicalAnalysisService.getLatestAnalysis();
        setAnalysisData(newAnalysisData);

        // Récupérer l'historique mis à jour
        const historyResponse = await TechnicalAnalysisService.getAnalysisHistory();
        setAnalysisHistory(historyResponse);
      } else {
        // En cas d'erreur, afficher un message
        console.error('Erreur lors de l\'analyse:', analysisResponse.message);

        // Utiliser les données fictives pour la démo
        setAnalysisData(mockAnalysisData);

        // Ajouter l'analyse fictive à l'historique
        const newAnalysis = {
          id: `a${Date.now()}`,
          date: new Date().toISOString(),
          score: mockAnalysisData.score,
          issues: {
            critical: mockAnalysisData.issues.critical.length,
            errors: mockAnalysisData.issues.errors.length,
            warnings: mockAnalysisData.issues.warnings.length,
            improvements: mockAnalysisData.issues.improvements.length,
            successes: mockAnalysisData.issues.successes.length
          }
        };

        setAnalysisHistory([newAnalysis, ...analysisHistory]);
      }
    } catch (error) {
      console.error('Erreur lors de l\'analyse:', error);

      // Utiliser les données fictives pour la démo
      setAnalysisData(mockAnalysisData);

      // Ajouter l'analyse fictive à l'historique
      const newAnalysis = {
        id: `a${Date.now()}`,
        date: new Date().toISOString(),
        score: mockAnalysisData.score,
        issues: {
          critical: mockAnalysisData.issues.critical.length,
          errors: mockAnalysisData.issues.errors.length,
          warnings: mockAnalysisData.issues.warnings.length,
          improvements: mockAnalysisData.issues.improvements.length,
          successes: mockAnalysisData.issues.successes.length
        }
      };

      setAnalysisHistory([newAnalysis, ...analysisHistory]);
    } finally {
      setIsAnalyzing(false);
    }
  };

  // Fonction pour marquer un problème comme résolu
  const handleMarkAsResolved = async (issueId, issueType) => {
    if (!analysisData || !analysisData.issues[issueType]) return;

    try {
      // Appeler le service pour marquer le problème comme résolu
      const response = await TechnicalAnalysisService.markIssueAsResolved(issueId);

      if (response.success) {
        // Mettre à jour l'état local
        const updatedIssues = { ...analysisData.issues };
        updatedIssues[issueType] = updatedIssues[issueType].map(issue =>
          issue.id === issueId ? { ...issue, status: 'resolved' } : issue
        );

        setAnalysisData({
          ...analysisData,
          issues: updatedIssues
        });
      } else {
        console.error('Erreur lors du marquage du problème comme résolu:', response.message);
      }
    } catch (error) {
      console.error('Erreur lors du marquage du problème comme résolu:', error);

      // En cas d'erreur, mettre quand même à jour l'interface pour une meilleure expérience utilisateur
      const updatedIssues = { ...analysisData.issues };
      updatedIssues[issueType] = updatedIssues[issueType].map(issue =>
        issue.id === issueId ? { ...issue, status: 'resolved' } : issue
      );

      setAnalysisData({
        ...analysisData,
        issues: updatedIssues
      });
    }
  };

  // Fonction pour basculer l'état d'expansion d'un problème
  const toggleIssueExpanded = (issueId) => {
    setExpandedIssues({
      ...expandedIssues,
      [issueId]: !expandedIssues[issueId]
    });
  };

  // Rendu du composant
  return (
    <div className="boss-flex boss-flex-col boss-min-h-screen">
      <div className="boss-p-6">
        <div className="boss-mb-6">
          <h1 className="boss-text-2xl boss-font-bold boss-text-boss-dark boss-mb-2">
            {__('Analyse technique', 'boss-seo')}
          </h1>
          <p className="boss-text-boss-gray">
            {__('Auditez les aspects techniques SEO de votre site pour améliorer son référencement', 'boss-seo')}
          </p>
        </div>

        {isLoading ? (
          <div className="boss-flex boss-justify-center boss-items-center boss-p-12">
            <Spinner />
          </div>
        ) : (
          <>
            {/* Onglets principaux */}
            <TabPanel
              className="boss-mb-6"
              activeClass="boss-bg-white boss-border-t boss-border-l boss-border-r boss-border-gray-200 boss-rounded-t-lg"
              tabs={[
                {
                  name: 'overview',
                  title: __('Aperçu', 'boss-seo'),
                  className: 'boss-font-medium boss-px-4 boss-py-2'
                },
                {
                  name: 'issues',
                  title: __('Problèmes détectés', 'boss-seo'),
                  className: 'boss-font-medium boss-px-4 boss-py-2'
                },
                {
                  name: 'performance',
                  title: __('Performance', 'boss-seo'),
                  className: 'boss-font-medium boss-px-4 boss-py-2'
                },
                {
                  name: 'schema',
                  title: __('Schema Markup', 'boss-seo'),
                  className: 'boss-font-medium boss-px-4 boss-py-2'
                },
                {
                  name: 'hreflang',
                  title: __('Hreflang', 'boss-seo'),
                  className: 'boss-font-medium boss-px-4 boss-py-2'
                },
                {
                  name: 'history',
                  title: __('Historique', 'boss-seo'),
                  className: 'boss-font-medium boss-px-4 boss-py-2'
                }
              ]}
              onSelect={(tabName) => setActiveTab(tabName)}
            >
              {(tab) => {
                if (tab.name === 'overview') {
                  return (
                    <OverviewSection
                      data={analysisData}
                      onStartAnalysis={handleStartAnalysis}
                      isAnalyzing={isAnalyzing}
                    />
                  );
                } else if (tab.name === 'issues') {
                  return (
                    <IssuesSection
                      issues={analysisData.issues}
                      expandedIssues={expandedIssues}
                      onToggleExpand={toggleIssueExpanded}
                      onMarkResolved={handleMarkAsResolved}
                    />
                  );
                } else if (tab.name === 'performance') {
                  return (
                    <div className="boss-space-y-6">
                      <CoreWebVitalsCard
                        data={analysisData.performance.coreWebVitals}
                        isLoading={false}
                        onRefresh={handleStartAnalysis}
                      />
                      <PerformanceSection
                        performance={analysisData.performance}
                      />
                    </div>
                  );
                } else if (tab.name === 'schema') {
                  return <SchemaAnalysisSection />;
                } else if (tab.name === 'hreflang') {
                  return <HreflangAnalysisSection />;
                } else if (tab.name === 'history') {
                  return (
                    <HistorySection
                      history={analysisHistory}
                    />
                  );
                }
              }}
            </TabPanel>
          </>
        )}
      </div>
    </div>
  );
};

export default TechnicalAnalysis;
