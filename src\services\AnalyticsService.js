/**
 * Service Analytics pour Boss SEO
 *
 * Gère les communications avec l'API REST WordPress pour les données Analytics
 */

import apiFetch from '@wordpress/api-fetch';

class AnalyticsService {
  /**
   * Constructeur
   */
  constructor() {
    this.baseUrl = '/boss-seo/v1';
    this.cache = new Map(); // Cache local pour éviter les appels répétés
    this.cacheTimeout = 30000; // 30 secondes
  }

  // ==========================================
  // MÉTHODES D'AUTHENTIFICATION
  // ==========================================

  /**
   * Obtient le statut d'authentification des services
   *
   * @returns {Promise} Statut des services GA4 et GSC
   */
  async getAuthStatus() {
    try {
      const response = await apiFetch({
        path: `${this.baseUrl}/auth/status`,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération du statut d\'authentification:', error);
      throw error;
    }
  }

  /**
   * Gère le callback OAuth depuis le proxy
   *
   * @param {string} service - Service (ga4 ou gsc)
   * @param {Object} tokens - Tokens OAuth reçus
   * @returns {Promise} Résultat de l'authentification
   */
  async handleOAuthCallback(service, tokens) {
    try {
      const response = await apiFetch({
        path: `${this.baseUrl}/auth/callback`,
        method: 'POST',
        data: {
          service: service,
          tokens: tokens
        }
      });
      return response;
    } catch (error) {
      console.error('Erreur lors du callback OAuth:', error);
      throw error;
    }
  }

  /**
   * Déconnecte un service
   *
   * @param {string} service - Service à déconnecter (ga4 ou gsc)
   * @returns {Promise} Résultat de la déconnexion
   */
  async disconnectService(service) {
    try {
      const response = await apiFetch({
        path: `${this.baseUrl}/auth/disconnect/${service}`,
        method: 'DELETE'
      });
      return response;
    } catch (error) {
      console.error(`Erreur lors de la déconnexion du service ${service}:`, error);
      throw error;
    }
  }

  // ==========================================
  // MÉTHODES GOOGLE ANALYTICS 4
  // ==========================================

  /**
   * Récupère les données d'aperçu GA4
   *
   * @param {string} propertyId - ID de la propriété GA4
   * @param {string} dateRange - Plage de dates
   * @returns {Promise} Données d'aperçu GA4
   */
  async getGA4Overview(propertyId = null, dateRange = 'last30days') {
    try {
      const params = new URLSearchParams();
      if (propertyId) params.append('property_id', propertyId);
      params.append('date_range', dateRange);

      const response = await apiFetch({
        path: `${this.baseUrl}/ga4/overview?${params.toString()}`,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des données d\'aperçu GA4:', error);
      throw error;
    }
  }

  /**
   * Récupère les données de trafic GA4
   *
   * @param {string} propertyId - ID de la propriété GA4
   * @param {string} dateRange - Plage de dates
   * @returns {Promise} Données de trafic GA4
   */
  async getGA4Traffic(propertyId = null, dateRange = 'last30days') {
    try {
      const params = new URLSearchParams();
      if (propertyId) params.append('property_id', propertyId);
      params.append('date_range', dateRange);

      const response = await apiFetch({
        path: `${this.baseUrl}/ga4/traffic?${params.toString()}`,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des données de trafic GA4:', error);
      throw error;
    }
  }

  /**
   * Récupère les données des pages GA4
   *
   * @param {string} propertyId - ID de la propriété GA4
   * @param {string} dateRange - Plage de dates
   * @param {number} limit - Limite de résultats
   * @returns {Promise} Données des pages GA4
   */
  async getGA4Pages(propertyId = null, dateRange = 'last30days', limit = 50) {
    try {
      const params = new URLSearchParams();
      if (propertyId) params.append('property_id', propertyId);
      params.append('date_range', dateRange);
      params.append('limit', limit.toString());

      const response = await apiFetch({
        path: `${this.baseUrl}/ga4/pages?${params.toString()}`,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des données des pages GA4:', error);
      throw error;
    }
  }

  // ==========================================
  // MÉTHODES GOOGLE SEARCH CONSOLE
  // ==========================================

  /**
   * Récupère les données de mots-clés GSC
   *
   * @param {string} propertyUrl - URL de la propriété GSC
   * @param {string} dateRange - Plage de dates
   * @param {number} limit - Limite de résultats
   * @returns {Promise} Données de mots-clés GSC
   */
  async getGSCKeywords(propertyUrl = null, dateRange = 'last30days', limit = 100) {
    try {
      const params = new URLSearchParams();
      if (propertyUrl) params.append('property_url', propertyUrl);
      params.append('date_range', dateRange);
      params.append('limit', limit.toString());

      const response = await apiFetch({
        path: `${this.baseUrl}/gsc/keywords?${params.toString()}`,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des mots-clés GSC:', error);
      throw error;
    }
  }

  /**
   * Récupère les données de performance GSC
   *
   * @param {string} propertyUrl - URL de la propriété GSC
   * @param {string} dateRange - Plage de dates
   * @returns {Promise} Données de performance GSC
   */
  async getGSCPerformance(propertyUrl = null, dateRange = 'last30days') {
    try {
      const params = new URLSearchParams();
      if (propertyUrl) params.append('property_url', propertyUrl);
      params.append('date_range', dateRange);

      const response = await apiFetch({
        path: `${this.baseUrl}/gsc/performance?${params.toString()}`,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des données de performance GSC:', error);
      throw error;
    }
  }

  // ==========================================
  // MÉTHODES DE GESTION
  // ==========================================

  /**
   * Force la synchronisation des données
   *
   * @returns {Promise} Résultat de la synchronisation
   */
  async forceSyncData() {
    try {
      const response = await apiFetch({
        path: `${this.baseUrl}/analytics/sync`,
        method: 'POST'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la synchronisation forcée:', error);
      throw error;
    }
  }

  /**
   * Vide le cache Analytics
   *
   * @returns {Promise} Résultat du vidage de cache
   */
  async clearCache() {
    try {
      // Vider le cache local d'abord
      this.cache.clear();

      const response = await apiFetch({
        path: `${this.baseUrl}/analytics/cache/clear`,
        method: 'DELETE'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors du vidage du cache:', error);
      throw error;
    }
  }

  /**
   * Vide le cache local uniquement
   */
  clearLocalCache() {
    this.cache.clear();
    console.log('✅ Cache local Analytics vidé');
  }

  // ==========================================
  // MÉTHODES UTILITAIRES
  // ==========================================

  /**
   * Combine les données GA4 et GSC pour le dashboard
   *
   * @param {string} dateRange - Plage de dates
   * @returns {Promise} Données combinées pour le dashboard
   */
  async getDashboardData(dateRange = 'last30days') {
    try {
      // Récupérer les données en parallèle
      const [ga4Overview, ga4Traffic, gscKeywords, gscPerformance] = await Promise.allSettled([
        this.getGA4Overview(null, dateRange),
        this.getGA4Traffic(null, dateRange),
        this.getGSCKeywords(null, dateRange, 20),
        this.getGSCPerformance(null, dateRange)
      ]);

      // Traiter les résultats
      const dashboardData = {
        ga4: {
          overview: ga4Overview.status === 'fulfilled' ? ga4Overview.value : null,
          traffic: ga4Traffic.status === 'fulfilled' ? ga4Traffic.value : null
        },
        gsc: {
          keywords: gscKeywords.status === 'fulfilled' ? gscKeywords.value : null,
          performance: gscPerformance.status === 'fulfilled' ? gscPerformance.value : null
        },
        errors: []
      };

      // Collecter les erreurs
      if (ga4Overview.status === 'rejected') {
        dashboardData.errors.push({ service: 'GA4 Overview', error: ga4Overview.reason });
      }
      if (ga4Traffic.status === 'rejected') {
        dashboardData.errors.push({ service: 'GA4 Traffic', error: ga4Traffic.reason });
      }
      if (gscKeywords.status === 'rejected') {
        dashboardData.errors.push({ service: 'GSC Keywords', error: gscKeywords.reason });
      }
      if (gscPerformance.status === 'rejected') {
        dashboardData.errors.push({ service: 'GSC Performance', error: gscPerformance.reason });
      }

      return dashboardData;

    } catch (error) {
      console.error('Erreur lors de la récupération des données du dashboard:', error);
      throw error;
    }
  }

  /**
   * Génère des données d'opportunités basées sur GA4 et GSC
   *
   * @param {string} dateRange - Plage de dates
   * @returns {Promise} Opportunités d'amélioration SEO
   */
  async getOpportunities(dateRange = 'last30days') {
    try {
      // Récupérer les données nécessaires
      const [gscKeywords, ga4Pages] = await Promise.allSettled([
        this.getGSCKeywords(null, dateRange, 100),
        this.getGA4Pages(null, dateRange, 50)
      ]);

      const opportunities = [];

      // Analyser les mots-clés GSC pour les opportunités
      if (gscKeywords.status === 'fulfilled' && gscKeywords.value) {
        const keywords = gscKeywords.value;

        // Opportunités de mots-clés (position 4-10)
        keywords.forEach(keyword => {
          if (keyword.position >= 4 && keyword.position <= 10 && keyword.impressions > 100) {
            opportunities.push({
              type: 'keyword',
              title: `Améliorer le classement pour "${keyword.keyword}"`,
              description: `Ce mot-clé est en position ${keyword.position.toFixed(1)} avec ${keyword.impressions} impressions`,
              potential: Math.max(20, 100 - (keyword.position * 10)),
              difficulty: keyword.position <= 6 ? 'low' : 'medium',
              currentPosition: keyword.position,
              keyword: keyword.keyword,
              impressions: keyword.impressions,
              clicks: keyword.clicks
            });
          }
        });
      }

      // Analyser les pages GA4 pour les opportunités de contenu
      if (ga4Pages.status === 'fulfilled' && ga4Pages.value) {
        const pages = ga4Pages.value;

        pages.forEach(page => {
          if (page.bounce_rate > 70 && page.pageviews > 50) {
            opportunities.push({
              type: 'content',
              title: `Réduire le taux de rebond de "${page.title}"`,
              description: `Cette page a un taux de rebond de ${page.bounce_rate.toFixed(1)}%`,
              potential: Math.min(90, page.bounce_rate),
              difficulty: 'medium',
              pageUrl: page.url,
              bounceRate: page.bounce_rate,
              pageviews: page.pageviews
            });
          }
        });
      }

      // Trier par potentiel décroissant
      opportunities.sort((a, b) => b.potential - a.potential);

      return opportunities.slice(0, 20); // Limiter à 20 opportunités

    } catch (error) {
      console.error('Erreur lors de la génération des opportunités:', error);
      throw error;
    }
  }

  /**
   * Valide les paramètres de date
   *
   * @param {string} dateRange - Plage de dates à valider
   * @returns {boolean} True si valide
   */
  isValidDateRange(dateRange) {
    const validRanges = ['last7days', 'last30days', 'last90days', 'last12months'];
    return validRanges.includes(dateRange);
  }

  /**
   * Formate les erreurs pour l'affichage
   *
   * @param {Error} error - Erreur à formater
   * @returns {Object} Erreur formatée
   */
  formatError(error) {
    return {
      message: error.message || 'Erreur inconnue',
      code: error.code || 'unknown_error',
      status: error.status || 500
    };
  }
}

// Exporter une instance unique du service
const analyticsService = new AnalyticsService();
export default analyticsService;
