/**
 * Service API pour les rapports Boss SEO avec données réelles.
 * 
 * Ce service connecte l'interface React aux nouvelles APIs
 * de rapports avec des données réelles.
 */

class BossReportsAPI {
    constructor() {
        this.baseUrl = '/wp-json/boss-seo/v1';
        this.nonce = window.bossSeoDashboard?.nonce || '';
        this.cache = new Map();
        this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
    }

    /**
     * Récupère les données pour un type de rapport.
     * 
     * @param {string} type - Type de rapport (performance, content, technical)
     * @returns {Promise} Données du rapport
     */
    async getReportData(type) {
        try {
            const response = await fetch(`${this.baseUrl}/reports/data/${type}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-WP-Nonce': this.nonce
                }
            });

            if (!response.ok) {
                throw new Error(`Erreur HTTP: ${response.status}`);
            }

            const data = await response.json();
            
            if (!data.success) {
                throw new Error(data.message || 'Erreur lors de la récupération des données');
            }

            return data.data;
        } catch (error) {
            console.error('Erreur getReportData:', error);
            throw error;
        }
    }

    /**
     * Génère un nouveau rapport.
     * 
     * @param {Object} reportConfig - Configuration du rapport
     * @returns {Promise} Informations du rapport généré
     */
    async generateReport(reportConfig) {
        try {
            const response = await fetch(`${this.baseUrl}/reports/generate`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-WP-Nonce': this.nonce
                },
                body: JSON.stringify(reportConfig)
            });

            if (!response.ok) {
                throw new Error(`Erreur HTTP: ${response.status}`);
            }

            const data = await response.json();
            
            if (!data.success) {
                throw new Error(data.message || 'Erreur lors de la génération du rapport');
            }

            return data.report;
        } catch (error) {
            console.error('Erreur generateReport:', error);
            throw error;
        }
    }

    /**
     * Récupère l'historique des rapports.
     * 
     * @param {Object} filters - Filtres de recherche
     * @returns {Promise} Liste des rapports
     */
    async getReportsHistory(filters = {}) {
        try {
            const params = new URLSearchParams({
                page: filters.page || 1,
                per_page: filters.per_page || 10,
                ...(filters.type && { type: filters.type }),
                ...(filters.search && { search: filters.search })
            });

            const response = await fetch(`${this.baseUrl}/reports/history?${params}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-WP-Nonce': this.nonce
                }
            });

            if (!response.ok) {
                throw new Error(`Erreur HTTP: ${response.status}`);
            }

            const data = await response.json();
            
            if (!data.success) {
                throw new Error(data.message || 'Erreur lors de la récupération de l\'historique');
            }

            return {
                reports: data.reports,
                pagination: data.pagination
            };
        } catch (error) {
            console.error('Erreur getReportsHistory:', error);
            throw error;
        }
    }

    /**
     * Supprime un rapport.
     * 
     * @param {number} reportId - ID du rapport à supprimer
     * @returns {Promise} Résultat de la suppression
     */
    async deleteReport(reportId) {
        try {
            const response = await fetch(`${this.baseUrl}/reports/${reportId}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'X-WP-Nonce': this.nonce
                }
            });

            if (!response.ok) {
                throw new Error(`Erreur HTTP: ${response.status}`);
            }

            const data = await response.json();
            
            if (!data.success) {
                throw new Error(data.message || 'Erreur lors de la suppression');
            }

            return data;
        } catch (error) {
            console.error('Erreur deleteReport:', error);
            throw error;
        }
    }

    /**
     * Récupère les statistiques de stockage.
     * 
     * @returns {Promise} Statistiques de stockage
     */
    async getStorageStats() {
        try {
            const response = await fetch(`${this.baseUrl}/reports/stats`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-WP-Nonce': this.nonce
                }
            });

            if (!response.ok) {
                throw new Error(`Erreur HTTP: ${response.status}`);
            }

            const data = await response.json();
            
            if (!data.success) {
                throw new Error(data.message || 'Erreur lors de la récupération des statistiques');
            }

            return data.stats;
        } catch (error) {
            console.error('Erreur getStorageStats:', error);
            throw error;
        }
    }

    /**
     * Vide le cache des rapports.
     * 
     * @returns {Promise} Résultat du vidage
     */
    async clearCache() {
        try {
            const response = await fetch(`${this.baseUrl}/reports/cache/clear`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-WP-Nonce': this.nonce
                }
            });

            if (!response.ok) {
                throw new Error(`Erreur HTTP: ${response.status}`);
            }

            const data = await response.json();
            
            if (!data.success) {
                throw new Error(data.message || 'Erreur lors du vidage du cache');
            }

            return data;
        } catch (error) {
            console.error('Erreur clearCache:', error);
            throw error;
        }
    }

    /**
     * Formate la taille d'un fichier.
     * 
     * @param {number} bytes - Taille en bytes
     * @returns {string} Taille formatée
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    }

    /**
     * Formate une date.
     * 
     * @param {string} dateString - Date au format ISO
     * @returns {string} Date formatée
     */
    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('fr-FR', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    /**
     * Obtient l'icône pour un type de rapport.
     * 
     * @param {string} type - Type de rapport
     * @returns {string} Icône
     */
    getReportTypeIcon(type) {
        const icons = {
            performance: '📊',
            content: '📝',
            technical: '⚙️',
            keywords: '🔍',
            local: '📍',
            ecommerce: '🛒'
        };
        
        return icons[type] || '📄';
    }

    /**
     * Obtient la couleur pour un niveau de performance.
     * 
     * @param {string} level - Niveau (excellent, good, fair, poor)
     * @returns {string} Classe CSS
     */
    getPerformanceLevelClass(level) {
        const classes = {
            excellent: 'text-green-600',
            good: 'text-blue-600',
            fair: 'text-yellow-600',
            poor: 'text-red-600'
        };
        
        return classes[level] || 'text-gray-600';
    }

    /**
     * Obtient la couleur pour un score SEO.
     * 
     * @param {number} score - Score (0-100)
     * @returns {string} Classe CSS
     */
    getScoreClass(score) {
        if (score >= 80) return 'text-green-600';
        if (score >= 60) return 'text-blue-600';
        if (score >= 40) return 'text-yellow-600';
        return 'text-red-600';
    }

    /**
     * Génère une URL de téléchargement pour un rapport.
     * 
     * @param {Object} report - Données du rapport
     * @returns {string} URL de téléchargement
     */
    getDownloadUrl(report) {
        return report.download_url || '#';
    }

    /**
     * Vérifie si un rapport existe physiquement.
     *
     * @param {Object} report - Données du rapport
     * @returns {boolean} Existence du fichier
     */
    isReportFileExists(report) {
        return report.file_exists === true;
    }

    /**
     * Récupère les types de rapports disponibles.
     *
     * @returns {Array} Types de rapports
     */
    getReportTypes() {
        return [
            {
                id: 'performance',
                name: 'Performance SEO',
                description: 'Analyse complète des performances SEO avec métriques clés et tendances',
                icon: '📊',
                category: 'performance',
                metrics: ['Score SEO global', 'Pages optimisées', 'Erreurs critiques', 'Tendances'],
                available: true
            },
            {
                id: 'keywords',
                name: 'Analyse des mots-clés',
                description: 'Performance des mots-clés avec données Google Search Console',
                icon: '🔍',
                category: 'keywords',
                metrics: ['Mots-clés suivis', 'Positions moyennes', 'CTR', 'Opportunités'],
                available: true,
                requiresGSC: true
            },
            {
                id: 'content',
                name: 'Audit de contenu',
                description: 'Analyse du contenu et des méta-données',
                icon: '📝',
                category: 'content',
                metrics: ['Contenu analysé', 'Méta-données', 'Qualité', 'Optimisation'],
                available: true
            },
            {
                id: 'technical',
                name: 'Audit technique',
                description: 'Performance technique et Core Web Vitals',
                icon: '⚙️',
                category: 'technical',
                metrics: ['PageSpeed', 'Core Web Vitals', 'Erreurs techniques', 'Optimisations'],
                available: true
            },
            {
                id: 'local',
                name: 'SEO Local',
                description: 'Analyse du référencement local',
                icon: '📍',
                category: 'local',
                metrics: ['Fiches locales', 'Avis', 'Citations', 'Géolocalisation'],
                available: false,
                comingSoon: true
            },
            {
                id: 'ecommerce',
                name: 'E-commerce',
                description: 'Optimisation SEO des produits et catégories',
                icon: '🛒',
                category: 'ecommerce',
                metrics: ['Produits', 'Catégories', 'Rich Snippets', 'Conversions'],
                available: false,
                comingSoon: true
            }
        ];
    }

    /**
     * Récupère les catégories de rapports.
     *
     * @returns {Array} Catégories
     */
    getReportCategories() {
        return [
            { id: 'performance', name: 'Performance', color: '#0073aa' },
            { id: 'keywords', name: 'Mots-clés', color: '#46b450' },
            { id: 'content', name: 'Contenu', color: '#ffb900' },
            { id: 'technical', name: 'Technique', color: '#dc3232' },
            { id: 'local', name: 'Local', color: '#9b59b6' },
            { id: 'ecommerce', name: 'E-commerce', color: '#e67e22' }
        ];
    }

    /**
     * Récupère les formats de rapport disponibles.
     *
     * @returns {Array} Formats
     */
    getReportFormats() {
        return [
            { id: 'html', name: 'HTML', description: 'Rapport web interactif', icon: '🌐' },
            { id: 'pdf', name: 'PDF', description: 'Document PDF professionnel', icon: '📄' },
            { id: 'csv', name: 'CSV', description: 'Export pour Excel/Google Sheets', icon: '📊' }
        ];
    }

    /**
     * Vérifie la configuration Google Search Console.
     *
     * @returns {Promise} État de la configuration
     */
    async checkGSCConfiguration() {
        try {
            const cacheKey = 'gsc_config_check';
            const cached = this.getFromCache(cacheKey);
            if (cached) return cached;

            const response = await fetch(`${this.baseUrl}/reports/gsc/status`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-WP-Nonce': this.nonce
                }
            });

            if (!response.ok) {
                return { configured: false, error: `HTTP ${response.status}` };
            }

            const data = await response.json();
            const result = {
                configured: data.success && data.configured,
                hasTokens: data.has_tokens || false,
                lastSync: data.last_sync || null,
                error: data.success ? null : data.message
            };

            this.setCache(cacheKey, result);
            return result;

        } catch (error) {
            console.error('Erreur checkGSCConfiguration:', error);
            return { configured: false, error: error.message };
        }
    }

    /**
     * Met en cache une valeur.
     *
     * @param {string} key - Clé de cache
     * @param {*} value - Valeur à mettre en cache
     */
    setCache(key, value) {
        this.cache.set(key, {
            value,
            timestamp: Date.now()
        });
    }

    /**
     * Récupère une valeur du cache.
     *
     * @param {string} key - Clé de cache
     * @returns {*} Valeur ou null si expirée
     */
    getFromCache(key) {
        const cached = this.cache.get(key);
        if (!cached) return null;

        if (Date.now() - cached.timestamp > this.cacheTimeout) {
            this.cache.delete(key);
            return null;
        }

        return cached.value;
    }

    /**
     * Vide le cache.
     */
    clearLocalCache() {
        this.cache.clear();
    }
}

// Exporter l'instance
const bossReportsAPI = new BossReportsAPI();
export default bossReportsAPI;
