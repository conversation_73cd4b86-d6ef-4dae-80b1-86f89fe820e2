<?php
/**
 * API REST pour le module Audit SEO assisté par IA
 *
 * @link       https://boss-seo.com
 * @since      1.0.0
 *
 * @package    Boss_Seo
 * @subpackage Boss_Seo/includes
 */

/**
 * Classe API REST pour l'audit SEO assisté par IA.
 *
 * Cette classe définit toutes les routes API nécessaires pour le module
 * d'audit SEO avec assistance IA.
 *
 * @since      1.0.0
 * @package    Boss_Seo
 * @subpackage Boss_Seo/includes
 * <AUTHOR> SEO Team
 */
class Boss_Audit_IA_API {

    /**
     * Instance du service IA.
     *
     * @since    1.0.0
     * @access   private
     * @var      Boss_Seo_AI_Service    $ai_service    Instance du service IA.
     */
    private $ai_service;

    /**
     * Instance du service PageSpeed.
     *
     * @since    1.0.0
     * @access   private
     * @var      Boss_Seo_PageSpeed_Service    $pagespeed_service    Instance du service PageSpeed.
     */
    private $pagespeed_service;

    /**
     * Namespace de l'API REST.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $namespace    Namespace de l'API.
     */
    private $namespace = 'boss-seo/v1';

    /**
     * Initialiser la classe et définir ses propriétés.
     *
     * @since    1.0.0
     */
    public function __construct() {
        $this->ai_service = new Boss_Seo_AI_Service();
        $this->pagespeed_service = new Boss_Seo_PageSpeed_Service();
    }

    /**
     * Enregistrer les routes de l'API REST.
     *
     * @since    1.0.0
     */
    public function register_rest_routes() {
        // Route pour vérifier la configuration IA
        register_rest_route( $this->namespace, '/ai/check', array(
            'methods'             => WP_REST_Server::READABLE,
            'callback'            => array( $this, 'check_ai_configuration' ),
            'permission_callback' => array( $this, 'check_permissions' ),
        ) );

        // Route pour récupérer les pages disponibles
        register_rest_route( $this->namespace, '/audit/pages', array(
            'methods'             => WP_REST_Server::READABLE,
            'callback'            => array( $this, 'get_available_pages' ),
            'permission_callback' => array( $this, 'check_permissions' ),
        ) );

        // Route pour effectuer un audit
        register_rest_route( $this->namespace, '/audit/perform', array(
            'methods'             => WP_REST_Server::CREATABLE,
            'callback'            => array( $this, 'perform_audit' ),
            'permission_callback' => array( $this, 'check_permissions' ),
            'args'                => array(
                'url' => array(
                    'required'          => true,
                    'type'              => 'string',
                    'format'            => 'uri',
                    'sanitize_callback' => 'esc_url_raw',
                    'validate_callback' => array( $this, 'validate_url' ),
                ),
                'mode' => array(
                    'required'          => false,
                    'type'              => 'string',
                    'default'           => 'novice',
                    'enum'              => array( 'novice', 'expert' ),
                    'sanitize_callback' => 'sanitize_text_field',
                ),
                'useAI' => array(
                    'required'          => false,
                    'type'              => 'boolean',
                    'default'           => true,
                ),
                'checks' => array(
                    'required'          => false,
                    'type'              => 'array',
                    'default'           => array(
                        'meta_tags',
                        'content_quality',
                        'technical_seo',
                        'performance',
                        'accessibility',
                        'images',
                        'links',
                        'structure'
                    ),
                    'items'             => array(
                        'type' => 'string',
                        'enum' => array(
                            'meta_tags',
                            'content_quality',
                            'technical_seo',
                            'performance',
                            'accessibility',
                            'images',
                            'links',
                            'structure'
                        ),
                    ),
                ),
            ),
        ) );

        // Route pour récupérer l'historique des audits
        register_rest_route( $this->namespace, '/audit/history', array(
            'methods'             => WP_REST_Server::READABLE,
            'callback'            => array( $this, 'get_audit_history' ),
            'permission_callback' => array( $this, 'check_permissions' ),
            'args'                => array(
                'limit' => array(
                    'required'          => false,
                    'type'              => 'integer',
                    'default'           => 20,
                    'minimum'           => 1,
                    'maximum'           => 100,
                    'sanitize_callback' => 'absint',
                ),
                'offset' => array(
                    'required'          => false,
                    'type'              => 'integer',
                    'default'           => 0,
                    'minimum'           => 0,
                    'sanitize_callback' => 'absint',
                ),
            ),
        ) );

        // Route pour sauvegarder un audit
        register_rest_route( $this->namespace, '/audit/save', array(
            'methods'             => WP_REST_Server::CREATABLE,
            'callback'            => array( $this, 'save_audit' ),
            'permission_callback' => array( $this, 'check_permissions' ),
            'args'                => array(
                'url' => array(
                    'required'          => true,
                    'type'              => 'string',
                    'format'            => 'uri',
                    'sanitize_callback' => 'esc_url_raw',
                ),
                'globalScore' => array(
                    'required'          => true,
                    'type'              => 'integer',
                    'minimum'           => 0,
                    'maximum'           => 100,
                    'sanitize_callback' => 'absint',
                ),
                'errors' => array(
                    'required'          => true,
                    'type'              => 'object',
                ),
                'performance' => array(
                    'required'          => false,
                    'type'              => 'object',
                ),
                'metadata' => array(
                    'required'          => false,
                    'type'              => 'object',
                ),
            ),
        ) );

        // Route pour obtenir une explication IA
        register_rest_route( $this->namespace, '/ai/explain', array(
            'methods'             => WP_REST_Server::CREATABLE,
            'callback'            => array( $this, 'get_ai_explanation' ),
            'permission_callback' => array( $this, 'check_permissions' ),
            'args'                => array(
                'error' => array(
                    'required'          => true,
                    'type'              => 'object',
                ),
                'language' => array(
                    'required'          => false,
                    'type'              => 'string',
                    'default'           => 'fr',
                    'enum'              => array( 'fr', 'en', 'es', 'de', 'it' ),
                    'sanitize_callback' => 'sanitize_text_field',
                ),
                'mode' => array(
                    'required'          => false,
                    'type'              => 'string',
                    'default'           => 'explanation',
                    'enum'              => array( 'explanation', 'detailed' ),
                    'sanitize_callback' => 'sanitize_text_field',
                ),
            ),
        ) );

        // Route pour obtenir une suggestion de correction IA
        register_rest_route( $this->namespace, '/ai/correct', array(
            'methods'             => WP_REST_Server::CREATABLE,
            'callback'            => array( $this, 'get_ai_correction' ),
            'permission_callback' => array( $this, 'check_permissions' ),
            'args'                => array(
                'error' => array(
                    'required'          => true,
                    'type'              => 'object',
                ),
                'language' => array(
                    'required'          => false,
                    'type'              => 'string',
                    'default'           => 'fr',
                    'enum'              => array( 'fr', 'en', 'es', 'de', 'it' ),
                    'sanitize_callback' => 'sanitize_text_field',
                ),
                'mode' => array(
                    'required'          => false,
                    'type'              => 'string',
                    'default'           => 'correction',
                    'enum'              => array( 'correction', 'advanced' ),
                    'sanitize_callback' => 'sanitize_text_field',
                ),
            ),
        ) );

        // Route pour appliquer une correction automatique
        register_rest_route( $this->namespace, '/audit/apply-fix', array(
            'methods'             => WP_REST_Server::CREATABLE,
            'callback'            => array( $this, 'apply_automatic_fix' ),
            'permission_callback' => array( $this, 'check_permissions' ),
            'args'                => array(
                'error' => array(
                    'required'          => true,
                    'type'              => 'object',
                ),
                'suggestion' => array(
                    'required'          => true,
                    'type'              => 'object',
                ),
                'post_id' => array(
                    'required'          => false,
                    'type'              => 'integer',
                    'sanitize_callback' => 'absint',
                ),
            ),
        ) );
    }

    /**
     * Vérifier les permissions pour l'API.
     *
     * @since    1.0.0
     * @param    WP_REST_Request    $request    Requête REST complète.
     * @return   bool                          True si l'utilisateur a les permissions.
     */
    public function check_permissions( $request ) {
        return current_user_can( 'manage_options' );
    }

    /**
     * Valider une URL.
     *
     * @since    1.0.0
     * @param    string             $param      URL à valider.
     * @param    WP_REST_Request    $request    Requête REST complète.
     * @param    string             $key        Clé du paramètre.
     * @return   bool                          True si l'URL est valide.
     */
    public function validate_url( $param, $request, $key ) {
        return filter_var( $param, FILTER_VALIDATE_URL ) !== false;
    }

    /**
     * Vérifier la configuration IA.
     *
     * @since    1.0.0
     * @param    WP_REST_Request    $request    Requête REST complète.
     * @return   WP_REST_Response               Réponse REST.
     */
    public function check_ai_configuration( $request ) {
        try {
            $ai_config = $this->ai_service->check_configuration();
            
            return new WP_REST_Response( array(
                'success' => true,
                'data'    => array(
                    'ai_available' => $ai_config['available'],
                    'providers'    => $ai_config['providers'],
                    'message'      => $ai_config['message']
                ),
                'message' => __( 'Configuration IA vérifiée avec succès.', 'boss-seo' )
            ), 200 );

        } catch ( Exception $e ) {
            return new WP_REST_Response( array(
                'success' => false,
                'data'    => array(
                    'ai_available' => false,
                    'providers'    => array(),
                    'message'      => $e->getMessage()
                ),
                'message' => __( 'Erreur lors de la vérification de la configuration IA.', 'boss-seo' )
            ), 500 );
        }
    }

    /**
     * Récupérer les pages disponibles pour l'audit.
     *
     * @since    1.0.0
     * @param    WP_REST_Request    $request    Requête REST complète.
     * @return   WP_REST_Response               Réponse REST.
     */
    public function get_available_pages( $request ) {
        try {
            $pages = array();

            // Récupérer les pages publiées
            $published_pages = get_posts( array(
                'post_type'      => array( 'page', 'post' ),
                'post_status'    => 'publish',
                'posts_per_page' => 50,
                'orderby'        => 'title',
                'order'          => 'ASC'
            ) );

            foreach ( $published_pages as $page ) {
                $pages[] = array(
                    'id'    => $page->ID,
                    'title' => $page->post_title,
                    'url'   => get_permalink( $page->ID ),
                    'type'  => $page->post_type
                );
            }

            // Ajouter la page d'accueil
            array_unshift( $pages, array(
                'id'    => 0,
                'title' => __( 'Page d\'accueil', 'boss-seo' ),
                'url'   => home_url(),
                'type'  => 'home'
            ) );

            return new WP_REST_Response( array(
                'success' => true,
                'data'    => array(
                    'pages' => $pages,
                    'total' => count( $pages )
                ),
                'message' => __( 'Pages récupérées avec succès.', 'boss-seo' )
            ), 200 );

        } catch ( Exception $e ) {
            return new WP_REST_Response( array(
                'success' => false,
                'data'    => array(
                    'pages' => array(),
                    'total' => 0
                ),
                'message' => __( 'Erreur lors de la récupération des pages.', 'boss-seo' ) . ' ' . $e->getMessage()
            ), 500 );
        }
    }
