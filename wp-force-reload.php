<?php
/**
 * Script à placer dans le répertoire racine de WordPress
 * pour forcer le rechargement du module Audit IA
 */

// Charger WordPress
require_once('wp-load.php');

// Vérifier les permissions
if (!current_user_can('manage_options')) {
    wp_die('Accès non autorisé');
}

echo "<!DOCTYPE html><html><head><title>Force Reload Audit IA</title>";
echo "<style>body{font-family:Arial,sans-serif;margin:40px;background:#f1f1f1;}";
echo ".container{background:white;padding:30px;border-radius:8px;box-shadow:0 2px 10px rgba(0,0,0,0.1);}";
echo ".success{color:#46b450;} .error{color:#dc3232;} .info{color:#0073aa;}";
echo "h1{color:#23282d;} pre{background:#f9f9f9;padding:15px;border-radius:4px;overflow-x:auto;}";
echo "</style></head><body><div class='container'>";

echo "<h1>🔄 Force Reload Module Audit IA</h1>";

// Forcer la régénération de la version des assets
echo "<h2>📦 Forçage du rechargement des assets...</h2>";

$cache_prefix = 'boss_seo_cache_';

// Supprimer les anciennes versions
delete_option($cache_prefix . 'audit_ia_version');
delete_option($cache_prefix . 'forced_assets_version');

// Forcer une nouvelle version
$new_version = time() . '.audit-ia-forced-' . wp_rand(1000, 9999);
update_option($cache_prefix . 'forced_assets_version', $new_version);

echo "<p class='success'>✅ Nouvelle version forcée : <strong>$new_version</strong></p>";

// Vider les transients Boss SEO
echo "<h2>🗑️ Nettoyage du cache...</h2>";

global $wpdb;
$transients_deleted = 0;

$transients = $wpdb->get_results(
    "SELECT option_name FROM {$wpdb->options} WHERE option_name LIKE '_transient_boss_seo_%'"
);

foreach ($transients as $transient) {
    $key = str_replace('_transient_', '', $transient->option_name);
    if (delete_transient($key)) {
        $transients_deleted++;
    }
}

echo "<p class='success'>✅ $transients_deleted transients Boss SEO supprimés</p>";

// Vider le cache d'objet
if (function_exists('wp_cache_flush')) {
    wp_cache_flush();
    echo "<p class='success'>✅ Cache d'objet WordPress vidé</p>";
}

// Vider le cache des plugins populaires
$cache_plugins = [
    'WP Rocket' => function() {
        if (function_exists('rocket_clean_domain')) {
            rocket_clean_domain();
            return true;
        }
        return false;
    },
    'W3 Total Cache' => function() {
        if (function_exists('w3tc_flush_all')) {
            w3tc_flush_all();
            return true;
        }
        return false;
    },
    'WP Super Cache' => function() {
        if (function_exists('wp_cache_clear_cache')) {
            wp_cache_clear_cache();
            return true;
        }
        return false;
    },
    'LiteSpeed Cache' => function() {
        if (class_exists('LiteSpeed\Purge')) {
            LiteSpeed\Purge::purge_all();
            return true;
        }
        return false;
    }
];

echo "<h2>🧹 Nettoyage des caches de plugins...</h2>";
foreach ($cache_plugins as $plugin_name => $clear_function) {
    if ($clear_function()) {
        echo "<p class='success'>✅ Cache $plugin_name vidé</p>";
    } else {
        echo "<p class='info'>ℹ️ $plugin_name non détecté ou non actif</p>";
    }
}

// Vérifier les fichiers compilés
echo "<h2>📁 Vérification des fichiers compilés...</h2>";

$plugin_dir = WP_PLUGIN_DIR . '/Bossseov1.1/';
$js_file = $plugin_dir . 'assets/js/dashboard.js';
$css_file = $plugin_dir . 'assets/js/dashboard.css';

if (file_exists($js_file)) {
    $js_size = filesize($js_file);
    $js_time = date('Y-m-d H:i:s', filemtime($js_file));
    echo "<p class='success'>✅ dashboard.js - " . round($js_size/1024, 1) . " KB (modifié: $js_time)</p>";
} else {
    echo "<p class='error'>❌ dashboard.js - Fichier manquant</p>";
}

if (file_exists($css_file)) {
    $css_size = filesize($css_file);
    $css_time = date('Y-m-d H:i:s', filemtime($css_file));
    echo "<p class='success'>✅ dashboard.css - " . round($css_size/1024, 1) . " KB (modifié: $css_time)</p>";
} else {
    echo "<p class='error'>❌ dashboard.css - Fichier manquant</p>";
}

// Instructions finales
echo "<h2>🎯 Actions terminées !</h2>";

echo "<div style='background:#e7f3ff;padding:20px;border-radius:6px;border-left:4px solid #0073aa;'>";
echo "<h3>📝 Prochaines étapes :</h3>";
echo "<ol>";
echo "<li><strong>Allez dans WordPress Admin</strong></li>";
echo "<li><strong>Naviguez vers Boss SEO > Audit SEO assisté par IA</strong></li>";
echo "<li><strong>Appuyez sur Ctrl+F5</strong> (ou Cmd+Shift+R sur Mac) pour forcer le rechargement</li>";
echo "<li><strong>Vous devriez voir la nouvelle interface !</strong></li>";
echo "</ol>";
echo "</div>";

echo "<div style='background:#f0f8f0;padding:20px;border-radius:6px;border-left:4px solid #46b450;margin-top:20px;'>";
echo "<h3>🔍 Nouvelle interface attendue :</h3>";
echo "<ul>";
echo "<li>✅ <strong>Premier onglet :</strong> 'Statistiques globales'</li>";
echo "<li>✅ <strong>Vraies données</strong> au lieu de mock data</li>";
echo "<li>✅ <strong>Interface professionnelle</strong> avec onglets</li>";
echo "<li>✅ <strong>Support de tous les types</strong> de contenu</li>";
echo "<li>✅ <strong>Explications IA</strong> personnalisées</li>";
echo "</ul>";
echo "</div>";

echo "<div style='background:#fff3cd;padding:20px;border-radius:6px;border-left:4px solid #856404;margin-top:20px;'>";
echo "<h3>⚠️ Si vous ne voyez toujours pas la nouvelle interface :</h3>";
echo "<ul>";
echo "<li>Videz le cache de votre navigateur (Ctrl+Shift+Delete)</li>";
echo "<li>Ouvrez les outils de développement (F12) et vérifiez la console pour les erreurs</li>";
echo "<li>Essayez en navigation privée</li>";
echo "<li>Vérifiez que le plugin Boss SEO est bien actif</li>";
echo "</ul>";
echo "</div>";

$admin_url = admin_url('admin.php?page=boss-seo-audit-ia');
echo "<div style='text-align:center;margin-top:30px;'>";
echo "<a href='$admin_url' style='background:#0073aa;color:white;padding:15px 30px;text-decoration:none;border-radius:6px;font-weight:bold;'>🚀 Aller au module Audit IA</a>";
echo "</div>";

echo "<p style='text-align:center;margin-top:20px;color:#666;'>🎉 Module Audit IA transformé et prêt !</p>";

echo "</div></body></html>";

// Supprimer ce fichier après utilisation pour la sécurité
// unlink(__FILE__);
