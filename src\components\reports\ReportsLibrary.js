import { useState, useEffect } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  <PERSON>,
  CardBody,
  CardHeader,
  CardFooter,
  Button,
  SelectControl,
  TextControl,
  Modal,
  Spinner,
  Notice
} from '@wordpress/components';
import bossReportsAPI from '../../modules/Reports/api';

const ReportsLibrary = () => {
  // États
  const [isLoading, setIsLoading] = useState(true);
  const [reports, setReports] = useState([]);
  const [categories, setCategories] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [showPreview, setShowPreview] = useState(false);
  const [selectedReport, setSelectedReport] = useState(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [gscStatus, setGscStatus] = useState({ configured: false, checking: true });
  const [error, setError] = useState(null);
  
  // Charger les données
  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Charger les catégories et types de rapports depuis l'API
        const categories = bossReportsAPI.getReportCategories();
        const reportTypes = bossReportsAPI.getReportTypes();

        // Vérifier la configuration Google Search Console
        const gscConfig = await bossReportsAPI.checkGSCConfiguration();
        setGscStatus({ ...gscConfig, checking: false });

        // Transformer les types de rapports en format compatible avec l'interface
        const reportsData = reportTypes.map(type => ({
          id: type.id,
          title: type.name,
          description: type.description,
          category: type.category,
          icon: type.icon,
          metrics: type.metrics,
          available: type.available,
          comingSoon: type.comingSoon,
          requiresGSC: type.requiresGSC,
          // Ajouter des données d'interface
          thumbnail: null, // Pas de thumbnail pour l'instant
          frequency: 'on-demand', // Par défaut à la demande
          lastGenerated: null // Sera récupéré de l'historique si nécessaire
        }));

        setCategories(categories);
        setReports(reportsData);

      } catch (error) {
        console.error('Erreur lors du chargement des données:', error);
        setError(__('Erreur lors du chargement des données de rapports.', 'boss-seo'));
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, []);
  
  // Fonction pour filtrer les rapports
  const getFilteredReports = () => {
    return reports.filter(report => {
      // Filtrer par catégorie
      const matchesCategory = selectedCategory === 'all' || report.category === selectedCategory;
      
      // Filtrer par recherche
      const matchesSearch = searchQuery === '' || 
        report.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        report.description.toLowerCase().includes(searchQuery.toLowerCase());
      
      return matchesCategory && matchesSearch;
    });
  };
  
  // Obtenir les rapports filtrés
  const filteredReports = getFilteredReports();
  
  // Fonction pour prévisualiser un rapport
  const handlePreview = (report) => {
    setSelectedReport(report);
    setShowPreview(true);
  };
  
  // Fonction pour générer un rapport
  const handleGenerate = async (report) => {
    // Vérifier si le rapport nécessite Google Search Console
    if (report.requiresGSC && !gscStatus.configured) {
      alert(__('Ce rapport nécessite la configuration de Google Search Console. Veuillez configurer GSC dans les paramètres.', 'boss-seo'));
      return;
    }

    // Vérifier si le rapport est disponible
    if (!report.available) {
      alert(__('Ce rapport n\'est pas encore disponible. Il sera ajouté dans une prochaine mise à jour.', 'boss-seo'));
      return;
    }

    setIsGenerating(true);
    setError(null);

    try {
      // Configuration du rapport
      const reportConfig = {
        type: report.id,
        format: 'html', // Par défaut HTML, l'utilisateur pourra choisir plus tard
        options: {
          include_charts: true,
          include_recommendations: true
        }
      };

      // Générer le rapport via l'API
      const generatedReport = await bossReportsAPI.generateReport(reportConfig);

      // Mettre à jour la date de dernière génération
      const updatedReports = reports.map(r => {
        if (r.id === report.id) {
          return {
            ...r,
            lastGenerated: new Date().toISOString().split('T')[0]
          };
        }
        return r;
      });

      setReports(updatedReports);

      // Si on est dans la prévisualisation, mettre à jour le rapport sélectionné
      if (selectedReport && selectedReport.id === report.id) {
        setSelectedReport({
          ...selectedReport,
          lastGenerated: new Date().toISOString().split('T')[0]
        });
      }

      // Succès - afficher le rapport ou rediriger
      if (generatedReport.download_url) {
        // Ouvrir le rapport dans un nouvel onglet
        window.open(generatedReport.download_url, '_blank');
      } else {
        alert(__('Rapport généré avec succès !', 'boss-seo'));
      }

    } catch (error) {
      console.error('Erreur lors de la génération du rapport:', error);
      setError(__('Erreur lors de la génération du rapport: ', 'boss-seo') + error.message);
    } finally {
      setIsGenerating(false);
    }
  };
  
  // Fonction pour obtenir le texte de la fréquence
  const getFrequencyText = (frequency) => {
    switch (frequency) {
      case 'daily':
        return __('Quotidien', 'boss-seo');
      case 'weekly':
        return __('Hebdomadaire', 'boss-seo');
      case 'monthly':
        return __('Mensuel', 'boss-seo');
      case 'quarterly':
        return __('Trimestriel', 'boss-seo');
      default:
        return frequency;
    }
  };

  return (
    <div>
      {isLoading ? (
        <div className="boss-flex boss-justify-center boss-items-center boss-p-12">
          <Spinner />
        </div>
      ) : (
        <div>
          {/* Affichage des erreurs */}
          {error && (
            <Notice status="error" isDismissible onRemove={() => setError(null)}>
              {error}
            </Notice>
          )}

          {/* Notification Google Search Console */}
          {!gscStatus.checking && !gscStatus.configured && (
            <Notice status="warning" isDismissible={false}>
              <p>
                <strong>{__('Google Search Console non configuré', 'boss-seo')}</strong>
              </p>
              <p>
                {__('Certains rapports (mots-clés) nécessitent Google Search Console pour afficher des données réelles. ', 'boss-seo')}
                <a href="#settings" className="boss-text-blue-600 boss-underline">
                  {__('Configurer maintenant', 'boss-seo')}
                </a>
              </p>
            </Notice>
          )}

          {gscStatus.configured && (
            <Notice status="success" isDismissible={false}>
              <p>
                <strong>{__('Google Search Console configuré', 'boss-seo')}</strong> ✅
              </p>
              <p>
                {__('Tous les rapports avec données réelles sont disponibles.', 'boss-seo')}
                {gscStatus.lastSync && (
                  <span className="boss-text-sm boss-text-gray-600">
                    {' '}({__('Dernière synchronisation:', 'boss-seo')} {gscStatus.lastSync})
                  </span>
                )}
              </p>
            </Notice>
          )}
          <Card className="boss-mb-6">
            <CardBody>
              <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-3 boss-gap-4">
                <TextControl
                  placeholder={__('Rechercher un rapport...', 'boss-seo')}
                  value={searchQuery}
                  onChange={setSearchQuery}
                />
                
                <SelectControl
                  label=""
                  value={selectedCategory}
                  options={[
                    { label: __('Toutes les catégories', 'boss-seo'), value: 'all' },
                    ...categories.map(category => ({
                      label: category.name,
                      value: category.id
                    }))
                  ]}
                  onChange={setSelectedCategory}
                />
                
                <div className="boss-flex boss-items-end">
                  <Button
                    isPrimary
                    href="#report-creator"
                    className="boss-w-full"
                  >
                    {__('Créer un rapport personnalisé', 'boss-seo')}
                  </Button>
                </div>
              </div>
            </CardBody>
          </Card>
          
          <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 lg:boss-grid-cols-3 boss-gap-6">
            {filteredReports.length === 0 ? (
              <div className="boss-col-span-3 boss-text-center boss-py-12 boss-text-boss-gray">
                {__('Aucun rapport trouvé.', 'boss-seo')}
              </div>
            ) : (
              filteredReports.map(report => (
                <Card key={report.id} className={`boss-transition-all boss-duration-300 boss-hover:boss-shadow-lg ${!report.available ? 'boss-opacity-60' : ''}`}>
                  <div className="boss-relative boss-h-40 boss-bg-gradient-to-br boss-from-blue-50 boss-to-blue-100 boss-rounded-t-lg boss-overflow-hidden">
                    <div className="boss-flex boss-justify-center boss-items-center boss-h-full">
                      <span className="boss-text-6xl">{report.icon}</span>
                    </div>

                    {/* Badge de catégorie */}
                    <div className="boss-absolute boss-top-2 boss-right-2">
                      <span
                        className="boss-px-2 boss-py-1 boss-text-xs boss-font-medium boss-rounded-full boss-text-white"
                        style={{ backgroundColor: categories.find(c => c.id === report.category)?.color || '#0073aa' }}
                      >
                        {categories.find(c => c.id === report.category)?.name || report.category}
                      </span>
                    </div>

                    {/* Badge de statut */}
                    <div className="boss-absolute boss-top-2 boss-left-2">
                      {!report.available && report.comingSoon && (
                        <span className="boss-px-2 boss-py-1 boss-text-xs boss-font-medium boss-rounded-full boss-bg-yellow-100 boss-text-yellow-800">
                          {__('Bientôt', 'boss-seo')}
                        </span>
                      )}
                      {report.requiresGSC && (
                        <span className="boss-px-2 boss-py-1 boss-text-xs boss-font-medium boss-rounded-full boss-bg-purple-100 boss-text-purple-800 boss-mt-1 boss-block">
                          GSC
                        </span>
                      )}
                    </div>
                  </div>
                  <CardBody>
                    <h3 className="boss-text-lg boss-font-semibold boss-text-boss-dark boss-mb-2">
                      {report.title}
                    </h3>
                    <p className="boss-text-sm boss-text-boss-gray boss-mb-4">
                      {report.description}
                    </p>
                    <div className="boss-flex boss-flex-wrap boss-gap-2 boss-mb-4">
                      {report.metrics.map((metric, index) => (
                        <span 
                          key={index} 
                          className="boss-px-2 boss-py-1 boss-text-xs boss-rounded-full boss-bg-gray-100 boss-text-boss-gray"
                        >
                          {metric}
                        </span>
                      ))}
                    </div>
                    <div className="boss-flex boss-justify-between boss-items-center boss-text-sm boss-text-boss-gray boss-mb-4">
                      <span>
                        {getFrequencyText(report.frequency)}
                      </span>
                      <span>
                        {report.lastGenerated 
                          ? __('Généré le:', 'boss-seo') + ' ' + report.lastGenerated 
                          : __('Jamais généré', 'boss-seo')}
                      </span>
                    </div>
                  </CardBody>
                  <CardFooter className="boss-border-t boss-border-gray-200">
                    <div className="boss-flex boss-justify-between boss-space-x-2">
                      <Button
                        isSecondary
                        onClick={() => handlePreview(report)}
                      >
                        {__('Aperçu', 'boss-seo')}
                      </Button>

                      {report.available ? (
                        <Button
                          isPrimary
                          onClick={() => handleGenerate(report)}
                          isBusy={isGenerating && selectedReport?.id === report.id}
                          disabled={isGenerating || (report.requiresGSC && !gscStatus.configured)}
                        >
                          {isGenerating && selectedReport?.id === report.id
                            ? __('Génération...', 'boss-seo')
                            : __('Générer', 'boss-seo')
                          }
                        </Button>
                      ) : (
                        <Button
                          disabled
                          className="boss-opacity-50"
                        >
                          {report.comingSoon ? __('Bientôt disponible', 'boss-seo') : __('Non disponible', 'boss-seo')}
                        </Button>
                      )}
                    </div>

                    {/* Avertissement GSC */}
                    {report.requiresGSC && !gscStatus.configured && (
                      <div className="boss-mt-2 boss-text-xs boss-text-orange-600">
                        ⚠️ {__('Nécessite Google Search Console', 'boss-seo')}
                      </div>
                    )}
                  </CardFooter>
                </Card>
              ))
            )}
          </div>
          
          {/* Modal de prévisualisation */}
          {showPreview && selectedReport && (
            <Modal
              title={selectedReport.title}
              onRequestClose={() => setShowPreview(false)}
              className="boss-report-preview-modal"
            >
              <div className="boss-p-6">
                <div className="boss-mb-6">
                  <div className="boss-h-60 boss-bg-gray-100 boss-rounded-lg boss-overflow-hidden boss-mb-4">
                    {selectedReport.thumbnail ? (
                      <img 
                        src={selectedReport.thumbnail} 
                        alt={selectedReport.title} 
                        className="boss-w-full boss-h-full boss-object-cover"
                      />
                    ) : (
                      <div className="boss-flex boss-justify-center boss-items-center boss-h-full boss-bg-gray-200">
                        <span className="dashicons dashicons-chart-bar boss-text-6xl boss-text-gray-400"></span>
                      </div>
                    )}
                  </div>
                  
                  <h2 className="boss-text-xl boss-font-bold boss-text-boss-dark boss-mb-2">
                    {selectedReport.title}
                  </h2>
                  
                  <p className="boss-text-boss-gray boss-mb-4">
                    {selectedReport.description}
                  </p>
                  
                  <div className="boss-grid boss-grid-cols-2 boss-gap-4 boss-mb-6">
                    <div>
                      <h3 className="boss-text-sm boss-font-medium boss-text-boss-dark boss-mb-1">
                        {__('Catégorie', 'boss-seo')}
                      </h3>
                      <p className="boss-text-boss-gray">
                        {categories.find(c => c.id === selectedReport.category)?.name || selectedReport.category}
                      </p>
                    </div>
                    
                    <div>
                      <h3 className="boss-text-sm boss-font-medium boss-text-boss-dark boss-mb-1">
                        {__('Fréquence recommandée', 'boss-seo')}
                      </h3>
                      <p className="boss-text-boss-gray">
                        {getFrequencyText(selectedReport.frequency)}
                      </p>
                    </div>
                    
                    <div>
                      <h3 className="boss-text-sm boss-font-medium boss-text-boss-dark boss-mb-1">
                        {__('Dernière génération', 'boss-seo')}
                      </h3>
                      <p className="boss-text-boss-gray">
                        {selectedReport.lastGenerated || __('Jamais généré', 'boss-seo')}
                      </p>
                    </div>
                    
                    <div>
                      <h3 className="boss-text-sm boss-font-medium boss-text-boss-dark boss-mb-1">
                        {__('Format', 'boss-seo')}
                      </h3>
                      <p className="boss-text-boss-gray">
                        PDF, CSV, HTML
                      </p>
                    </div>
                  </div>
                  
                  <div className="boss-mb-6">
                    <h3 className="boss-text-md boss-font-semibold boss-text-boss-dark boss-mb-2">
                      {__('Métriques incluses', 'boss-seo')}
                    </h3>
                    
                    <div className="boss-flex boss-flex-wrap boss-gap-2">
                      {selectedReport.metrics.map((metric, index) => (
                        <span 
                          key={index} 
                          className="boss-px-3 boss-py-1 boss-text-sm boss-rounded-full boss-bg-blue-50 boss-text-blue-700"
                        >
                          {metric}
                        </span>
                      ))}
                    </div>
                  </div>
                  
                  <div className="boss-p-4 boss-bg-gray-50 boss-rounded-lg boss-mb-6">
                    <h3 className="boss-text-md boss-font-semibold boss-text-boss-dark boss-mb-2">
                      {__('À propos de ce rapport', 'boss-seo')}
                    </h3>
                    
                    <p className="boss-text-sm boss-text-boss-gray boss-mb-2">
                      {__('Ce rapport vous fournit une analyse détaillée de vos performances SEO, avec des métriques clés et des recommandations d\'amélioration.', 'boss-seo')}
                    </p>
                    
                    <p className="boss-text-sm boss-text-boss-gray">
                      {__('Utilisez-le pour suivre vos progrès au fil du temps et identifier les opportunités d\'optimisation.', 'boss-seo')}
                    </p>
                  </div>
                </div>
                
                <div className="boss-flex boss-justify-between">
                  <Button
                    isSecondary
                    onClick={() => setShowPreview(false)}
                  >
                    {__('Fermer', 'boss-seo')}
                  </Button>
                  
                  <Button
                    isPrimary
                    onClick={() => {
                      handleGenerate(selectedReport);
                      setShowPreview(false);
                    }}
                    isBusy={isGenerating}
                    disabled={isGenerating}
                  >
                    {__('Générer ce rapport', 'boss-seo')}
                  </Button>
                </div>
              </div>
            </Modal>
          )}
        </div>
      )}
    </div>
  );
};

export default ReportsLibrary;
