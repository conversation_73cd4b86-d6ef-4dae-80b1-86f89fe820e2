<?php
/**
 * Script pour corriger les duplications dans l'analyseur technique v2
 */

echo "🔧 CORRECTION DES DUPLICATIONS ANALYSEUR TECHNIQUE v2\n";
echo "====================================================\n\n";

$file_path = 'includes/class-boss-technical-analyzer-v2.php';

if (!file_exists($file_path)) {
    echo "❌ Fichier non trouvé: {$file_path}\n";
    exit(1);
}

$content = file_get_contents($file_path);

echo "📄 Fichier original: " . strlen($content) . " caractères\n";

// Supprimer les anciennes méthodes dupliquées
$methods_to_remove = [
    // Ancienne categorize_issues
    '/\/\*\*\s*\*\s*Catégorise les problèmes détectés\.\s*\*.*?\*\/\s*private function categorize_issues\([^}]*\}\s*\}\s*return \$categories;\s*\}/s',
    
    // Ancienne generate_analysis_summary
    '/\/\*\*\s*\*\s*Génère un résumé de l\'analyse\.\s*\*.*?\*\/\s*private function generate_analysis_summary\([^}]*\}\s*return \$summary;\s*\}/s',
    
    // Ancienne get_score_status
    '/\/\*\*\s*\*\s*Détermine le statut d\'un score\.\s*\*.*?\*\/\s*private function get_score_status\([^}]*\}\s*\}/s',
    
    // Ancienne save_analysis_to_history
    '/\/\*\*\s*\*\s*Sauvegarde l\'analyse dans l\'historique\.\s*\*.*?\*\/\s*private function save_analysis_to_history\([^}]*\}\s*return update_option[^}]*\}\s*\}/s',
];

foreach ($methods_to_remove as $pattern) {
    $content = preg_replace($pattern, '', $content);
}

// Nettoyer les lignes vides multiples
$content = preg_replace('/\n\s*\n\s*\n+/', "\n\n", $content);

// Sauvegarder le fichier corrigé
if (file_put_contents($file_path, $content)) {
    echo "✅ Fichier corrigé: " . strlen($content) . " caractères\n";
    echo "📉 Réduction: " . (strlen(file_get_contents($file_path)) - strlen($content)) . " caractères\n";
} else {
    echo "❌ Erreur lors de la sauvegarde\n";
    exit(1);
}

// Vérifier la syntaxe PHP
$output = shell_exec("php -l {$file_path} 2>&1");
if (strpos($output, 'No syntax errors') !== false) {
    echo "✅ Syntaxe PHP valide\n";
} else {
    echo "❌ Erreur de syntaxe:\n{$output}\n";
    exit(1);
}

// Vérifier les duplications restantes
$duplicates = [];
$methods = [
    'combine_analysis_data',
    'calculate_global_score', 
    'calculate_combined_score',
    'categorize_issues',
    'generate_analysis_summary',
    'save_analysis_to_history'
];

foreach ($methods as $method) {
    $count = preg_match_all("/function {$method}\(/", $content);
    if ($count > 1) {
        $duplicates[] = "{$method} ({$count} occurrences)";
    }
}

if (empty($duplicates)) {
    echo "✅ Plus de duplications détectées\n";
} else {
    echo "⚠️ Duplications restantes:\n";
    foreach ($duplicates as $duplicate) {
        echo "  - {$duplicate}\n";
    }
}

echo "\n🏁 CORRECTION TERMINÉE\n";
?>
