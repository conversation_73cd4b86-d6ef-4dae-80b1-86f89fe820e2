<?php
/**
 * Script de nettoyage complet du cache Boss SEO
 * 
 * À exécuter depuis l'admin WordPress : /wp-admin/admin.php?page=clear-all-cache
 */

// Empêcher l'accès direct
if (!defined('ABSPATH')) {
    exit;
}

echo '<div class="wrap">';
echo '<h1>🧹 Nettoyage Complet du Cache Boss SEO</h1>';

$cleared_items = [];
$errors = [];

// 1. Vider le cache WordPress
try {
    if (function_exists('wp_cache_flush')) {
        wp_cache_flush();
        $cleared_items[] = '✅ Cache WordPress (wp_cache_flush)';
    }
} catch (Exception $e) {
    $errors[] = '❌ Cache WordPress: ' . $e->getMessage();
}

// 2. Vider tous les transients Boss SEO
try {
    global $wpdb;
    
    $transients_deleted = $wpdb->query(
        "DELETE FROM {$wpdb->options} 
         WHERE option_name LIKE '_transient_boss_%' 
         OR option_name LIKE '_transient_timeout_boss_%'"
    );
    
    $cleared_items[] = "✅ Transients Boss SEO ($transients_deleted supprimés)";
} catch (Exception $e) {
    $errors[] = '❌ Transients: ' . $e->getMessage();
}

// 3. Vider le cache Analytics spécifiquement
try {
    if (class_exists('Boss_Analytics_Cache')) {
        $cache_manager = new Boss_Analytics_Cache();
        $cache_manager->flush_all();
        $cleared_items[] = '✅ Cache Analytics Boss SEO';
    }
} catch (Exception $e) {
    $errors[] = '❌ Cache Analytics: ' . $e->getMessage();
}

// 4. Supprimer les options temporaires
try {
    $temp_options = [
        'boss_analytics_temp_data',
        'boss_seo_temp_cache',
        'boss_optimizer_temp_results'
    ];
    
    foreach ($temp_options as $option) {
        delete_option($option);
        delete_transient($option);
    }
    
    $cleared_items[] = '✅ Options temporaires supprimées';
} catch (Exception $e) {
    $errors[] = '❌ Options temporaires: ' . $e->getMessage();
}

// 5. Vider le cache des plugins de cache populaires
$cache_plugins_cleared = [];

// WP Rocket
if (function_exists('rocket_clean_domain')) {
    rocket_clean_domain();
    $cache_plugins_cleared[] = 'WP Rocket';
}

// W3 Total Cache
if (function_exists('w3tc_flush_all')) {
    w3tc_flush_all();
    $cache_plugins_cleared[] = 'W3 Total Cache';
}

// WP Super Cache
if (function_exists('wp_cache_clear_cache')) {
    wp_cache_clear_cache();
    $cache_plugins_cleared[] = 'WP Super Cache';
}

// LiteSpeed Cache
if (class_exists('LiteSpeed_Cache_API')) {
    LiteSpeed_Cache_API::purge_all();
    $cache_plugins_cleared[] = 'LiteSpeed Cache';
}

// Autoptimize
if (class_exists('autoptimizeCache')) {
    autoptimizeCache::clearall();
    $cache_plugins_cleared[] = 'Autoptimize';
}

if (!empty($cache_plugins_cleared)) {
    $cleared_items[] = '✅ Plugins de cache: ' . implode(', ', $cache_plugins_cleared);
}

// 6. Forcer la recompilation des assets
try {
    // Supprimer les fichiers de cache des assets s'ils existent
    $asset_cache_dirs = [
        WP_CONTENT_DIR . '/cache/boss-seo/',
        WP_CONTENT_DIR . '/uploads/boss-seo-cache/',
        BOSS_SEO_PLUGIN_DIR . 'assets/cache/'
    ];
    
    foreach ($asset_cache_dirs as $dir) {
        if (is_dir($dir)) {
            $files = glob($dir . '*');
            foreach ($files as $file) {
                if (is_file($file)) {
                    unlink($file);
                }
            }
        }
    }
    
    $cleared_items[] = '✅ Cache des assets supprimé';
} catch (Exception $e) {
    $errors[] = '❌ Cache des assets: ' . $e->getMessage();
}

// 7. Vider le cache de l'opcode PHP
try {
    if (function_exists('opcache_reset')) {
        opcache_reset();
        $cleared_items[] = '✅ Cache OpCode PHP';
    }
} catch (Exception $e) {
    $errors[] = '❌ Cache OpCode: ' . $e->getMessage();
}

// 8. Forcer la régénération des permaliens
try {
    flush_rewrite_rules();
    $cleared_items[] = '✅ Règles de réécriture régénérées';
} catch (Exception $e) {
    $errors[] = '❌ Règles de réécriture: ' . $e->getMessage();
}

// Afficher les résultats
echo '<h2>✅ Éléments nettoyés avec succès</h2>';
echo '<ul>';
foreach ($cleared_items as $item) {
    echo "<li>$item</li>";
}
echo '</ul>';

if (!empty($errors)) {
    echo '<h2>❌ Erreurs rencontrées</h2>';
    echo '<ul>';
    foreach ($errors as $error) {
        echo "<li>$error</li>";
    }
    echo '</ul>';
}

// Instructions post-nettoyage
echo '<h2>📋 Instructions post-nettoyage</h2>';
echo '<div style="background: #f0f8ff; padding: 15px; border-left: 4px solid #0073aa;">';
echo '<ol>';
echo '<li><strong>Fermez tous les onglets</strong> de votre site</li>';
echo '<li><strong>Videz le cache de votre navigateur</strong> (Ctrl+Shift+Delete)</li>';
echo '<li><strong>Ouvrez un nouvel onglet en navigation privée</strong></li>';
echo '<li><strong>Accédez à Boss SEO Analytics</strong></li>';
echo '<li><strong>Vérifiez que les changements sont visibles</strong></li>';
echo '</ol>';
echo '</div>';

// Bouton pour forcer la recompilation
echo '<h2>🔄 Actions supplémentaires</h2>';
echo '<p>';
echo '<a href="' . admin_url('admin.php?page=boss-seo-analytics&force_reload=1') . '" class="button button-primary">';
echo '🔄 Recharger Boss SEO Analytics avec cache forcé';
echo '</a>';
echo '</p>';

echo '<p>';
echo '<a href="javascript:location.reload(true)" class="button button-secondary">';
echo '🔄 Recharger cette page (force)';
echo '</a>';
echo '</p>';

// Script JavaScript pour vider le cache du navigateur
echo '<script>';
echo 'if ("caches" in window) {';
echo '  caches.keys().then(function(names) {';
echo '    names.forEach(function(name) {';
echo '      caches.delete(name);';
echo '    });';
echo '  });';
echo '  console.log("✅ Cache du navigateur vidé");';
echo '}';
echo '</script>';

echo '</div>';
?>
