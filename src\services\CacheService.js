/**
 * Service pour gérer les opérations de cache Boss SEO.
 */

import apiFetch from '@wordpress/api-fetch';

/**
 * Service pour gérer les opérations de cache Boss SEO.
 */
class CacheService {
  /**
   * Vide tout le cache du plugin.
   *
   * @return {Promise} Une promesse qui se résout avec les résultats du vidage.
   */
  async flushAllCache() {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/cache/flush-all',
        method: 'DELETE',
      });
      return response;
    } catch (error) {
      console.error('Erreur lors du vidage de tout le cache:', error);
      throw error;
    }
  }

  /**
   * Vide le cache d'un module spécifique.
   *
   * @param {string} module - Nom du module.
   * @return {Promise} Une promesse qui se résout avec les résultats du vidage.
   */
  async flushModuleCache(module) {
    try {
      const response = await apiFetch({
        path: `/boss-seo/v1/cache/flush/${module}`,
        method: 'DELETE',
      });
      return response;
    } catch (error) {
      console.error(`Erreur lors du vidage du cache du module ${module}:`, error);
      throw error;
    }
  }

  /**
   * Vide seulement le cache des assets (CSS/JS).
   *
   * @return {Promise} Une promesse qui se résout avec les résultats du vidage.
   */
  async flushAssetsCache() {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/cache/flush-assets',
        method: 'DELETE',
      });
      return response;
    } catch (error) {
      console.error('Erreur lors du vidage du cache des assets:', error);
      throw error;
    }
  }

  /**
   * Récupère les statistiques du cache.
   *
   * @return {Promise} Une promesse qui se résout avec les statistiques.
   */
  async getCacheStats() {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/cache/stats',
        method: 'GET',
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des statistiques du cache:', error);
      throw error;
    }
  }

  /**
   * Récupère la version actuelle des assets.
   *
   * @return {Promise} Une promesse qui se résout avec la version des assets.
   */
  async getAssetsVersion() {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/cache/assets-version',
        method: 'GET',
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération de la version des assets:', error);
      throw error;
    }
  }

  /**
   * Vérifie si le cache est en mode debug.
   *
   * @return {Promise} Une promesse qui se résout avec le statut du mode debug.
   */
  async isDebugMode() {
    try {
      const response = await this.getAssetsVersion();
      return response.debug_mode || false;
    } catch (error) {
      console.error('Erreur lors de la vérification du mode debug:', error);
      return false;
    }
  }

  /**
   * Force le rechargement de la page avec un nouveau cache.
   * Utile après avoir vidé le cache des assets.
   *
   * @param {number} delay - Délai en millisecondes avant le rechargement (défaut: 1000ms).
   */
  reloadPageWithNewCache(delay = 1000) {
    setTimeout(() => {
      // Ajouter un paramètre de cache busting à l'URL
      const url = new URL(window.location);
      url.searchParams.set('cache_bust', Date.now());
      window.location.href = url.toString();
    }, delay);
  }

  /**
   * Vide le cache et recharge la page automatiquement.
   * Méthode de convenance pour résoudre rapidement les problèmes de cache.
   *
   * @param {string} type - Type de cache à vider ('all', 'assets', ou nom de module).
   * @return {Promise} Une promesse qui se résout après le vidage du cache.
   */
  async flushAndReload(type = 'assets') {
    try {
      let response;
      
      switch (type) {
        case 'all':
          response = await this.flushAllCache();
          break;
        case 'assets':
          response = await this.flushAssetsCache();
          break;
        default:
          // Considérer comme un nom de module
          response = await this.flushModuleCache(type);
          break;
      }

      if (response.success) {
        // Recharger la page après un court délai
        this.reloadPageWithNewCache();
      }

      return response;
    } catch (error) {
      console.error('Erreur lors du vidage du cache et rechargement:', error);
      throw error;
    }
  }

  /**
   * Surveille les changements de cache et notifie l'utilisateur.
   * Utile pour détecter quand le cache doit être vidé.
   *
   * @param {Function} callback - Fonction appelée quand un changement est détecté.
   * @param {number} interval - Intervalle de vérification en millisecondes (défaut: 30000ms).
   * @return {number} ID de l'intervalle pour pouvoir l'arrêter.
   */
  watchCacheChanges(callback, interval = 30000) {
    let lastVersion = null;

    const checkVersion = async () => {
      try {
        const response = await this.getAssetsVersion();
        const currentVersion = response.version;

        if (lastVersion && lastVersion !== currentVersion) {
          callback({
            type: 'version_changed',
            oldVersion: lastVersion,
            newVersion: currentVersion,
            timestamp: new Date()
          });
        }

        lastVersion = currentVersion;
      } catch (error) {
        callback({
          type: 'error',
          error: error,
          timestamp: new Date()
        });
      }
    };

    // Vérification initiale
    checkVersion();

    // Vérifications périodiques
    return setInterval(checkVersion, interval);
  }

  /**
   * Arrête la surveillance des changements de cache.
   *
   * @param {number} watchId - ID retourné par watchCacheChanges().
   */
  stopWatchingCacheChanges(watchId) {
    if (watchId) {
      clearInterval(watchId);
    }
  }

  /**
   * Nettoie le cache du navigateur pour les assets Boss SEO.
   * Utilise les APIs du navigateur si disponibles.
   */
  async clearBrowserCache() {
    try {
      // Vider le cache du service worker si disponible
      if ('serviceWorker' in navigator && 'caches' in window) {
        const cacheNames = await caches.keys();
        const bossCacheNames = cacheNames.filter(name => name.includes('boss-seo'));
        
        await Promise.all(
          bossCacheNames.map(cacheName => caches.delete(cacheName))
        );
      }

      // Forcer le rechargement des assets en ajoutant des paramètres de cache busting
      const links = document.querySelectorAll('link[href*="boss-seo"]');
      const scripts = document.querySelectorAll('script[src*="boss-seo"]');

      [...links, ...scripts].forEach(element => {
        const url = new URL(element.href || element.src, window.location.origin);
        url.searchParams.set('v', Date.now());
        
        if (element.tagName === 'LINK') {
          element.href = url.toString();
        } else {
          element.src = url.toString();
        }
      });

      return true;
    } catch (error) {
      console.error('Erreur lors du nettoyage du cache navigateur:', error);
      return false;
    }
  }

  /**
   * Diagnostic complet du cache.
   * Retourne des informations détaillées sur l'état du cache.
   *
   * @return {Promise} Une promesse qui se résout avec le diagnostic.
   */
  async diagnoseCacheIssues() {
    try {
      const [stats, version, debugMode] = await Promise.all([
        this.getCacheStats(),
        this.getAssetsVersion(),
        this.isDebugMode()
      ]);

      const diagnosis = {
        timestamp: new Date(),
        assets_version: version.version,
        debug_mode: debugMode,
        transients_count: stats.stats.transients_count,
        modules_status: stats.stats.modules_status,
        recommendations: []
      };

      // Ajouter des recommandations basées sur l'état
      if (debugMode) {
        diagnosis.recommendations.push({
          type: 'info',
          message: 'Mode debug activé - Le cache est automatiquement désactivé.'
        });
      }

      if (stats.stats.transients_count > 100) {
        diagnosis.recommendations.push({
          type: 'warning',
          message: 'Nombre élevé de transients détecté. Considérez vider le cache.'
        });
      }

      const inactiveModules = Object.entries(stats.stats.modules_status)
        .filter(([module, active]) => !active)
        .map(([module]) => module);

      if (inactiveModules.length > 0) {
        diagnosis.recommendations.push({
          type: 'warning',
          message: `Modules de cache inactifs: ${inactiveModules.join(', ')}`
        });
      }

      return diagnosis;
    } catch (error) {
      console.error('Erreur lors du diagnostic du cache:', error);
      throw error;
    }
  }
}

// Exporter une instance unique du service
export default new CacheService();
