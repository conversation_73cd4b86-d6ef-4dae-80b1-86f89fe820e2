import { useState } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  Card,
  CardBody,
  Button,
  Dashicon,
  Spinner,
  Notice,
  __experimentalSpacer as Spacer
} from '@wordpress/components';

const CorrectionSuggestion = ({ suggestion, error }) => {
  const [isApplying, setIsApplying] = useState(false);
  const [isApplied, setIsApplied] = useState(false);
  const [copySuccess, setCopySuccess] = useState(false);

  // Copier le code dans le presse-papiers
  const copyToClipboard = async (text) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    } catch (err) {
      console.error('Erreur lors de la copie:', err);
      // Fallback pour les navigateurs plus anciens
      const textArea = document.createElement('textarea');
      textArea.value = text;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    }
  };

  // Appliquer la correction automatiquement
  const applySuggestion = async () => {
    if (!suggestion.canApply) {
      return;
    }

    try {
      setIsApplying(true);
      
      // Simuler l'application de la correction
      // Dans la vraie implémentation, cela appellerait l'API WordPress
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      setIsApplied(true);
      
    } catch (err) {
      console.error('Erreur lors de l\'application de la suggestion:', err);
    } finally {
      setIsApplying(false);
    }
  };

  return (
    <div className="boss-border-t boss-border-gray-200 boss-pt-4 boss-mt-4">
      <div className="boss-bg-gradient-to-r boss-from-green-50 boss-to-emerald-50 boss-border boss-border-green-200 boss-rounded-lg boss-p-4">
        {/* En-tête de la suggestion */}
        <div className="boss-flex boss-items-center boss-justify-between boss-mb-3">
          <div className="boss-flex boss-items-center">
            <div className="boss-bg-gradient-to-r boss-from-green-500 boss-to-emerald-600 boss-p-2 boss-rounded-lg boss-mr-3">
              <Dashicon icon="admin-tools" className="boss-text-white boss-text-lg" />
            </div>
            <div>
              <h4 className="boss-text-md boss-font-semibold boss-text-boss-dark">
                {suggestion.title}
              </h4>
              <p className="boss-text-sm boss-text-boss-gray">
                {suggestion.description}
              </p>
            </div>
          </div>
          
          {/* Statut */}
          {isApplied && (
            <div className="boss-flex boss-items-center boss-text-green-600">
              <Dashicon icon="yes-alt" className="boss-mr-1" />
              <span className="boss-text-sm boss-font-medium">
                {__('Appliqué', 'boss-seo')}
              </span>
            </div>
          )}
        </div>

        {/* Code de correction */}
        {suggestion.code && (
          <div className="boss-mb-4">
            <h5 className="boss-text-sm boss-font-semibold boss-text-boss-dark boss-mb-2 boss-flex boss-items-center">
              <Dashicon icon="editor-code" className="boss-mr-2 boss-text-blue-600" />
              {__('Code suggéré', 'boss-seo')}
            </h5>
            
            <div className="boss-relative boss-bg-gray-900 boss-rounded-lg boss-p-4 boss-overflow-x-auto">
              <pre className="boss-text-sm boss-text-green-400 boss-font-mono boss-whitespace-pre-wrap">
                <code>{suggestion.code}</code>
              </pre>
              
              {/* Bouton de copie */}
              <div className="boss-absolute boss-top-2 boss-right-2">
                <Button
                  isSecondary
                  size="small"
                  onClick={() => copyToClipboard(suggestion.code)}
                  className="boss-bg-gray-800 boss-text-white boss-border-gray-600 hover:boss-bg-gray-700"
                >
                  {copySuccess ? (
                    <>
                      <Dashicon icon="yes-alt" className="boss-mr-1" />
                      {__('Copié !', 'boss-seo')}
                    </>
                  ) : (
                    <>
                      <Dashicon icon="clipboard" className="boss-mr-1" />
                      {__('Copier', 'boss-seo')}
                    </>
                  )}
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Étapes manuelles */}
        {suggestion.steps && suggestion.steps.length > 0 && (
          <div className="boss-mb-4">
            <h5 className="boss-text-sm boss-font-semibold boss-text-boss-dark boss-mb-2 boss-flex boss-items-center">
              <Dashicon icon="list-view" className="boss-mr-2 boss-text-purple-600" />
              {__('Étapes de correction', 'boss-seo')}
            </h5>
            
            <div className="boss-bg-white boss-border boss-border-purple-200 boss-rounded boss-p-3">
              <ol className="boss-space-y-2">
                {suggestion.steps.map((step, index) => (
                  <li key={index} className="boss-flex boss-items-start">
                    <span className="boss-bg-purple-100 boss-text-purple-800 boss-text-xs boss-font-medium boss-px-2 boss-py-1 boss-rounded boss-mr-2 boss-flex-shrink-0 boss-mt-0.5">
                      {index + 1}
                    </span>
                    <span className="boss-text-sm boss-text-boss-gray">
                      {step}
                    </span>
                  </li>
                ))}
              </ol>
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="boss-flex boss-items-center boss-justify-between boss-pt-3 boss-border-t boss-border-green-200">
          <div className="boss-flex boss-items-center boss-space-x-3">
            {/* Bouton d'application automatique */}
            {suggestion.canApply && !isApplied && (
              <Button
                isPrimary
                onClick={applySuggestion}
                disabled={isApplying}
                className="boss-flex boss-items-center boss-bg-green-600 hover:boss-bg-green-700"
              >
                {isApplying ? (
                  <>
                    <Spinner />
                    {__('Application...', 'boss-seo')}
                  </>
                ) : (
                  <>
                    <Dashicon icon="yes" className="boss-mr-1" />
                    {__('Appliquer la suggestion', 'boss-seo')}
                  </>
                )}
              </Button>
            )}
            
            {/* Message si non applicable automatiquement */}
            {!suggestion.canApply && (
              <div className="boss-flex boss-items-center boss-text-sm boss-text-boss-gray">
                <Dashicon icon="info" className="boss-mr-1 boss-text-blue-500" />
                {__('Correction manuelle requise', 'boss-seo')}
              </div>
            )}
          </div>
          
          {/* Niveau de difficulté */}
          <div className="boss-flex boss-items-center">
            <span className="boss-text-xs boss-text-boss-gray boss-mr-2">
              {__('Difficulté:', 'boss-seo')}
            </span>
            <span className={`boss-px-2 boss-py-1 boss-rounded boss-text-xs boss-font-medium ${
              suggestion.canApply 
                ? 'boss-bg-green-100 boss-text-green-800'
                : 'boss-bg-yellow-100 boss-text-yellow-800'
            }`}>
              {suggestion.canApply ? __('Facile', 'boss-seo') : __('Intermédiaire', 'boss-seo')}
            </span>
          </div>
        </div>

        {/* Avertissements */}
        {suggestion.warnings && suggestion.warnings.length > 0 && (
          <div className="boss-mt-4">
            <Notice
              status="warning"
              isDismissible={false}
              className="boss-mb-0"
            >
              <div>
                <h6 className="boss-text-sm boss-font-semibold boss-mb-1">
                  {__('⚠️ Points d\'attention', 'boss-seo')}
                </h6>
                <ul className="boss-text-sm boss-space-y-1">
                  {suggestion.warnings.map((warning, index) => (
                    <li key={index} className="boss-flex boss-items-start">
                      <span className="boss-mr-1">•</span>
                      <span>{warning}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </Notice>
          </div>
        )}

        {/* Résultat attendu */}
        {suggestion.expectedResult && (
          <div className="boss-mt-4">
            <h5 className="boss-text-sm boss-font-semibold boss-text-boss-dark boss-mb-2 boss-flex boss-items-center">
              <Dashicon icon="chart-line" className="boss-mr-2 boss-text-green-600" />
              {__('Résultat attendu', 'boss-seo')}
            </h5>
            <div className="boss-bg-white boss-border boss-border-green-200 boss-rounded boss-p-3">
              <p className="boss-text-sm boss-text-boss-gray">
                {suggestion.expectedResult}
              </p>
            </div>
          </div>
        )}

        {/* Note sur l'IA */}
        <div className="boss-mt-4 boss-pt-3 boss-border-t boss-border-green-200">
          <div className="boss-flex boss-items-center boss-text-xs boss-text-boss-gray">
            <Dashicon icon="admin-tools" className="boss-mr-1 boss-text-green-500" />
            <span>
              {__('Cette suggestion a été générée par l\'IA en analysant votre contenu et les meilleures pratiques SEO.', 'boss-seo')}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CorrectionSuggestion;
