import { useState } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  Card,
  CardBody,
  CardHeader,
  CardFooter,
  Button,
  Dashicon,
  ToggleControl,
  SelectControl,
  TextControl,
  Notice,
  Panel,
  PanelBody,
  PanelRow
} from '@wordpress/components';

const AnalyticsSetup = ({
  integrations,
  onConnectGA4,
  onConnectGSC,
  onDisconnectService,
  onPropertyChange,
  onRefreshData,
  onClearCache,
  isLoading
}) => {

  // DEBUG: Afficher les données reçues
  console.log('🔍 AnalyticsSetup - Données reçues:', integrations);
  // États
  const [syncSettings, setSyncSettings] = useState({
    frequency: 'daily',
    autoSync: true,
    syncKeywords: true,
    syncPages: true,
    syncEvents: true,
    syncGoals: true,
    retentionPeriod: '90'
  });

  // Fonction pour mettre à jour les paramètres de synchronisation
  const updateSyncSetting = (key, value) => {
    setSyncSettings({
      ...syncSettings,
      [key]: value
    });
  };

  // Fonction pour gérer la sélection de propriété GA4
  const handleGA4PropertyChange = (propertyId) => {
    // Mettre à jour la propriété sélectionnée
    const updatedProperties = integrations.ga4.properties.map(prop => ({
      ...prop,
      selected: prop.id === propertyId
    }));

    // Appeler la fonction de changement de propriété
    onPropertyChange(propertyId);
  };

  // Fonction pour gérer la sélection de propriété GSC
  const handleGSCPropertyChange = (propertyId) => {
    // Mettre à jour la propriété sélectionnée
    const updatedProperties = integrations.gsc.properties.map(prop => ({
      ...prop,
      selected: prop.id === propertyId
    }));

    // Appeler la fonction de changement de propriété
    onPropertyChange(propertyId);
  };

  // Fonction pour sauvegarder les paramètres
  const handleSaveSettings = () => {
    // Simuler la sauvegarde des paramètres
    console.log('Paramètres de synchronisation sauvegardés:', syncSettings);

    // Afficher une notification de succès (à implémenter)
  };

  return (
    <div className="boss-grid boss-grid-cols-1 lg:boss-grid-cols-3 boss-gap-6">
      {/* Panneau des intégrations */}
      <div className="lg:boss-col-span-2">
        <Card className="boss-mb-6">
          <CardHeader className="boss-border-b boss-border-gray-200">
            <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
              {__('Intégrations Google', 'boss-seo')}
            </h2>
          </CardHeader>
          <CardBody>
            <div className="boss-space-y-8">
              {/* Google Analytics 4 */}
              <div className="boss-border boss-border-gray-200 boss-rounded-lg boss-p-6">
                <div className="boss-flex boss-justify-between boss-items-start boss-mb-4">
                  <div className="boss-flex boss-items-center">
                    <div className="boss-bg-blue-100 boss-p-3 boss-rounded-lg boss-mr-4">
                      <Dashicon icon="chart-bar" className="boss-text-blue-600 boss-text-2xl" />
                    </div>
                    <div>
                      <h3 className="boss-text-lg boss-font-semibold boss-mb-1">
                        {__('Google Analytics 4', 'boss-seo')}
                      </h3>
                      <p className="boss-text-boss-gray boss-text-sm">
                        {__('Connectez-vous à Google Analytics 4 pour importer vos données de trafic et de comportement utilisateur.', 'boss-seo')}
                      </p>
                    </div>
                  </div>
                  <div>
                    {integrations.ga4.connected ? (
                      <div className="boss-flex boss-items-center boss-space-x-2">
                        <div className="boss-flex boss-items-center boss-text-green-600">
                          <Dashicon icon="yes-alt" className="boss-mr-1" />
                          <span>{__('Connecté', 'boss-seo')}</span>
                        </div>
                        <Button
                          isDestructive
                          isSmall
                          onClick={() => onDisconnectService('ga4')}
                          disabled={isLoading}
                        >
                          {__('Déconnecter', 'boss-seo')}
                        </Button>
                      </div>
                    ) : (
                      <Button
                        isPrimary
                        onClick={onConnectGA4}
                        disabled={isLoading}
                      >
                        {isLoading ? __('Connexion...', 'boss-seo') : __('Connecter', 'boss-seo')}
                      </Button>
                    )}
                  </div>
                </div>

                {integrations.ga4.connected && (
                  <div>
                    <div className="boss-mb-4">
                      <SelectControl
                        label={__('Sélectionner une propriété', 'boss-seo')}
                        value={integrations.ga4.properties.find(prop => prop.selected)?.id || ''}
                        options={[
                          { label: __('-- Sélectionner une propriété --', 'boss-seo'), value: '' },
                          ...integrations.ga4.properties.map(prop => ({
                            label: prop.name,
                            value: prop.id
                          }))
                        ]}
                        onChange={handleGA4PropertyChange}
                      />
                    </div>

                    <div className="boss-bg-gray-50 boss-p-4 boss-rounded-lg boss-text-sm">
                      <h4 className="boss-font-medium boss-mb-2">
                        {__('Métriques importées', 'boss-seo')}
                      </h4>
                      <ul className="boss-grid boss-grid-cols-2 boss-gap-2">
                        <li className="boss-flex boss-items-center">
                          <Dashicon icon="yes" className="boss-text-green-500 boss-mr-1" />
                          {__('Sessions', 'boss-seo')}
                        </li>
                        <li className="boss-flex boss-items-center">
                          <Dashicon icon="yes" className="boss-text-green-500 boss-mr-1" />
                          {__('Utilisateurs', 'boss-seo')}
                        </li>
                        <li className="boss-flex boss-items-center">
                          <Dashicon icon="yes" className="boss-text-green-500 boss-mr-1" />
                          {__('Pages vues', 'boss-seo')}
                        </li>
                        <li className="boss-flex boss-items-center">
                          <Dashicon icon="yes" className="boss-text-green-500 boss-mr-1" />
                          {__('Taux de rebond', 'boss-seo')}
                        </li>
                        <li className="boss-flex boss-items-center">
                          <Dashicon icon="yes" className="boss-text-green-500 boss-mr-1" />
                          {__('Durée moyenne de session', 'boss-seo')}
                        </li>
                        <li className="boss-flex boss-items-center">
                          <Dashicon icon="yes" className="boss-text-green-500 boss-mr-1" />
                          {__('Conversions', 'boss-seo')}
                        </li>
                      </ul>
                    </div>
                  </div>
                )}
              </div>

              {/* Google Search Console */}
              <div className="boss-border boss-border-gray-200 boss-rounded-lg boss-p-6">
                <div className="boss-flex boss-justify-between boss-items-start boss-mb-4">
                  <div className="boss-flex boss-items-center">
                    <div className="boss-bg-red-100 boss-p-3 boss-rounded-lg boss-mr-4">
                      <Dashicon icon="search" className="boss-text-red-600 boss-text-2xl" />
                    </div>
                    <div>
                      <h3 className="boss-text-lg boss-font-semibold boss-mb-1">
                        {__('Google Search Console', 'boss-seo')}
                      </h3>
                      <p className="boss-text-boss-gray boss-text-sm">
                        {__('Connectez-vous à Google Search Console pour importer vos données de performance dans les résultats de recherche.', 'boss-seo')}
                      </p>
                    </div>
                  </div>
                  <div>
                    {integrations.gsc.connected ? (
                      <div className="boss-flex boss-items-center boss-space-x-2">
                        <div className="boss-flex boss-items-center boss-text-green-600">
                          <Dashicon icon="yes-alt" className="boss-mr-1" />
                          <span>{__('Connecté', 'boss-seo')}</span>
                        </div>
                        <Button
                          isDestructive
                          isSmall
                          onClick={() => onDisconnectService('gsc')}
                          disabled={isLoading}
                        >
                          {__('Déconnecter', 'boss-seo')}
                        </Button>
                      </div>
                    ) : (
                      <Button
                        isPrimary
                        onClick={onConnectGSC}
                        disabled={isLoading}
                      >
                        {isLoading ? __('Connexion...', 'boss-seo') : __('Connecter', 'boss-seo')}
                      </Button>
                    )}
                  </div>
                </div>

                {integrations.gsc.connected && (
                  <div>
                    <div className="boss-mb-4">
                      <SelectControl
                        label={__('Sélectionner une propriété', 'boss-seo')}
                        value={integrations.gsc.properties.find(prop => prop.selected)?.id || ''}
                        options={[
                          { label: __('-- Sélectionner une propriété --', 'boss-seo'), value: '' },
                          ...integrations.gsc.properties.map(prop => ({
                            label: prop.name,
                            value: prop.id
                          }))
                        ]}
                        onChange={handleGSCPropertyChange}
                      />
                    </div>

                    <div className="boss-bg-gray-50 boss-p-4 boss-rounded-lg boss-text-sm">
                      <h4 className="boss-font-medium boss-mb-2">
                        {__('Métriques importées', 'boss-seo')}
                      </h4>
                      <ul className="boss-grid boss-grid-cols-2 boss-gap-2">
                        <li className="boss-flex boss-items-center">
                          <Dashicon icon="yes" className="boss-text-green-500 boss-mr-1" />
                          {__('Clics', 'boss-seo')}
                        </li>
                        <li className="boss-flex boss-items-center">
                          <Dashicon icon="yes" className="boss-text-green-500 boss-mr-1" />
                          {__('Impressions', 'boss-seo')}
                        </li>
                        <li className="boss-flex boss-items-center">
                          <Dashicon icon="yes" className="boss-text-green-500 boss-mr-1" />
                          {__('CTR', 'boss-seo')}
                        </li>
                        <li className="boss-flex boss-items-center">
                          <Dashicon icon="yes" className="boss-text-green-500 boss-mr-1" />
                          {__('Position moyenne', 'boss-seo')}
                        </li>
                        <li className="boss-flex boss-items-center">
                          <Dashicon icon="yes" className="boss-text-green-500 boss-mr-1" />
                          {__('Mots-clés', 'boss-seo')}
                        </li>
                        <li className="boss-flex boss-items-center">
                          <Dashicon icon="yes" className="boss-text-green-500 boss-mr-1" />
                          {__('Pages indexées', 'boss-seo')}
                        </li>
                      </ul>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </CardBody>
        </Card>
      </div>

      {/* Panneau des paramètres de synchronisation */}
      <div>
        <Card>
          <CardHeader className="boss-border-b boss-border-gray-200">
            <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
              {__('Paramètres de synchronisation', 'boss-seo')}
            </h2>
          </CardHeader>
          <CardBody>
            <div className="boss-space-y-4">
              <SelectControl
                label={__('Fréquence de synchronisation', 'boss-seo')}
                value={syncSettings.frequency}
                options={[
                  { label: __('Quotidienne', 'boss-seo'), value: 'daily' },
                  { label: __('Hebdomadaire', 'boss-seo'), value: 'weekly' },
                  { label: __('Toutes les 12 heures', 'boss-seo'), value: '12hours' },
                  { label: __('Toutes les 6 heures', 'boss-seo'), value: '6hours' }
                ]}
                onChange={(value) => updateSyncSetting('frequency', value)}
              />

              <ToggleControl
                label={__('Synchronisation automatique', 'boss-seo')}
                checked={syncSettings.autoSync}
                onChange={(value) => updateSyncSetting('autoSync', value)}
              />

              <div className="boss-border-t boss-border-gray-200 boss-pt-4 boss-mt-4">
                <h3 className="boss-font-medium boss-mb-3">
                  {__('Données à synchroniser', 'boss-seo')}
                </h3>

                <div className="boss-space-y-3">
                  <ToggleControl
                    label={__('Mots-clés et positions', 'boss-seo')}
                    checked={syncSettings.syncKeywords}
                    onChange={(value) => updateSyncSetting('syncKeywords', value)}
                  />

                  <ToggleControl
                    label={__('Pages et métriques', 'boss-seo')}
                    checked={syncSettings.syncPages}
                    onChange={(value) => updateSyncSetting('syncPages', value)}
                  />

                  <ToggleControl
                    label={__('Événements', 'boss-seo')}
                    checked={syncSettings.syncEvents}
                    onChange={(value) => updateSyncSetting('syncEvents', value)}
                  />

                  <ToggleControl
                    label={__('Objectifs et conversions', 'boss-seo')}
                    checked={syncSettings.syncGoals}
                    onChange={(value) => updateSyncSetting('syncGoals', value)}
                  />
                </div>
              </div>

              <div className="boss-border-t boss-border-gray-200 boss-pt-4 boss-mt-4">
                <TextControl
                  label={__('Période de conservation des données (jours)', 'boss-seo')}
                  value={syncSettings.retentionPeriod}
                  type="number"
                  min="30"
                  max="365"
                  onChange={(value) => updateSyncSetting('retentionPeriod', value)}
                />
              </div>
            </div>
          </CardBody>
          <CardFooter>
            <Button
              isPrimary
              onClick={handleSaveSettings}
              className="boss-w-full boss-mb-3"
              disabled={isLoading}
            >
              {__('Enregistrer les paramètres', 'boss-seo')}
            </Button>

            <div className="boss-space-y-2">
              <Button
                isSecondary
                onClick={onRefreshData}
                className="boss-w-full"
                disabled={isLoading}
                icon="update"
              >
                {isLoading ? __('Actualisation...', 'boss-seo') : __('Actualiser les données', 'boss-seo')}
              </Button>

              <Button
                isSecondary
                onClick={onClearCache}
                className="boss-w-full"
                disabled={isLoading}
                icon="trash"
              >
                {__('Vider le cache', 'boss-seo')}
              </Button>
            </div>
          </CardFooter>
        </Card>

        {/* Informations sur la dernière synchronisation */}
        {(integrations.ga4.connected || integrations.gsc.connected) && (
          <Card className="boss-mt-4">
            <CardHeader className="boss-border-b boss-border-gray-200">
              <h3 className="boss-text-md boss-font-semibold boss-text-boss-dark">
                {__('Statut de synchronisation', 'boss-seo')}
              </h3>
            </CardHeader>
            <CardBody>
              <div className="boss-space-y-3 boss-text-sm">
                {integrations.ga4.connected && (
                  <div className="boss-flex boss-justify-between boss-items-center">
                    <span>{__('Google Analytics 4:', 'boss-seo')}</span>
                    <span className="boss-text-boss-gray">
                      {integrations.ga4.last_sync
                        ? new Date(integrations.ga4.last_sync * 1000).toLocaleString()
                        : __('Jamais synchronisé', 'boss-seo')
                      }
                    </span>
                  </div>
                )}

                {integrations.gsc.connected && (
                  <div className="boss-flex boss-justify-between boss-items-center">
                    <span>{__('Google Search Console:', 'boss-seo')}</span>
                    <span className="boss-text-boss-gray">
                      {integrations.gsc.last_sync
                        ? new Date(integrations.gsc.last_sync * 1000).toLocaleString()
                        : __('Jamais synchronisé', 'boss-seo')
                      }
                    </span>
                  </div>
                )}
              </div>
            </CardBody>
          </Card>
        )}
      </div>
    </div>
  );
};

export default AnalyticsSetup;
