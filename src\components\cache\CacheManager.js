/**
 * Composant de gestion du cache Boss SEO.
 * 
 * Fournit une interface simple pour vider les différents types de cache.
 */

import { useState, useEffect } from '@wordpress/element';
import { Button, Card, CardBody, CardHeader, Notice, Spinner, Flex, FlexItem, __experimentalSpacer as Spacer } from '@wordpress/components';
import { __ } from '@wordpress/i18n';
import apiFetch from '@wordpress/api-fetch';

const CacheManager = () => {
  // États
  const [isLoading, setIsLoading] = useState(false);
  const [cacheStats, setCacheStats] = useState(null);
  const [notices, setNotices] = useState([]);
  const [operationInProgress, setOperationInProgress] = useState(null);

  // Charger les statistiques du cache au montage
  useEffect(() => {
    loadCacheStats();
  }, []);

  /**
   * Charge les statistiques du cache
   */
  const loadCacheStats = async () => {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/cache/stats',
        method: 'GET'
      });

      if (response.success) {
        setCacheStats(response.stats);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des statistiques du cache:', error);
      addNotice('error', __('Impossible de charger les statistiques du cache.', 'boss-seo'));
    }
  };

  /**
   * Ajoute une notification
   */
  const addNotice = (type, message) => {
    const notice = {
      id: Date.now(),
      type,
      message
    };
    setNotices(prev => [...prev, notice]);

    // Supprimer automatiquement après 5 secondes
    setTimeout(() => {
      setNotices(prev => prev.filter(n => n.id !== notice.id));
    }, 5000);
  };

  /**
   * Vide tout le cache
   */
  const flushAllCache = async () => {
    setIsLoading(true);
    setOperationInProgress('all');

    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/cache/flush-all',
        method: 'DELETE'
      });

      if (response.success) {
        addNotice('success', response.message);
        await loadCacheStats(); // Recharger les stats
      } else {
        addNotice('error', __('Erreur lors du vidage du cache.', 'boss-seo'));
      }
    } catch (error) {
      console.error('Erreur lors du vidage du cache:', error);
      addNotice('error', __('Erreur lors du vidage du cache.', 'boss-seo'));
    } finally {
      setIsLoading(false);
      setOperationInProgress(null);
    }
  };

  /**
   * Vide le cache d'un module spécifique
   */
  const flushModuleCache = async (module) => {
    setIsLoading(true);
    setOperationInProgress(module);

    try {
      const response = await apiFetch({
        path: `/boss-seo/v1/cache/flush/${module}`,
        method: 'DELETE'
      });

      if (response.success) {
        addNotice('success', response.message);
        await loadCacheStats(); // Recharger les stats
      } else {
        addNotice('error', __('Erreur lors du vidage du cache du module.', 'boss-seo'));
      }
    } catch (error) {
      console.error(`Erreur lors du vidage du cache du module ${module}:`, error);
      addNotice('error', __('Erreur lors du vidage du cache du module.', 'boss-seo'));
    } finally {
      setIsLoading(false);
      setOperationInProgress(null);
    }
  };

  /**
   * Vide seulement le cache des assets
   */
  const flushAssetsCache = async () => {
    setIsLoading(true);
    setOperationInProgress('assets');

    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/cache/flush-assets',
        method: 'DELETE'
      });

      if (response.success) {
        addNotice('success', response.message);
        await loadCacheStats(); // Recharger les stats
        
        // Recharger la page pour appliquer la nouvelle version des assets
        setTimeout(() => {
          window.location.reload();
        }, 1000);
      } else {
        addNotice('error', __('Erreur lors du vidage du cache des assets.', 'boss-seo'));
      }
    } catch (error) {
      console.error('Erreur lors du vidage du cache des assets:', error);
      addNotice('error', __('Erreur lors du vidage du cache des assets.', 'boss-seo'));
    } finally {
      setIsLoading(false);
      setOperationInProgress(null);
    }
  };

  /**
   * Supprime une notification
   */
  const removeNotice = (noticeId) => {
    setNotices(prev => prev.filter(n => n.id !== noticeId));
  };

  // Modules disponibles
  const modules = [
    { key: 'optimizer', label: __('Boss Optimizer', 'boss-seo'), description: __('Cache des optimisations et analyses SEO', 'boss-seo') },
    { key: 'analytics', label: __('Analytics', 'boss-seo'), description: __('Cache des données Google Analytics et Search Console', 'boss-seo') },
    { key: 'ecommerce', label: __('E-commerce', 'boss-seo'), description: __('Cache des données WooCommerce', 'boss-seo') },
    { key: 'technical', label: __('Technique', 'boss-seo'), description: __('Cache des analyses techniques', 'boss-seo') },
    { key: 'local', label: __('SEO Local', 'boss-seo'), description: __('Cache des données de référencement local', 'boss-seo') },
    { key: 'structured_schemas', label: __('Schémas Structurés', 'boss-seo'), description: __('Cache des schémas JSON-LD', 'boss-seo') }
  ];

  return (
    <div className="boss-cache-manager">
      {/* Notifications */}
      {notices.map(notice => (
        <Notice
          key={notice.id}
          status={notice.type}
          onRemove={() => removeNotice(notice.id)}
          isDismissible
        >
          {notice.message}
        </Notice>
      ))}

      {/* Actions principales */}
      <Card>
        <CardHeader>
          <h2>{__('Gestion du Cache', 'boss-seo')}</h2>
        </CardHeader>
        <CardBody>
          <p>{__('Utilisez ces outils pour vider le cache et résoudre les problèmes d\'affichage.', 'boss-seo')}</p>
          
          <Spacer marginY={4} />

          <Flex gap={3} wrap>
            <FlexItem>
              <Button
                variant="primary"
                onClick={flushAllCache}
                disabled={isLoading}
                icon={operationInProgress === 'all' ? <Spinner /> : null}
              >
                {operationInProgress === 'all' 
                  ? __('Vidage en cours...', 'boss-seo')
                  : __('Vider tout le cache', 'boss-seo')
                }
              </Button>
            </FlexItem>
            
            <FlexItem>
              <Button
                variant="secondary"
                onClick={flushAssetsCache}
                disabled={isLoading}
                icon={operationInProgress === 'assets' ? <Spinner /> : null}
              >
                {operationInProgress === 'assets' 
                  ? __('Vidage en cours...', 'boss-seo')
                  : __('Vider cache CSS/JS', 'boss-seo')
                }
              </Button>
            </FlexItem>
          </Flex>

          {cacheStats && (
            <>
              <Spacer marginY={4} />
              <div className="cache-stats">
                <h3>{__('Statistiques du Cache', 'boss-seo')}</h3>
                <ul>
                  <li><strong>{__('Version des assets:', 'boss-seo')}</strong> {cacheStats.assets_version}</li>
                  <li><strong>{__('Transients actifs:', 'boss-seo')}</strong> {cacheStats.transients_count}</li>
                  <li><strong>{__('Mode debug:', 'boss-seo')}</strong> {cacheStats.debug_mode ? __('Activé', 'boss-seo') : __('Désactivé', 'boss-seo')}</li>
                </ul>
              </div>
            </>
          )}
        </CardBody>
      </Card>

      {/* Cache par module */}
      <Spacer marginY={6} />
      
      <Card>
        <CardHeader>
          <h3>{__('Cache par Module', 'boss-seo')}</h3>
        </CardHeader>
        <CardBody>
          <p>{__('Videz le cache d\'un module spécifique si vous rencontrez des problèmes.', 'boss-seo')}</p>
          
          <Spacer marginY={4} />

          <div className="modules-grid" style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '16px' }}>
            {modules.map(module => (
              <div key={module.key} className="module-cache-item" style={{ 
                border: '1px solid #ddd', 
                borderRadius: '4px', 
                padding: '16px',
                backgroundColor: '#f9f9f9'
              }}>
                <h4 style={{ margin: '0 0 8px 0' }}>{module.label}</h4>
                <p style={{ margin: '0 0 12px 0', fontSize: '14px', color: '#666' }}>
                  {module.description}
                </p>
                <Button
                  variant="secondary"
                  size="small"
                  onClick={() => flushModuleCache(module.key)}
                  disabled={isLoading}
                  icon={operationInProgress === module.key ? <Spinner /> : null}
                >
                  {operationInProgress === module.key 
                    ? __('Vidage...', 'boss-seo')
                    : __('Vider', 'boss-seo')
                  }
                </Button>
                {cacheStats && cacheStats.modules_status && (
                  <span style={{ 
                    marginLeft: '8px', 
                    fontSize: '12px',
                    color: cacheStats.modules_status[module.key] ? '#46b450' : '#dc3232'
                  }}>
                    {cacheStats.modules_status[module.key] ? '●' : '○'}
                  </span>
                )}
              </div>
            ))}
          </div>
        </CardBody>
      </Card>
    </div>
  );
};

export default CacheManager;
