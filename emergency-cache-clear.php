<?php
/**
 * SCRIPT D'URGENCE - NETTOYAGE COMPLET DU CACHE
 * 
 * À exécuter depuis l'admin WordPress : /wp-admin/admin.php?page=emergency-cache-clear
 * OU directement via URL : /wp-content/plugins/boss-seo/emergency-cache-clear.php
 */

// Si appelé directement, charger WordPress
if (!defined('ABSPATH')) {
    // Essayer de charger WordPress
    $wp_load_paths = [
        '../../../wp-load.php',
        '../../../../wp-load.php',
        '../../../../../wp-load.php'
    ];
    
    $wp_loaded = false;
    foreach ($wp_load_paths as $path) {
        if (file_exists(__DIR__ . '/' . $path)) {
            require_once __DIR__ . '/' . $path;
            $wp_loaded = true;
            break;
        }
    }
    
    if (!$wp_loaded) {
        die('❌ Impossible de charger WordPress. Utilisez l\'admin WordPress.');
    }
}

// Vérifier les permissions
if (!current_user_can('manage_options')) {
    die('❌ Permissions insuffisantes.');
}

echo '<div style="font-family: Arial, sans-serif; max-width: 800px; margin: 20px auto; padding: 20px; background: #f9f9f9; border-radius: 8px;">';
echo '<h1 style="color: #d63384;">🚨 NETTOYAGE D\'URGENCE - BOSS SEO</h1>';

$cleared = [];
$errors = [];

// 1. Vider le cache WordPress
try {
    if (function_exists('wp_cache_flush')) {
        wp_cache_flush();
        $cleared[] = '✅ Cache WordPress (wp_cache_flush)';
    }
} catch (Exception $e) {
    $errors[] = '❌ Cache WordPress: ' . $e->getMessage();
}

// 2. Supprimer TOUS les transients Boss SEO
try {
    global $wpdb;
    
    $deleted = $wpdb->query(
        "DELETE FROM {$wpdb->options} 
         WHERE option_name LIKE '_transient_boss_%' 
         OR option_name LIKE '_transient_timeout_boss_%'"
    );
    
    $cleared[] = "✅ Transients Boss SEO ($deleted supprimés)";
} catch (Exception $e) {
    $errors[] = '❌ Transients: ' . $e->getMessage();
}

// 3. Supprimer les options de cache
try {
    $cache_options = [
        'boss_seo_cache_version',
        'boss_analytics_cache_version',
        'boss_optimizer_cache_version'
    ];
    
    foreach ($cache_options as $option) {
        delete_option($option);
        delete_transient($option);
    }
    
    $cleared[] = '✅ Options de cache supprimées';
} catch (Exception $e) {
    $errors[] = '❌ Options de cache: ' . $e->getMessage();
}

// 4. Vider les caches de plugins populaires
$cache_plugins = [];

if (function_exists('rocket_clean_domain')) {
    rocket_clean_domain();
    $cache_plugins[] = 'WP Rocket';
}

if (function_exists('w3tc_flush_all')) {
    w3tc_flush_all();
    $cache_plugins[] = 'W3 Total Cache';
}

if (function_exists('wp_cache_clear_cache')) {
    wp_cache_clear_cache();
    $cache_plugins[] = 'WP Super Cache';
}

if (class_exists('LiteSpeed_Cache_API')) {
    LiteSpeed_Cache_API::purge_all();
    $cache_plugins[] = 'LiteSpeed Cache';
}

if (class_exists('autoptimizeCache')) {
    autoptimizeCache::clearall();
    $cache_plugins[] = 'Autoptimize';
}

if (!empty($cache_plugins)) {
    $cleared[] = '✅ Plugins de cache: ' . implode(', ', $cache_plugins);
}

// 5. Vider l'opcode PHP
try {
    if (function_exists('opcache_reset')) {
        opcache_reset();
        $cleared[] = '✅ OpCode PHP vidé';
    }
} catch (Exception $e) {
    $errors[] = '❌ OpCode PHP: ' . $e->getMessage();
}

// 6. Régénérer les permaliens
try {
    flush_rewrite_rules();
    $cleared[] = '✅ Permaliens régénérés';
} catch (Exception $e) {
    $errors[] = '❌ Permaliens: ' . $e->getMessage();
}

// 7. Forcer la recompilation des assets
try {
    // Mettre à jour la version des assets
    update_option('boss_seo_assets_version', time());
    $cleared[] = '✅ Version des assets mise à jour';
} catch (Exception $e) {
    $errors[] = '❌ Assets: ' . $e->getMessage();
}

// Afficher les résultats
echo '<h2 style="color: #198754;">✅ Éléments nettoyés</h2>';
echo '<ul>';
foreach ($cleared as $item) {
    echo "<li>$item</li>";
}
echo '</ul>';

if (!empty($errors)) {
    echo '<h2 style="color: #dc3545;">❌ Erreurs</h2>';
    echo '<ul>';
    foreach ($errors as $error) {
        echo "<li>$error</li>";
    }
    echo '</ul>';
}

// Instructions
echo '<h2 style="color: #0d6efd;">📋 Instructions</h2>';
echo '<div style="background: #e7f3ff; padding: 15px; border-radius: 5px; border-left: 4px solid #0d6efd;">';
echo '<ol>';
echo '<li><strong>Fermez TOUS les onglets</strong> de votre site</li>';
echo '<li><strong>Videz le cache de votre navigateur</strong> (Ctrl+Shift+Delete)</li>';
echo '<li><strong>Ouvrez un nouvel onglet en navigation privée</strong></li>';
echo '<li><strong>Allez sur Boss SEO</strong> et vérifiez que tout fonctionne</li>';
echo '</ol>';
echo '</div>';

// Boutons d'action
echo '<h2 style="color: #6f42c1;">🔄 Actions rapides</h2>';
echo '<p>';
echo '<a href="' . admin_url('admin.php?page=boss-seo') . '" style="background: #0d6efd; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;">🏠 Aller au Dashboard</a>';
echo '<a href="' . admin_url('admin.php?page=boss-seo-content') . '" style="background: #198754; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;">📝 Optimisation de contenu</a>';
echo '<a href="javascript:location.reload(true)" style="background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">🔄 Recharger cette page</a>';
echo '</p>';

echo '</div>';

// Script JavaScript pour vider le cache du navigateur
echo '<script>';
echo 'console.log("🧹 Nettoyage d\'urgence Boss SEO terminé");';
echo 'if ("caches" in window) {';
echo '  caches.keys().then(function(names) {';
echo '    names.forEach(function(name) {';
echo '      caches.delete(name);';
echo '    });';
echo '    console.log("✅ Cache du navigateur vidé");';
echo '  });';
echo '}';
echo '</script>';
?>
