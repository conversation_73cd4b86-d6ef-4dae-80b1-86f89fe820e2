<?php
/**
 * La classe API REST du module Boss Optimizer.
 *
 * Cette classe gère les endpoints API REST pour le module Boss Optimizer.
 *
 * @link       https://bossseo.com
 * @since      1.1.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 */

/**
 * La classe API REST du module Boss Optimizer.
 *
 * @since      1.1.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 * <AUTHOR> SEO Team
 */
class Boss_Optimizer_API {

    /**
     * L'identifiant unique de ce plugin.
     *
     * @since    1.1.0
     * @access   protected
     * @var      string    $plugin_name    La chaîne utilisée pour identifier ce plugin.
     */
    protected $plugin_name;

    /**
     * La version actuelle du plugin.
     *
     * @since    1.1.0
     * @access   protected
     * @var      string    $version    La version actuelle du plugin.
     */
    protected $version;

    /**
     * Instance de la classe de gestion des contenus.
     *
     * @since    1.1.0
     * @access   protected
     * @var      Boss_Optimizer_Content    $content    Gère les contenus.
     */
    protected $content;

    /**
     * Instance de la classe d'analyse SEO.
     *
     * @since    1.1.0
     * @access   protected
     * @var      Boss_Optimizer_Analysis    $analysis    Effectue les analyses SEO.
     */
    protected $analysis;

    /**
     * Instance de la classe de recommandations.
     *
     * @since    1.1.0
     * @access   protected
     * @var      Boss_Optimizer_Recommendations    $recommendations    Gère les recommandations.
     */
    protected $recommendations;

    /**
     * Instance de la classe d'intégration IA.
     *
     * @since    1.1.0
     * @access   protected
     * @var      Boss_Optimizer_AI    $ai    Gère l'intégration avec les services d'IA.
     */
    protected $ai;

    /**
     * Instance de la classe de paramètres.
     *
     * @since    1.1.0
     * @access   protected
     * @var      Boss_Optimizer_Settings    $settings    Gère les paramètres du module.
     */
    protected $settings;

    /**
     * Instance de la classe d'analyse technique.
     *
     * @since    1.1.0
     * @access   protected
     * @var      Boss_Optimizer_Technical_Analysis    $technical_analysis    Gère l'analyse technique.
     */
    protected $technical_analysis;

    /**
     * Instance de la classe de performance.
     *
     * @since    1.1.0
     * @access   protected
     * @var      Boss_Optimizer_Performance    $performance    Gère les performances.
     */
    protected $performance;

    /**
     * Instance de la classe de services externes.
     *
     * @since    1.1.0
     * @access   protected
     * @var      Boss_Optimizer_External_Services    $external_services    Gère les services externes.
     */
    protected $external_services;

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.1.0
     * @param    string                          $plugin_name       Le nom du plugin.
     * @param    string                          $version           La version du plugin.
     * @param    Boss_Optimizer_Content          $content           Instance de la classe de gestion des contenus.
     * @param    Boss_Optimizer_Analysis         $analysis          Instance de la classe d'analyse SEO.
     * @param    Boss_Optimizer_Recommendations  $recommendations   Instance de la classe de recommandations.
     * @param    Boss_Optimizer_AI               $ai                Instance de la classe d'intégration IA.
     * @param    Boss_Optimizer_Settings         $settings          Instance de la classe de paramètres.
     */
    public function __construct( $plugin_name, $version, $content, $analysis, $recommendations, $ai, $settings ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
        $this->content = $content;
        $this->analysis = $analysis;
        $this->recommendations = $recommendations;
        $this->ai = $ai;
        $this->settings = $settings;

        // Initialiser les nouvelles classes
        $this->technical_analysis = Boss_Optimizer_Technical_Analysis::get_instance();
        $this->performance = Boss_Optimizer_Performance::get_instance();
        $this->external_services = Boss_Optimizer_External_Services::get_instance();
    }

    /**
     * Enregistre les routes API REST.
     *
     * @since    1.1.0
     */
    public function register_routes() {
        $namespace = $this->plugin_name . '/v1';

        // Charger les dépendances pour les API externes
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/api/class-boss-seo-serpapi.php';
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/api/class-boss-seo-image-api.php';

        // Route pour récupérer les contenus
        register_rest_route( $namespace, '/contents', array(
            array(
                'methods'             => WP_REST_Server::READABLE,
                'callback'            => array( $this, 'get_contents' ),
                'permission_callback' => array( $this, 'get_contents_permissions_check' ),
                'args'                => array(
                    'content_type' => array(
                        'default'           => 'all',
                        'sanitize_callback' => 'sanitize_text_field'
                    ),
                    'seo_score' => array(
                        'default'           => 'all',
                        'sanitize_callback' => 'sanitize_text_field'
                    ),
                    'status' => array(
                        'default'           => 'all',
                        'sanitize_callback' => 'sanitize_text_field'
                    ),
                    'author' => array(
                        'default'           => 'all',
                        'sanitize_callback' => 'absint'
                    ),
                    'date_range' => array(
                        'default'           => 'all',
                        'sanitize_callback' => 'sanitize_text_field'
                    ),
                    'search' => array(
                        'default'           => '',
                        'sanitize_callback' => 'sanitize_text_field'
                    ),
                    'page' => array(
                        'default'           => 1,
                        'sanitize_callback' => 'absint'
                    ),
                    'per_page' => array(
                        'default'           => 10,
                        'sanitize_callback' => 'absint'
                    ),
                    'orderby' => array(
                        'default'           => 'date',
                        'sanitize_callback' => 'sanitize_text_field'
                    ),
                    'order' => array(
                        'default'           => 'DESC',
                        'sanitize_callback' => 'sanitize_text_field'
                    )
                )
            )
        ) );

        // Route pour récupérer un contenu spécifique
        register_rest_route( $namespace, '/contents/(?P<id>\d+)', array(
            array(
                'methods'             => WP_REST_Server::READABLE,
                'callback'            => array( $this, 'get_content' ),
                'permission_callback' => array( $this, 'get_content_permissions_check' ),
                'args'                => array(
                    'id' => array(
                        'validate_callback' => function( $param ) {
                            return is_numeric( $param );
                        }
                    )
                )
            )
        ) );

        // Route pour analyser un contenu
        register_rest_route( $namespace, '/analyze', array(
            array(
                'methods'             => WP_REST_Server::CREATABLE,
                'callback'            => array( $this, 'analyze_content' ),
                'permission_callback' => array( $this, 'analyze_content_permissions_check' ),
                'args'                => array(
                    'id' => array(
                        'required'          => true,
                        'validate_callback' => function( $param ) {
                            return is_numeric( $param );
                        }
                    )
                )
            )
        ) );

        // Route pour analyser plusieurs contenus
        register_rest_route( $namespace, '/analyze/bulk', array(
            array(
                'methods'             => WP_REST_Server::CREATABLE,
                'callback'            => array( $this, 'analyze_contents_bulk' ),
                'permission_callback' => array( $this, 'analyze_content_permissions_check' ),
                'args'                => array(
                    'ids' => array(
                        'required'          => true,
                        'validate_callback' => function( $param ) {
                            return is_array( $param );
                        }
                    )
                )
            )
        ) );

        // Route pour analyser tous les contenus
        register_rest_route( $namespace, '/analyze/all', array(
            array(
                'methods'             => WP_REST_Server::CREATABLE,
                'callback'            => array( $this, 'analyze_all_contents' ),
                'permission_callback' => array( $this, 'analyze_content_permissions_check' ),
                'args'                => array(
                    'filters' => array(
                        'default'           => array(),
                        'validate_callback' => function( $param ) {
                            return is_array( $param );
                        }
                    )
                )
            )
        ) );

        // Route pour ajouter des balises à plusieurs contenus
        register_rest_route( $namespace, '/tags/add', array(
            array(
                'methods'             => WP_REST_Server::CREATABLE,
                'callback'            => array( $this, 'add_tags_to_contents' ),
                'permission_callback' => array( $this, 'manage_content_permissions_check' ),
                'args'                => array(
                    'ids' => array(
                        'required'          => true,
                        'validate_callback' => function( $param ) {
                            return is_array( $param );
                        }
                    ),
                    'tags' => array(
                        'required'          => true,
                        'validate_callback' => function( $param ) {
                            return is_array( $param );
                        }
                    )
                )
            )
        ) );

        // Route pour changer la catégorie de plusieurs contenus
        register_rest_route( $namespace, '/category/change', array(
            array(
                'methods'             => WP_REST_Server::CREATABLE,
                'callback'            => array( $this, 'change_category_for_contents' ),
                'permission_callback' => array( $this, 'manage_content_permissions_check' ),
                'args'                => array(
                    'ids' => array(
                        'required'          => true,
                        'validate_callback' => function( $param ) {
                            return is_array( $param );
                        }
                    ),
                    'category_id' => array(
                        'required'          => true,
                        'validate_callback' => function( $param ) {
                            return is_numeric( $param );
                        }
                    )
                )
            )
        ) );

        // Route pour changer l'auteur de plusieurs contenus
        register_rest_route( $namespace, '/author/change', array(
            array(
                'methods'             => WP_REST_Server::CREATABLE,
                'callback'            => array( $this, 'change_author_for_contents' ),
                'permission_callback' => array( $this, 'manage_content_permissions_check' ),
                'args'                => array(
                    'ids' => array(
                        'required'          => true,
                        'validate_callback' => function( $param ) {
                            return is_array( $param );
                        }
                    ),
                    'author_id' => array(
                        'required'          => true,
                        'validate_callback' => function( $param ) {
                            return is_numeric( $param );
                        }
                    )
                )
            )
        ) );

        // Route pour optimiser un contenu
        register_rest_route( $namespace, '/optimize/(?P<id>\d+)', array(
            array(
                'methods'             => WP_REST_Server::CREATABLE,
                'callback'            => array( $this, 'optimize_content' ),
                'permission_callback' => array( $this, 'optimize_content_permissions_check' ),
                'args'                => array(
                    'id' => array(
                        'validate_callback' => function( $param ) {
                            return is_numeric( $param );
                        }
                    ),
                    'settings' => array(
                        'default'           => array(),
                        'validate_callback' => function( $param ) {
                            return is_array( $param );
                        }
                    )
                )
            )
        ) );

        // Route pour optimiser plusieurs contenus
        register_rest_route( $namespace, '/optimize/bulk', array(
            array(
                'methods'             => WP_REST_Server::CREATABLE,
                'callback'            => array( $this, 'optimize_contents_bulk' ),
                'permission_callback' => array( $this, 'optimize_content_permissions_check' ),
                'args'                => array(
                    'ids' => array(
                        'required'          => true,
                        'validate_callback' => function( $param ) {
                            return is_array( $param );
                        }
                    ),
                    'settings' => array(
                        'default'           => array(),
                        'validate_callback' => function( $param ) {
                            return is_array( $param );
                        }
                    )
                )
            )
        ) );

        // Route pour optimiser tous les contenus
        register_rest_route( $namespace, '/optimize/all', array(
            array(
                'methods'             => WP_REST_Server::CREATABLE,
                'callback'            => array( $this, 'optimize_all_contents' ),
                'permission_callback' => array( $this, 'optimize_content_permissions_check' ),
                'args'                => array(
                    'settings' => array(
                        'default'           => array(),
                        'validate_callback' => function( $param ) {
                            return is_array( $param );
                        }
                    ),
                    'filters' => array(
                        'default'           => array(),
                        'validate_callback' => function( $param ) {
                            return is_array( $param );
                        }
                    )
                )
            )
        ) );

        // Route pour récupérer les recommandations d'un contenu
        register_rest_route( $namespace, '/recommendations/(?P<id>\d+)', array(
            array(
                'methods'             => WP_REST_Server::READABLE,
                'callback'            => array( $this, 'get_recommendations' ),
                'permission_callback' => array( $this, 'get_recommendations_permissions_check' ),
                'args'                => array(
                    'id' => array(
                        'validate_callback' => function( $param ) {
                            return is_numeric( $param );
                        }
                    )
                )
            )
        ) );

        // Route pour appliquer une recommandation
        register_rest_route( $namespace, '/recommendations/apply', array(
            array(
                'methods'             => WP_REST_Server::CREATABLE,
                'callback'            => array( $this, 'apply_recommendation' ),
                'permission_callback' => array( $this, 'apply_recommendation_permissions_check' ),
                'args'                => array(
                    'post_id' => array(
                        'required'          => true,
                        'validate_callback' => function( $param ) {
                            return is_numeric( $param );
                        }
                    ),
                    'recommendation_id' => array(
                        'required'          => true,
                        'validate_callback' => function( $param ) {
                            return is_numeric( $param );
                        }
                    )
                )
            )
        ) );

        // Route pour vérifier une clé API
        register_rest_route( $namespace, '/verify-api-key', array(
            array(
                'methods'             => WP_REST_Server::CREATABLE,
                'callback'            => array( $this, 'verify_api_key' ),
                'permission_callback' => array( $this, 'verify_api_key_permissions_check' ),
                'args'                => array(
                    'provider' => array(
                        'required'          => true,
                        'validate_callback' => function( $param ) {
                            return in_array( $param, array( 'openai', 'anthropic', 'gemini', 'semrush', 'moz', 'ahrefs', 'majestic', 'serpapi' ) );
                        }
                    ),
                    'api_key' => array(
                        'required'          => true,
                        'validate_callback' => function( $param ) {
                            return is_string( $param ) && ! empty( $param );
                        }
                    )
                )
            )
        ) );

        // Route pour récupérer les paramètres d'IA
        register_rest_route( $namespace, '/ai/settings', array(
            array(
                'methods'             => WP_REST_Server::READABLE,
                'callback'            => array( $this, 'get_ai_settings' ),
                'permission_callback' => array( $this, 'get_settings_permissions_check' )
            ),
            array(
                'methods'             => WP_REST_Server::CREATABLE,
                'callback'            => array( $this, 'save_ai_settings' ),
                'permission_callback' => array( $this, 'save_settings_permissions_check' ),
                'args'                => array(
                    'settings' => array(
                        'required'          => true,
                        'validate_callback' => function( $param ) {
                            return is_array( $param );
                        }
                    )
                )
            )
        ) );

        // Route pour tester une clé API d'IA
        register_rest_route( $namespace, '/ai/test-api-key', array(
            array(
                'methods'             => WP_REST_Server::CREATABLE,
                'callback'            => array( $this, 'test_ai_api_key' ),
                'permission_callback' => array( $this, 'verify_api_key_permissions_check' ),
                'args'                => array(
                    'provider' => array(
                        'required'          => true,
                        'validate_callback' => function( $param ) {
                            return in_array( $param, array( 'openai', 'anthropic', 'gemini' ) );
                        }
                    ),
                    'api_key' => array(
                        'required'          => true,
                        'validate_callback' => function( $param ) {
                            return is_string( $param ) && ! empty( $param );
                        }
                    )
                )
            )
        ) );

        // Route pour générer du contenu avec l'IA
        register_rest_route( $namespace, '/ai/generate-content', array(
            array(
                'methods'             => WP_REST_Server::CREATABLE,
                'callback'            => array( $this, 'generate_content_with_ai' ),
                'permission_callback' => array( $this, 'generate_content_permissions_check' ),
                'args'                => array(
                    'prompt' => array(
                        'required'          => true,
                        'validate_callback' => function( $param ) {
                            // Validation plus détaillée du prompt
                            if ( ! is_string( $param ) ) {
                                return false;
                            }

                            $trimmed = trim( $param );
                            if ( empty( $trimmed ) ) {
                                return false;
                            }

                            // Le prompt doit faire au moins 5 caractères
                            if ( strlen( $trimmed ) < 5 ) {
                                return false;
                            }

                            return true;
                        },
                        'sanitize_callback' => function( $param ) {
                            return sanitize_textarea_field( $param );
                        }
                    ),
                    'options' => array(
                        'default'           => array(),
                        'validate_callback' => function( $param ) {
                            return is_array( $param );
                        },
                        'sanitize_callback' => function( $param ) {
                            return is_array( $param ) ? $param : array();
                        }
                    )
                )
            )
        ) );

        // Route pour récupérer les paramètres des services externes
        register_rest_route( $namespace, '/external-services/settings', array(
            array(
                'methods'             => WP_REST_Server::READABLE,
                'callback'            => array( $this, 'get_external_services_settings' ),
                'permission_callback' => array( $this, 'get_settings_permissions_check' )
            ),
            array(
                'methods'             => WP_REST_Server::CREATABLE,
                'callback'            => array( $this, 'save_external_services_settings' ),
                'permission_callback' => array( $this, 'save_settings_permissions_check' ),
                'args'                => array(
                    'settings' => array(
                        'required'          => true,
                        'validate_callback' => function( $param ) {
                            return is_array( $param );
                        }
                    )
                )
            )
        ) );

        // Route pour vérifier une clé API de service externe
        register_rest_route( $namespace, '/external-services/verify-api-key', array(
            array(
                'methods'             => WP_REST_Server::CREATABLE,
                'callback'            => array( $this, 'verify_external_service_api_key' ),
                'permission_callback' => array( $this, 'verify_api_key_permissions_check' ),
                'args'                => array(
                    'service' => array(
                        'required'          => true,
                        'validate_callback' => function( $param ) {
                            return in_array( $param, array( 'google_pagespeed', 'tinypng', 'imagify', 'optimole', 'semrush', 'moz', 'ahrefs', 'majestic', 'serpapi' ) );
                        }
                    ),
                    'api_key' => array(
                        'required'          => true,
                        'validate_callback' => function( $param ) {
                            return is_string( $param ) && ! empty( $param );
                        }
                    )
                )
            )
        ) );

        // Route pour enregistrer les paramètres
        register_rest_route( $namespace, '/save-settings', array(
            array(
                'methods'             => WP_REST_Server::CREATABLE,
                'callback'            => array( $this, 'save_settings' ),
                'permission_callback' => array( $this, 'save_settings_permissions_check' ),
                'args'                => array(
                    'settings' => array(
                        'required'          => true,
                        'validate_callback' => function( $param ) {
                            return is_array( $param );
                        }
                    )
                )
            )
        ) );

        // Routes pour les mots-clés (SerpAPI)
        register_rest_route( $namespace, '/keywords/search', array(
            array(
                'methods'             => WP_REST_Server::READABLE,
                'callback'            => array( $this, 'search_keywords' ),
                'permission_callback' => array( $this, 'keywords_permissions_check' ),
                'args'                => array(
                    'query' => array(
                        'required'          => true,
                        'validate_callback' => function( $param ) {
                            return is_string( $param ) && ! empty( $param );
                        }
                    ),
                    'language' => array(
                        'default'           => 'fr',
                        'sanitize_callback' => 'sanitize_text_field'
                    ),
                    'country' => array(
                        'default'           => 'fr',
                        'sanitize_callback' => 'sanitize_text_field'
                    )
                )
            )
        ) );

        register_rest_route( $namespace, '/keywords/related', array(
            array(
                'methods'             => WP_REST_Server::READABLE,
                'callback'            => array( $this, 'get_related_keywords' ),
                'permission_callback' => array( $this, 'keywords_permissions_check' ),
                'args'                => array(
                    'keyword' => array(
                        'required'          => true,
                        'validate_callback' => function( $param ) {
                            return is_string( $param ) && ! empty( $param );
                        }
                    ),
                    'language' => array(
                        'default'           => 'fr',
                        'sanitize_callback' => 'sanitize_text_field'
                    ),
                    'country' => array(
                        'default'           => 'fr',
                        'sanitize_callback' => 'sanitize_text_field'
                    )
                )
            )
        ) );

        register_rest_route( $namespace, '/keywords/longtail', array(
            array(
                'methods'             => WP_REST_Server::READABLE,
                'callback'            => array( $this, 'get_long_tail_keywords' ),
                'permission_callback' => array( $this, 'keywords_permissions_check' ),
                'args'                => array(
                    'keyword' => array(
                        'required'          => true,
                        'validate_callback' => function( $param ) {
                            return is_string( $param ) && ! empty( $param );
                        }
                    ),
                    'language' => array(
                        'default'           => 'fr',
                        'sanitize_callback' => 'sanitize_text_field'
                    ),
                    'country' => array(
                        'default'           => 'fr',
                        'sanitize_callback' => 'sanitize_text_field'
                    )
                )
            )
        ) );

        register_rest_route( $namespace, '/keywords/ai-suggestions', array(
            array(
                'methods'             => WP_REST_Server::CREATABLE,
                'callback'            => array( $this, 'get_ai_suggested_keywords' ),
                'permission_callback' => array( $this, 'keywords_permissions_check' ),
                'args'                => array(
                    'topic' => array(
                        'required'          => true,
                        'validate_callback' => function( $param ) {
                            return is_string( $param ) && ! empty( $param );
                        }
                    ),
                    'content_type' => array(
                        'default'           => 'article',
                        'sanitize_callback' => 'sanitize_text_field'
                    )
                )
            )
        ) );

        register_rest_route( $namespace, '/keywords/save', array(
            array(
                'methods'             => WP_REST_Server::CREATABLE,
                'callback'            => array( $this, 'save_keyword' ),
                'permission_callback' => array( $this, 'keywords_permissions_check' ),
                'args'                => array(
                    'keyword' => array(
                        'required'          => true,
                        'validate_callback' => function( $param ) {
                            return is_array( $param );
                        }
                    )
                )
            )
        ) );

        register_rest_route( $namespace, '/keywords/saved', array(
            array(
                'methods'             => WP_REST_Server::READABLE,
                'callback'            => array( $this, 'get_saved_keywords' ),
                'permission_callback' => array( $this, 'keywords_permissions_check' )
            )
        ) );

        // Routes pour les images (Pexels, Unsplash, Pixabay)
        register_rest_route( $namespace, '/images/search', array(
            array(
                'methods'             => WP_REST_Server::READABLE,
                'callback'            => array( $this, 'search_images' ),
                'permission_callback' => array( $this, 'images_permissions_check' ),
                'args'                => array(
                    'query' => array(
                        'required'          => true,
                        'validate_callback' => function( $param ) {
                            return is_string( $param ) && ! empty( $param );
                        }
                    ),
                    'source' => array(
                        'default'           => 'pexels',
                        'sanitize_callback' => 'sanitize_text_field'
                    ),
                    'per_page' => array(
                        'default'           => 8,
                        'sanitize_callback' => 'absint'
                    ),
                    'page' => array(
                        'default'           => 1,
                        'sanitize_callback' => 'absint'
                    )
                )
            )
        ) );

        register_rest_route( $namespace, '/images/import', array(
            array(
                'methods'             => WP_REST_Server::CREATABLE,
                'callback'            => array( $this, 'import_image' ),
                'permission_callback' => array( $this, 'images_permissions_check' ),
                'args'                => array(
                    'image' => array(
                        'required'          => true,
                        'validate_callback' => function( $param ) {
                            return is_array( $param );
                        }
                    )
                )
            )
        ) );

        register_rest_route( $namespace, '/images/import-bulk', array(
            array(
                'methods'             => WP_REST_Server::CREATABLE,
                'callback'            => array( $this, 'import_images' ),
                'permission_callback' => array( $this, 'images_permissions_check' ),
                'args'                => array(
                    'images' => array(
                        'required'          => true,
                        'validate_callback' => function( $param ) {
                            return is_array( $param );
                        }
                    )
                )
            )
        ) );

        register_rest_route( $namespace, '/images/sources', array(
            array(
                'methods'             => WP_REST_Server::READABLE,
                'callback'            => array( $this, 'get_image_sources' ),
                'permission_callback' => array( $this, 'images_permissions_check' )
            )
        ) );

        register_rest_route( $namespace, '/images/settings', array(
            array(
                'methods'             => WP_REST_Server::READABLE,
                'callback'            => array( $this, 'get_image_api_settings' ),
                'permission_callback' => array( $this, 'images_permissions_check' )
            )
        ) );

        register_rest_route( $namespace, '/images/check-api', array(
            array(
                'methods'             => WP_REST_Server::READABLE,
                'callback'            => array( $this, 'check_image_api_status' ),
                'permission_callback' => array( $this, 'images_permissions_check' ),
                'args'                => array(
                    'source' => array(
                        'required'          => true,
                        'validate_callback' => function( $param ) {
                            return in_array( $param, array( 'pexels', 'unsplash', 'pixabay' ) );
                        }
                    )
                )
            )
        ) );

        // Route pour la génération de contenu (multistep)
        register_rest_route( $namespace, '/ai/generate-content', array(
            array(
                'methods'             => WP_REST_Server::CREATABLE,
                'callback'            => array( $this, 'generate_content' ),
                'permission_callback' => array( $this, 'manage_content_permissions_check' ),
                'args'                => array(
                    'prompt' => array(
                        'required'          => true,
                        'sanitize_callback' => 'wp_kses_post'
                    ),
                    'options' => array(
                        'required'          => false,
                        'default'           => array(),
                        'validate_callback' => function( $param ) {
                            return is_array( $param );
                        }
                    )
                )
            )
        ) );
    }

    /**
     * Vérifie les permissions pour récupérer les contenus.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Requête REST complète.
     * @return   boolean                        True si l'utilisateur a les permissions, false sinon.
     */
    public function get_contents_permissions_check( $request ) {
        return current_user_can( 'edit_posts' );
    }

    /**
     * Vérifie les permissions pour récupérer un contenu spécifique.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Requête REST complète.
     * @return   boolean                        True si l'utilisateur a les permissions, false sinon.
     */
    public function get_content_permissions_check( $request ) {
        return current_user_can( 'edit_posts' );
    }

    /**
     * Vérifie les permissions pour analyser un contenu.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Requête REST complète.
     * @return   boolean                        True si l'utilisateur a les permissions, false sinon.
     */
    public function analyze_content_permissions_check( $request ) {
        // L'analyse nécessite seulement la capacité de lire les posts
        if ( ! current_user_can( 'read' ) ) {
            return false;
        }

        // Si un ID de post spécifique est fourni, vérifier les permissions pour ce post
        if ( $request->get_param( 'id' ) ) {
            $post_id = $request->get_param( 'id' );
            return current_user_can( 'read_post', $post_id );
        }

        // Pour les opérations en masse, vérifier si l'utilisateur peut lire les posts
        return current_user_can( 'read' );
    }

    /**
     * Vérifie les permissions pour optimiser un contenu.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Requête REST complète.
     * @return   boolean                        True si l'utilisateur a les permissions, false sinon.
     */
    public function optimize_content_permissions_check( $request ) {
        // Vérification de base
        if ( ! current_user_can( 'edit_posts' ) ) {
            return false;
        }

        // Si un ID de post spécifique est fourni, vérifier les permissions pour ce post
        if ( $request->get_param( 'id' ) ) {
            $post_id = $request->get_param( 'id' );
            return current_user_can( 'edit_post', $post_id );
        }

        // Pour les opérations en masse, vérifier si l'utilisateur peut éditer d'autres posts
        return current_user_can( 'edit_others_posts' );
    }

    /**
     * Vérifie les permissions pour récupérer les recommandations.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Requête REST complète.
     * @return   boolean                        True si l'utilisateur a les permissions, false sinon.
     */
    public function get_recommendations_permissions_check( $request ) {
        return current_user_can( 'edit_posts' );
    }

    /**
     * Vérifie les permissions pour appliquer une recommandation.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Requête REST complète.
     * @return   boolean                        True si l'utilisateur a les permissions, false sinon.
     */
    public function apply_recommendation_permissions_check( $request ) {
        return current_user_can( 'edit_posts' );
    }

    /**
     * Vérifie les permissions pour vérifier une clé API.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Requête REST complète.
     * @return   boolean                        True si l'utilisateur a les permissions, false sinon.
     */
    public function verify_api_key_permissions_check( $request ) {
        return current_user_can( 'manage_options' );
    }

    /**
     * Vérifie les permissions pour récupérer les paramètres.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Requête REST complète.
     * @return   boolean                        True si l'utilisateur a les permissions, false sinon.
     */
    public function get_settings_permissions_check( $request ) {
        return current_user_can( 'manage_options' );
    }

    /**
     * Récupère les contenus selon les filtres spécifiés.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Requête REST complète.
     * @return   WP_REST_Response               Réponse REST.
     */
    public function get_contents( $request ) {
        $args = array(
            'content_type' => $request->get_param( 'content_type' ),
            'seo_score' => $request->get_param( 'seo_score' ),
            'status' => $request->get_param( 'status' ),
            'author' => $request->get_param( 'author' ),
            'date_range' => $request->get_param( 'date_range' ),
            'search' => $request->get_param( 'search' ),
            'page' => $request->get_param( 'page' ),
            'per_page' => $request->get_param( 'per_page' ),
            'orderby' => $request->get_param( 'orderby' ),
            'order' => $request->get_param( 'order' )
        );

        $result = $this->content->get_contents( $args );

        return rest_ensure_response( $result );
    }

    /**
     * Récupère un contenu spécifique.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Requête REST complète.
     * @return   WP_REST_Response               Réponse REST.
     */
    public function get_content( $request ) {
        $post_id = $request->get_param( 'id' );
        $content = $this->content->get_content( $post_id );

        if ( ! $content ) {
            return new WP_Error( 'content_not_found', __( 'Contenu non trouvé.', 'boss-seo' ), array( 'status' => 404 ) );
        }

        return rest_ensure_response( $content );
    }

    /**
     * Analyse un contenu.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Requête REST complète.
     * @return   WP_REST_Response               Réponse REST.
     */
    public function analyze_content( $request ) {
        $post_id = $request->get_param( 'id' );
        $post = get_post( $post_id );

        if ( ! $post ) {
            return new WP_Error( 'content_not_found', __( 'Contenu non trouvé.', 'boss-seo' ), array( 'status' => 404 ) );
        }

        $analysis_results = $this->analysis->analyze( $post );

        if ( ! $analysis_results['success'] ) {
            return new WP_Error( 'analysis_failed', $analysis_results['message'], array( 'status' => 500 ) );
        }

        // Journaliser les résultats pour le débogage
        error_log('Analysis results for post ' . $post_id . ': ' . print_r($analysis_results, true));

        // Forcer la mise à jour des métadonnées
        update_post_meta( $post_id, '_boss_seo_score', $analysis_results['overall_score'] );
        update_post_meta( $post_id, '_boss_seo_recommendations', $analysis_results['recommendations'] );
        update_post_meta( $post_id, '_boss_seo_analysis_date', current_time( 'mysql' ) );

        // Récupérer les métadonnées mises à jour pour les inclure dans la réponse
        $meta_description = get_post_meta( $post_id, '_boss_seo_meta_description', true );
        $focus_keyword = get_post_meta( $post_id, '_boss_seo_focus_keyword', true );
        $seo_score = get_post_meta( $post_id, '_boss_seo_score', true );
        $recommendations = get_post_meta( $post_id, '_boss_seo_recommendations', true );
        $analysis_date = get_post_meta( $post_id, '_boss_seo_analysis_date', true );

        // Ajouter les métadonnées à la réponse
        $analysis_results['meta_description'] = $meta_description;
        $analysis_results['focus_keyword'] = $focus_keyword;
        $analysis_results['score'] = $seo_score;
        $analysis_results['recommendations'] = $recommendations;
        $analysis_results['analysis_date'] = $analysis_date;

        return rest_ensure_response( $analysis_results );
    }

    /**
     * Analyse plusieurs contenus.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Requête REST complète.
     * @return   WP_REST_Response               Réponse REST.
     */
    public function analyze_contents_bulk( $request ) {
        $post_ids = $request->get_param( 'ids' );
        $results = array();
        $success_count = 0;
        $error_count = 0;

        foreach ( $post_ids as $post_id ) {
            $post = get_post( $post_id );

            if ( ! $post ) {
                $results[ $post_id ] = array(
                    'success' => false,
                    'message' => __( 'Contenu non trouvé.', 'boss-seo' )
                );
                $error_count++;
                continue;
            }

            $analysis_results = $this->analysis->analyze( $post );
            $results[ $post_id ] = $analysis_results;

            if ( $analysis_results['success'] ) {
                $success_count++;
            } else {
                $error_count++;
            }
        }

        return rest_ensure_response( array(
            'results' => $results,
            'summary' => array(
                'total' => count( $post_ids ),
                'success' => $success_count,
                'error' => $error_count
            )
        ) );
    }

    /**
     * Optimise un contenu avec le service unifié.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Requête REST complète.
     * @return   WP_REST_Response               Réponse REST.
     */
    public function optimize_content( $request ) {
        $post_id = $request->get_param( 'id' );
        $settings = $request->get_param( 'settings' );
        $post = get_post( $post_id );

        if ( ! $post ) {
            return new WP_Error( 'content_not_found', __( 'Contenu non trouvé.', 'boss-seo' ), array( 'status' => 404 ) );
        }

        // Charger le service d'optimisation unifié
        if ( ! class_exists( 'Boss_Optimizer_Unified' ) ) {
            require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-boss-optimizer-unified.php';
        }

        try {
            $optimizer = new Boss_Optimizer_Unified();

            // Préparer les options d'optimisation à partir des paramètres de la requête
            $options = array(
                'use_ai' => isset( $settings['useAI'] ) ? (bool) $settings['useAI'] : true,
                'auto_save' => true,
                'generate_title' => isset( $settings['title_seo'] ) ? (bool) $settings['title_seo'] : true,
                'generate_description' => isset( $settings['meta_description'] ) ? (bool) $settings['meta_description'] : true,
                'generate_keywords' => isset( $settings['keywords'] ) ? (bool) $settings['keywords'] : true,
                'focus_keyword' => '',
            );

            // Optimiser avec le service unifié
            $result = $optimizer->optimize_content( $post_id, $options );

            if ( $result['success'] ) {
                // Analyser le contenu après optimisation pour obtenir le nouveau score
                $analysis_results = $this->analysis->analyze( $post );

                // Récupérer les métadonnées mises à jour
                $seo_title = get_post_meta( $post_id, '_boss_seo_title', true );
                $meta_description = get_post_meta( $post_id, '_boss_seo_meta_description', true );
                $focus_keyword = get_post_meta( $post_id, '_boss_seo_focus_keyword', true );
                $secondary_keywords = get_post_meta( $post_id, '_boss_seo_secondary_keywords', true );

                return rest_ensure_response( array(
                    'success' => true,
                    'message' => $result['message'],
                    'seo_title' => $seo_title,
                    'meta_description' => $meta_description,
                    'focus_keyword' => $focus_keyword,
                    'secondary_keywords' => $secondary_keywords,
                    'score' => $analysis_results['overall_score'] ?? 0,
                    'analysis_results' => $analysis_results,
                    'auto_saved' => $result['auto_saved'],
                    'optimizations' => $result['optimizations']
                ) );
            } else {
                return new WP_Error( 'optimization_failed', $result['message'], array( 'status' => 500 ) );
            }

        } catch ( Exception $e ) {
            error_log( 'Boss SEO API Optimization Error: ' . $e->getMessage() );
            return new WP_Error( 'optimization_error', __( 'Erreur lors de l\'optimisation: ', 'boss-seo' ) . $e->getMessage(), array( 'status' => 500 ) );
        }
    }

    /**
     * Optimise plusieurs contenus avec le service unifié.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Requête REST complète.
     * @return   WP_REST_Response               Réponse REST.
     */
    public function optimize_contents_bulk( $request ) {
        $post_ids = $request->get_param( 'ids' );
        $settings = $request->get_param( 'settings' );

        // Charger le service d'optimisation unifié
        if ( ! class_exists( 'Boss_Optimizer_Unified' ) ) {
            require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-boss-optimizer-unified.php';
        }

        $results = array();
        $success_count = 0;
        $error_count = 0;

        // Préparer les options d'optimisation
        $options = array(
            'use_ai' => isset( $settings['useAI'] ) ? (bool) $settings['useAI'] : true,
            'auto_save' => true,
            'generate_title' => isset( $settings['title_seo'] ) ? (bool) $settings['title_seo'] : true,
            'generate_description' => isset( $settings['meta_description'] ) ? (bool) $settings['meta_description'] : true,
            'generate_keywords' => isset( $settings['keywords'] ) ? (bool) $settings['keywords'] : true,
            'focus_keyword' => '',
        );

        try {
            $optimizer = new Boss_Optimizer_Unified();

            foreach ( $post_ids as $post_id ) {
                $post = get_post( $post_id );

                if ( ! $post ) {
                    $results[ $post_id ] = array(
                        'success' => false,
                        'message' => __( 'Contenu non trouvé.', 'boss-seo' )
                    );
                    $error_count++;
                    continue;
                }

                // Optimiser avec le service unifié
                $result = $optimizer->optimize_content( $post_id, $options );
                $results[ $post_id ] = $result;

                if ( $result['success'] ) {
                    $success_count++;
                } else {
                    $error_count++;
                }
            }

        } catch ( Exception $e ) {
            error_log( 'Boss SEO Bulk Optimization Error: ' . $e->getMessage() );
            return new WP_Error( 'bulk_optimization_error', __( 'Erreur lors de l\'optimisation en masse: ', 'boss-seo' ) . $e->getMessage(), array( 'status' => 500 ) );
        }

        return rest_ensure_response( array(
            'results' => $results,
            'summary' => array(
                'total' => count( $post_ids ),
                'success' => $success_count,
                'error' => $error_count
            )
        ) );
    }

    /**
     * Optimise tous les contenus.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Requête REST complète.
     * @return   WP_REST_Response               Réponse REST.
     */
    public function optimize_all_contents( $request ) {
        $settings = $request->get_param( 'settings' );
        $filters = $request->get_param( 'filters' );

        // Récupérer tous les contenus selon les filtres
        $contents_data = $this->content->get_contents( $filters );

        if ( empty( $contents_data['contents'] ) ) {
            return new WP_REST_Response( array(
                'success' => false,
                'message' => __( 'Aucun contenu trouvé avec les filtres spécifiés.', 'boss-seo' )
            ), 404 );
        }

        $post_ids = array_column( $contents_data['contents'], 'id' );

        // Limiter le nombre de contenus pour éviter les timeouts
        if ( count( $post_ids ) > 100 ) {
            return new WP_REST_Response( array(
                'success' => false,
                'message' => sprintf(
                    __( 'Trop de contenus à optimiser (%d). Veuillez utiliser des filtres pour réduire le nombre à moins de 100.', 'boss-seo' ),
                    count( $post_ids )
                )
            ), 400 );
        }

        // Utiliser la méthode bulk existante
        $bulk_request = new WP_REST_Request();
        $bulk_request->set_param( 'ids', $post_ids );
        $bulk_request->set_param( 'settings', $settings );

        return $this->optimize_contents_bulk( $bulk_request );
    }

    /**
     * Vérifie les permissions pour gérer le contenu.
     *
     * @since    1.1.0
     * @return   bool
     */
    public function manage_content_permissions_check() {
        return current_user_can( 'edit_posts' );
    }

    /**
     * Optimise un contenu avec génération de métadonnées SEO.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Requête REST complète.
     * @return   WP_REST_Response               Réponse REST.
     */
    public function optimize_content_with_metadata( $request ) {
        $post_id = $request->get_param( 'id' );
        $settings = $request->get_param( 'settings' );
        $post = get_post( $post_id );

        if ( ! $post ) {
            return new WP_Error( 'content_not_found', __( 'Contenu non trouvé.', 'boss-seo' ), array( 'status' => 404 ) );
        }

        // Analyser d'abord le contenu
        $analysis_results = $this->analysis->analyze( $post );

        if ( ! $analysis_results['success'] ) {
            return new WP_Error( 'analysis_failed', $analysis_results['message'], array( 'status' => 500 ) );
        }

        // Vérifier les paramètres d'optimisation
        $generate_title_seo = isset( $settings['title_seo'] ) ? (bool) $settings['title_seo'] : true;
        $generate_meta_description = isset( $settings['meta_description'] ) ? (bool) $settings['meta_description'] : true;
        $generate_keywords = isset( $settings['keywords'] ) ? (bool) $settings['keywords'] : true;
        $modify_content = isset( $settings['modify_content'] ) ? (bool) $settings['modify_content'] : false;
        $modify_title = isset( $settings['modify_title'] ) ? (bool) $settings['modify_title'] : false;
        $modify_headings = isset( $settings['modify_headings'] ) ? (bool) $settings['modify_headings'] : false;
        $modify_images = isset( $settings['modify_images'] ) ? (bool) $settings['modify_images'] : false;
        $modify_links = isset( $settings['modify_links'] ) ? (bool) $settings['modify_links'] : false;
        $use_ai = isset( $settings['useAI'] ) ? (bool) $settings['useAI'] : true;

        // Générer les métadonnées SEO
        $updated = false;
        $applied_recommendations = array();
        $meta_updated = false;

        // Générer le titre SEO si demandé
        if ( $generate_title_seo ) {
            $title = $post->post_title;
            $content = $post->post_content;
            $seo_title = '';

            if ( $use_ai && $this->ai ) {
                // Utiliser l'IA pour générer un titre SEO
                $prompt = sprintf(
                    __( 'Génère un titre SEO optimisé pour le contenu suivant. Titre actuel: "%s". Contenu: "%s". Le titre SEO doit être concis (max 60 caractères), inclure des mots-clés pertinents, et être attrayant pour les utilisateurs.', 'boss-seo' ),
                    $title,
                    wp_trim_words( strip_tags( $content ), 100 )
                );

                $ai_response = $this->ai->generate_content( $prompt );
                if ( $ai_response['success'] ) {
                    $seo_title = $ai_response['content'];
                    // Nettoyer le titre (enlever les guillemets, etc.)
                    $seo_title = trim( str_replace( array( '"', "'" ), '', $seo_title ) );
                    // Limiter à 60 caractères
                    if ( strlen( $seo_title ) > 60 ) {
                        $seo_title = substr( $seo_title, 0, 57 ) . '...';
                    }
                }
            }

            if ( empty( $seo_title ) ) {
                // Fallback si l'IA échoue ou n'est pas utilisée
                $seo_title = $title;
                if ( strlen( $seo_title ) > 60 ) {
                    $seo_title = substr( $seo_title, 0, 57 ) . '...';
                }
            }

            // Enregistrer le titre SEO
            update_post_meta( $post_id, '_boss_seo_title', $seo_title );
            $meta_updated = true;
        }

        // Générer la meta description si demandée
        if ( $generate_meta_description ) {
            $content = $post->post_content;
            $meta_description = '';

            if ( $use_ai && $this->ai ) {
                // Utiliser l'IA pour générer une meta description
                $prompt = sprintf(
                    __( 'Génère une meta description SEO optimisée pour le contenu suivant: "%s". La description doit être concise (max 160 caractères), inclure des mots-clés pertinents, et donner un aperçu attrayant du contenu.', 'boss-seo' ),
                    wp_trim_words( strip_tags( $content ), 150 )
                );

                $ai_response = $this->ai->generate_content( $prompt );
                if ( $ai_response['success'] ) {
                    $meta_description = $ai_response['content'];
                    // Nettoyer la description (enlever les guillemets, etc.)
                    $meta_description = trim( str_replace( array( '"', "'" ), '', $meta_description ) );
                    // Limiter à 160 caractères
                    if ( strlen( $meta_description ) > 160 ) {
                        $meta_description = substr( $meta_description, 0, 157 ) . '...';
                    }
                }
            }

            if ( empty( $meta_description ) ) {
                // Fallback si l'IA échoue ou n'est pas utilisée
                $meta_description = wp_trim_words( strip_tags( $content ), 25 );
                if ( strlen( $meta_description ) > 160 ) {
                    $meta_description = substr( $meta_description, 0, 157 ) . '...';
                }
            }

            // Enregistrer la meta description
            update_post_meta( $post_id, '_boss_seo_meta_description', $meta_description );
            $meta_updated = true;
        }

        // Générer les mots-clés si demandés
        if ( $generate_keywords ) {
            $title = $post->post_title;
            $content = $post->post_content;
            $keywords = '';

            if ( $use_ai && $this->ai ) {
                // Utiliser l'IA pour générer des mots-clés
                $prompt = sprintf(
                    __( 'Extrais 3 à 5 mots-clés ou expressions pertinents pour le référencement du contenu suivant. Titre: "%s". Contenu: "%s". Retourne uniquement les mots-clés séparés par des virgules, sans autre texte.', 'boss-seo' ),
                    $title,
                    wp_trim_words( strip_tags( $content ), 100 )
                );

                $ai_response = $this->ai->generate_content( $prompt );
                if ( $ai_response['success'] ) {
                    $keywords = $ai_response['content'];
                    // Nettoyer les mots-clés (enlever les guillemets, etc.)
                    $keywords = trim( str_replace( array( '"', "'" ), '', $keywords ) );
                }
            }

            if ( empty( $keywords ) ) {
                // Fallback si l'IA échoue ou n'est pas utilisée
                $words = explode( ' ', $title . ' ' . wp_trim_words( strip_tags( $content ), 50 ) );
                $word_count = array_count_values( $words );
                arsort( $word_count );
                $top_words = array_slice( array_keys( $word_count ), 0, 5 );
                $keywords = implode( ', ', $top_words );
            }

            // Séparer et enregistrer les mots-clés
            if ( ! empty( $keywords ) ) {
                $keywords_array = array_map( 'trim', explode( ',', $keywords ) );
                $keywords_array = array_filter( $keywords_array ); // Supprimer les éléments vides

                if ( ! empty( $keywords_array ) ) {
                    // Premier mot-clé = principal
                    $focus_keyword = $keywords_array[0];
                    update_post_meta( $post_id, '_boss_seo_focus_keyword', $focus_keyword );

                    // Autres mots-clés = secondaires
                    if ( count( $keywords_array ) > 1 ) {
                        $secondary_keywords = array_slice( $keywords_array, 1 );
                        update_post_meta( $post_id, '_boss_seo_secondary_keywords', implode( ', ', $secondary_keywords ) );
                    } else {
                        // Pas de mots-clés secondaires
                        update_post_meta( $post_id, '_boss_seo_secondary_keywords', '' );
                    }

                    $meta_updated = true;
                }
            }
        }

        // Si l'utilisateur a explicitement demandé de modifier le contenu, appliquer les recommandations
        if ( $modify_content || $modify_title || $modify_headings || $modify_images || $modify_links ) {
            // Récupérer les recommandations
            $recommendations = $this->recommendations->get_recommendations( $post_id );

            if ( ! empty( $recommendations ) ) {
                foreach ( $recommendations as $recommendation ) {
                    // Vérifier si le type de modification est autorisé
                    $element = isset( $recommendation['element'] ) ? $recommendation['element'] : '';

                    if (
                        ( $element === 'content' && ! $modify_content ) ||
                        ( $element === 'title' && ! $modify_title ) ||
                        ( $element === 'headings' && ! $modify_headings ) ||
                        ( $element === 'images' && ! $modify_images ) ||
                        ( $element === 'links' && ! $modify_links )
                    ) {
                        continue;
                    }

                    // Appliquer uniquement les recommandations critiques et les avertissements
                    if ( $recommendation['type'] === 'critical' || $recommendation['type'] === 'warning' ) {
                        $result = $this->recommendations->apply_recommendation( $post_id, $recommendation['id'], $this->ai );

                        if ( $result['success'] ) {
                            $applied_recommendations[] = $recommendation;
                            $updated = true;
                        }
                    }
                }
            }
        }

        // Si des métadonnées ont été mises à jour ou des recommandations appliquées, analyser à nouveau le contenu
        if ( $meta_updated || $updated ) {
            $post = get_post( $post_id ); // Récupérer le contenu mis à jour
            $new_analysis_results = $this->analysis->analyze( $post );

            // Forcer la mise à jour des métadonnées d'analyse
            update_post_meta( $post_id, '_boss_seo_score', $new_analysis_results['overall_score'] );
            update_post_meta( $post_id, '_boss_seo_recommendations', $new_analysis_results['recommendations'] );
            update_post_meta( $post_id, '_boss_seo_analysis_date', current_time( 'mysql' ) );

            // Récupérer les métadonnées mises à jour pour les inclure dans la réponse
            $seo_title = get_post_meta( $post_id, '_boss_seo_title', true );
            $meta_description = get_post_meta( $post_id, '_boss_seo_meta_description', true );
            $focus_keyword = get_post_meta( $post_id, '_boss_seo_focus_keyword', true );
            $seo_score = get_post_meta( $post_id, '_boss_seo_score', true );
            $recommendations = get_post_meta( $post_id, '_boss_seo_recommendations', true );
            $analysis_date = get_post_meta( $post_id, '_boss_seo_analysis_date', true );
            $last_optimized = get_post_meta( $post_id, '_boss_seo_last_optimized', true );

            // Récupérer les métadonnées sociales
            $og_title = get_post_meta( $post_id, '_boss_seo_og_title', true );
            $og_description = get_post_meta( $post_id, '_boss_seo_og_description', true );
            $og_image = get_post_meta( $post_id, '_boss_seo_og_image', true );
            $twitter_title = get_post_meta( $post_id, '_boss_seo_twitter_title', true );
            $twitter_description = get_post_meta( $post_id, '_boss_seo_twitter_description', true );
            $twitter_image = get_post_meta( $post_id, '_boss_seo_twitter_image', true );

            // Récupérer les métadonnées avancées
            $canonical_url = get_post_meta( $post_id, '_boss_seo_canonical_url', true );
            $robots = get_post_meta( $post_id, '_boss_seo_robots', true );
            $schema_type = get_post_meta( $post_id, '_boss_seo_schema_type', true );
            $schema_data = get_post_meta( $post_id, '_boss_seo_schema_data', true );

            return rest_ensure_response( array(
                'success' => true,
                'message' => $updated
                    ? __( 'Contenu et métadonnées SEO optimisés avec succès.', 'boss-seo' )
                    : __( 'Métadonnées SEO générées avec succès.', 'boss-seo' ),
                'applied_recommendations' => $applied_recommendations,
                'analysis_results' => $new_analysis_results,
                'seo_title' => $seo_title,
                'meta_description' => $meta_description,
                'focus_keyword' => $focus_keyword,
                'score' => $seo_score,
                'recommendations' => $recommendations,
                'analysis_date' => $analysis_date,
                'last_optimized' => $last_optimized,
                'content_modified' => $updated,
                'social_meta' => array(
                    'og_title' => $og_title,
                    'og_description' => $og_description,
                    'og_image' => $og_image,
                    'twitter_title' => $twitter_title,
                    'twitter_description' => $twitter_description,
                    'twitter_image' => $twitter_image
                ),
                'advanced_meta' => array(
                    'canonical_url' => $canonical_url,
                    'robots' => $robots,
                    'schema_type' => $schema_type,
                    'schema_data' => $schema_data
                )
            ) );
        } else {
            return rest_ensure_response( array(
                'success' => false,
                'message' => __( 'Aucune optimisation n\'a été appliquée.', 'boss-seo' ),
                'analysis_results' => $analysis_results
            ) );
        }
    }

    /**
     * Génère du contenu avec l'IA pour le multistep.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Requête REST complète.
     * @return   WP_REST_Response               Réponse REST.
     */
    public function generate_content( $request ) {
        $prompt = $request->get_param( 'prompt' );
        $options = $request->get_param( 'options' );

        try {
            // Charger le service d'optimisation unifié
            if ( ! class_exists( 'Boss_Optimizer_Unified' ) ) {
                require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-boss-optimizer-unified.php';
            }

            // Charger le service IA
            if ( ! class_exists( 'Boss_Optimizer_AI' ) ) {
                require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-boss-optimizer-ai.php';
            }

            // Charger les paramètres
            if ( ! class_exists( 'Boss_Optimizer_Settings' ) ) {
                require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-boss-optimizer-settings.php';
            }

            $settings = new Boss_Optimizer_Settings( 'boss-seo' );
            $ai = new Boss_Optimizer_AI( 'boss-seo', $settings );

            if ( ! $ai->is_available() ) {
                return new WP_Error( 'ai_not_available', __( 'Service IA non disponible. Veuillez configurer vos clés API dans les paramètres.', 'boss-seo' ), array( 'status' => 503 ) );
            }

            // Préparer les options par défaut
            $default_options = array(
                'temperature' => 0.7,
                'max_tokens' => 2000,
                'provider' => 'openai'
            );

            $merged_options = wp_parse_args( $options, $default_options );

            // Appeler l'IA pour générer le contenu
            $ai_response = $ai->generate_content( $prompt, $merged_options );

            if ( ! $ai_response['success'] ) {
                return new WP_Error( 'ai_generation_failed', $ai_response['message'], array( 'status' => 500 ) );
            }

            // Parser la réponse pour extraire les différents éléments
            $generated_content = $ai_response['content'];
            $parsed_content = $this->parse_generated_content( $generated_content );

            return rest_ensure_response( array(
                'success' => true,
                'title' => $parsed_content['title'] ?? '',
                'metaDescription' => $parsed_content['meta_description'] ?? '',
                'content' => $parsed_content['content'] ?? $generated_content,
                'keywords' => $parsed_content['keywords'] ?? array(),
                'raw_response' => $generated_content
            ) );

        } catch ( Exception $e ) {
            error_log( 'Boss SEO Generate Content Error: ' . $e->getMessage() );
            return new WP_Error( 'generation_error', __( 'Erreur lors de la génération du contenu: ', 'boss-seo' ) . $e->getMessage(), array( 'status' => 500 ) );
        }
    }

    /**
     * Parse le contenu généré pour extraire les différents éléments
     *
     * @param string $content Contenu généré par l'IA
     * @return array Éléments parsés
     */
    private function parse_generated_content( $content ) {
        $parsed = array();

        // Essayer de parser comme JSON d'abord
        $json_data = json_decode( $content, true );
        if ( json_last_error() === JSON_ERROR_NONE && is_array( $json_data ) ) {
            return $json_data;
        }

        // Parser manuel si ce n'est pas du JSON
        $lines = explode( "\n", $content );
        $current_section = 'content';
        $content_lines = array();

        foreach ( $lines as $line ) {
            $line = trim( $line );

            if ( empty( $line ) ) {
                continue;
            }

            // Détecter les sections
            if ( preg_match( '/^(titre|title):\s*(.+)/i', $line, $matches ) ) {
                $parsed['title'] = trim( $matches[2] );
                continue;
            }

            if ( preg_match( '/^(meta.?description|description):\s*(.+)/i', $line, $matches ) ) {
                $parsed['meta_description'] = trim( $matches[2] );
                continue;
            }

            if ( preg_match( '/^(mots.?clés|keywords):\s*(.+)/i', $line, $matches ) ) {
                $keywords_str = trim( $matches[2] );
                $parsed['keywords'] = array_map( 'trim', explode( ',', $keywords_str ) );
                continue;
            }

            // Tout le reste va dans le contenu
            $content_lines[] = $line;
        }

        if ( ! empty( $content_lines ) ) {
            $parsed['content'] = implode( "\n", $content_lines );
        }

        return $parsed;
    }

    // Méthodes dupliquées supprimées - versions correctes plus bas dans la classe

    /**
     * Analyse tous les contenus selon les filtres.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Requête REST complète.
     * @return   WP_REST_Response               Réponse REST.
     */
    public function analyze_all_contents( $request ) {
        $filters = $request->get_param( 'filters' );

        // Récupérer tous les contenus selon les filtres
        $contents_data = $this->content->get_contents( $filters );

        if ( empty( $contents_data['contents'] ) ) {
            return new WP_REST_Response( array(
                'success' => false,
                'message' => __( 'Aucun contenu trouvé avec les filtres spécifiés.', 'boss-seo' )
            ), 404 );
        }

        $post_ids = array_column( $contents_data['contents'], 'id' );

        // Limiter le nombre de contenus pour éviter les timeouts
        if ( count( $post_ids ) > 200 ) {
            return new WP_REST_Response( array(
                'success' => false,
                'message' => sprintf(
                    __( 'Trop de contenus à analyser (%d). Veuillez utiliser des filtres pour réduire le nombre à moins de 200.', 'boss-seo' ),
                    count( $post_ids )
                )
            ), 400 );
        }

        // Utiliser la méthode bulk existante
        $bulk_request = new WP_REST_Request();
        $bulk_request->set_param( 'ids', $post_ids );

        return $this->analyze_contents_bulk( $bulk_request );
    }

    /**
     * Récupère les recommandations pour un contenu.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Requête REST complète.
     * @return   WP_REST_Response               Réponse REST.
     */
    public function get_recommendations( $request ) {
        $post_id = $request->get_param( 'id' );
        $recommendations = $this->recommendations->get_recommendations( $post_id );

        if ( empty( $recommendations ) ) {
            return new WP_Error( 'no_recommendations', __( 'Aucune recommandation trouvée pour ce contenu.', 'boss-seo' ), array( 'status' => 404 ) );
        }

        return rest_ensure_response( array(
            'post_id' => $post_id,
            'recommendations' => $recommendations
        ) );
    }

    /**
     * Applique une recommandation spécifique.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Requête REST complète.
     * @return   WP_REST_Response               Réponse REST.
     */
    public function apply_recommendation( $request ) {
        $post_id = $request->get_param( 'post_id' );
        $recommendation_id = $request->get_param( 'recommendation_id' );

        $result = $this->recommendations->apply_recommendation( $post_id, $recommendation_id, $this->ai );

        if ( ! $result['success'] ) {
            return new WP_Error( 'application_failed', $result['message'], array( 'status' => 500 ) );
        }

        return rest_ensure_response( $result );
    }

    /**
     * Vérifie la validité d'une clé API.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Requête REST complète.
     * @return   WP_REST_Response               Réponse REST.
     */
    public function verify_api_key( $request ) {
        $provider = $request->get_param( 'provider' );
        $api_key = $request->get_param( 'api_key' );

        // Vérifier la clé API en fonction du fournisseur
        switch ( $provider ) {
            case 'openai':
                $result = $this->verify_openai_api_key( $api_key );
                break;
            case 'anthropic':
                $result = $this->verify_anthropic_api_key( $api_key );
                break;
            case 'gemini':
                $result = $this->verify_gemini_api_key( $api_key );
                break;
            case 'semrush':
                $result = $this->verify_semrush_api_key( $api_key );
                break;
            case 'moz':
                $result = $this->verify_moz_api_key( $api_key );
                break;
            case 'ahrefs':
                $result = $this->verify_ahrefs_api_key( $api_key );
                break;
            case 'majestic':
                $result = $this->verify_majestic_api_key( $api_key );
                break;
            case 'serpapi':
                $result = $this->verify_serpapi_api_key( $api_key );
                break;
            default:
                return new WP_Error( 'invalid_provider', __( 'Fournisseur non pris en charge.', 'boss-seo' ), array( 'status' => 400 ) );
        }

        return rest_ensure_response( $result );
    }

    /**
     * Vérifie la validité d'une clé API OpenAI.
     *
     * @since    1.1.0
     * @param    string    $api_key    Clé API à vérifier.
     * @return   array                 Résultat de la vérification.
     */
    private function verify_openai_api_key( $api_key ) {
        // Appeler l'API OpenAI pour vérifier la clé
        $response = wp_remote_get(
            'https://api.openai.com/v1/models',
            array(
                'headers' => array(
                    'Authorization' => 'Bearer ' . $api_key,
                    'Content-Type' => 'application/json'
                ),
                'timeout' => 15
            )
        );

        // Vérifier les erreurs
        if ( is_wp_error( $response ) ) {
            return array(
                'success' => false,
                'message' => $response->get_error_message()
            );
        }

        $status_code = wp_remote_retrieve_response_code( $response );
        $body = json_decode( wp_remote_retrieve_body( $response ), true );

        if ( $status_code === 200 && isset( $body['data'] ) ) {
            return array(
                'success' => true,
                'message' => __( 'Clé API OpenAI valide.', 'boss-seo' )
            );
        } else {
            $error_message = isset( $body['error']['message'] ) ? $body['error']['message'] : __( 'Clé API OpenAI invalide.', 'boss-seo' );
            return array(
                'success' => false,
                'message' => $error_message
            );
        }
    }

    /**
     * Vérifie la validité d'une clé API Anthropic.
     *
     * @since    1.1.0
     * @param    string    $api_key    Clé API à vérifier.
     * @return   array                 Résultat de la vérification.
     */
    private function verify_anthropic_api_key( $api_key ) {
        // Appeler l'API Anthropic pour vérifier la clé
        $response = wp_remote_post(
            'https://api.anthropic.com/v1/messages',
            array(
                'headers' => array(
                    'x-api-key' => $api_key,
                    'anthropic-version' => '2023-06-01',
                    'Content-Type' => 'application/json'
                ),
                'body' => json_encode( array(
                    'model' => 'claude-3-haiku-20240307',
                    'max_tokens' => 10,
                    'messages' => array(
                        array(
                            'role' => 'user',
                            'content' => 'Hello'
                        )
                    )
                ) ),
                'timeout' => 15
            )
        );

        // Vérifier les erreurs
        if ( is_wp_error( $response ) ) {
            return array(
                'success' => false,
                'message' => $response->get_error_message()
            );
        }

        $status_code = wp_remote_retrieve_response_code( $response );
        $body = json_decode( wp_remote_retrieve_body( $response ), true );

        if ( $status_code === 200 && isset( $body['content'] ) ) {
            return array(
                'success' => true,
                'message' => __( 'Clé API Anthropic valide.', 'boss-seo' )
            );
        } else {
            $error_message = isset( $body['error']['message'] ) ? $body['error']['message'] : __( 'Clé API Anthropic invalide.', 'boss-seo' );
            return array(
                'success' => false,
                'message' => $error_message
            );
        }
    }

    /**
     * Vérifie la validité d'une clé API SEMrush.
     *
     * @since    1.1.0
     * @param    string    $api_key    Clé API à vérifier.
     * @return   array                 Résultat de la vérification.
     */
    private function verify_semrush_api_key( $api_key ) {
        // Pour l'instant, nous simulons une vérification réussie
        return array(
            'success' => true,
            'message' => __( 'Clé API SEMrush valide.', 'boss-seo' )
        );
    }

    /**
     * Vérifie la validité d'une clé API Moz.
     *
     * @since    1.1.0
     * @param    string    $api_key    Clé API à vérifier.
     * @return   array                 Résultat de la vérification.
     */
    private function verify_moz_api_key( $api_key ) {
        // Pour l'instant, nous simulons une vérification réussie
        return array(
            'success' => true,
            'message' => __( 'Clé API Moz valide.', 'boss-seo' )
        );
    }

    /**
     * Vérifie la validité d'une clé API Ahrefs.
     *
     * @since    1.1.0
     * @param    string    $api_key    Clé API à vérifier.
     * @return   array                 Résultat de la vérification.
     */
    private function verify_ahrefs_api_key( $api_key ) {
        // Pour l'instant, nous simulons une vérification réussie
        return array(
            'success' => true,
            'message' => __( 'Clé API Ahrefs valide.', 'boss-seo' )
        );
    }

    /**
     * Vérifie la validité d'une clé API Majestic.
     *
     * @since    1.1.0
     * @param    string    $api_key    Clé API à vérifier.
     * @return   array                 Résultat de la vérification.
     */
    private function verify_majestic_api_key( $api_key ) {
        // Pour l'instant, nous simulons une vérification réussie
        return array(
            'success' => true,
            'message' => __( 'Clé API Majestic valide.', 'boss-seo' )
        );
    }

    /**
     * Vérifie la validité d'une clé API SerpAPI.
     *
     * @since    1.1.0
     * @param    string    $api_key    Clé API à vérifier.
     * @return   array                 Résultat de la vérification.
     */
    private function verify_serpapi_api_key( $api_key ) {
        // Pour l'instant, nous simulons une vérification réussie
        return array(
            'success' => true,
            'message' => __( 'Clé API SerpAPI valide.', 'boss-seo' )
        );
    }

    /**
     * Récupère les paramètres d'IA.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Requête REST complète.
     * @return   WP_REST_Response               Réponse REST.
     */
    public function get_ai_settings( $request ) {
        $ai_settings = $this->settings->get_group( 'ai' );

        return rest_ensure_response( $ai_settings );
    }

    /**
     * Enregistre les paramètres d'IA.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Requête REST complète.
     * @return   WP_REST_Response               Réponse REST.
     */
    public function save_ai_settings( $request ) {
        $settings = $request->get_param( 'settings' );

        // Convertir les paramètres au format attendu par la classe Settings
        $formatted_settings = array(
            'ai' => array(
                'provider' => isset( $settings['provider'] ) ? sanitize_text_field( $settings['provider'] ) : 'openai',
                'openai_api_key' => isset( $settings['openai_api_key'] ) ? sanitize_text_field( $settings['openai_api_key'] ) : '',
                'openai_model' => isset( $settings['openai_model'] ) ? sanitize_text_field( $settings['openai_model'] ) : 'gpt-4',
                'openai_temperature' => isset( $settings['openai_temperature'] ) ? floatval( $settings['openai_temperature'] ) : 0.7,
                'anthropic_api_key' => isset( $settings['anthropic_api_key'] ) ? sanitize_text_field( $settings['anthropic_api_key'] ) : '',
                'anthropic_model' => isset( $settings['anthropic_model'] ) ? sanitize_text_field( $settings['anthropic_model'] ) : 'claude-3-opus',
                'anthropic_temperature' => isset( $settings['anthropic_temperature'] ) ? floatval( $settings['anthropic_temperature'] ) : 0.7,
                'gemini_api_key' => isset( $settings['gemini_api_key'] ) ? sanitize_text_field( $settings['gemini_api_key'] ) : '',
                'gemini_model' => isset( $settings['gemini_model'] ) ? sanitize_text_field( $settings['gemini_model'] ) : 'gemini-1.5-pro',
                'gemini_temperature' => isset( $settings['gemini_temperature'] ) ? floatval( $settings['gemini_temperature'] ) : 0.7,
                'use_ai_for_titles' => isset( $settings['use_ai_for_titles'] ) ? (bool) $settings['use_ai_for_titles'] : true,
                'use_ai_for_descriptions' => isset( $settings['use_ai_for_descriptions'] ) ? (bool) $settings['use_ai_for_descriptions'] : true,
                'use_ai_for_content' => isset( $settings['use_ai_for_content'] ) ? (bool) $settings['use_ai_for_content'] : true,
                'use_ai_for_alt_text' => isset( $settings['use_ai_for_alt_text'] ) ? (bool) $settings['use_ai_for_alt_text'] : true
            )
        );

        // Enregistrer les paramètres
        $result = $this->settings->save( $formatted_settings );

        if ( $result ) {
            return new WP_REST_Response( array(
                'success' => true,
                'message' => __( 'Paramètres d\'IA enregistrés avec succès.', 'boss-seo' )
            ), 200 );
        } else {
            return new WP_REST_Response( array(
                'success' => false,
                'message' => __( 'Erreur lors de l\'enregistrement des paramètres d\'IA.', 'boss-seo' )
            ), 500 );
        }
    }

    /**
     * Teste une clé API d'IA.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Requête REST complète.
     * @return   WP_REST_Response               Réponse REST.
     */
    public function test_ai_api_key( $request ) {
        $provider = $request->get_param( 'provider' );
        $api_key = $request->get_param( 'api_key' );

        // Vérifier la clé API en fonction du fournisseur
        switch ( $provider ) {
            case 'openai':
                $result = $this->verify_openai_api_key( $api_key );
                break;
            case 'anthropic':
                $result = $this->verify_anthropic_api_key( $api_key );
                break;
            case 'gemini':
                $result = $this->verify_gemini_api_key( $api_key );
                break;
            default:
                return new WP_Error( 'invalid_provider', __( 'Fournisseur d\'IA non pris en charge.', 'boss-seo' ), array( 'status' => 400 ) );
        }

        return rest_ensure_response( $result );
    }

    /**
     * Récupère les paramètres des services externes.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Requête REST complète.
     * @return   WP_REST_Response               Réponse REST.
     */
    public function get_external_services_settings( $request ) {
        // Récupérer les paramètres des services externes
        $settings = $this->settings->get( 'external_services' );

        // Si aucun paramètre n'est trouvé, essayer de récupérer depuis le groupe 'services'
        if ( ! $settings ) {
            $settings = $this->settings->get_group( 'services' );
        }

        if ( ! $settings ) {
            // Paramètres par défaut si aucun n'est trouvé
            $settings = array(
                'google_pagespeed' => array(
                    'enabled' => false,
                    'api_key' => ''
                ),
                'google_search_console' => array(
                    'enabled' => false,
                    'connected' => false
                ),
                'image_optimization' => array(
                    'service' => 'tinypng',
                    'enabled' => false,
                    'api_key' => ''
                ),
                'semrush' => array(
                    'enabled' => false,
                    'api_key' => ''
                ),
                'moz' => array(
                    'enabled' => false,
                    'access_id' => '',
                    'secret_key' => ''
                ),
                'ahrefs' => array(
                    'enabled' => false,
                    'api_key' => ''
                ),
                'majestic' => array(
                    'enabled' => false,
                    'api_key' => ''
                )
            );
        }

        return rest_ensure_response( $settings );
    }

    /**
     * Enregistre les paramètres des services externes.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Requête REST complète.
     * @return   WP_REST_Response               Réponse REST.
     */
    public function save_external_services_settings( $request ) {
        $settings = $request->get_param( 'settings' );

        // Convertir les paramètres au format attendu par la classe Settings
        $formatted_settings = array(
            'external_services' => $settings
        );

        // Enregistrer les paramètres
        $result = $this->settings->save( $formatted_settings );

        if ( $result ) {
            return new WP_REST_Response( array(
                'success' => true,
                'message' => __( 'Paramètres des services externes enregistrés avec succès.', 'boss-seo' )
            ), 200 );
        } else {
            return new WP_REST_Response( array(
                'success' => false,
                'message' => __( 'Erreur lors de l\'enregistrement des paramètres des services externes.', 'boss-seo' )
            ), 500 );
        }
    }

    /**
     * Vérifie une clé API pour un service externe.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Requête REST complète.
     * @return   WP_REST_Response               Réponse REST.
     */
    public function verify_external_service_api_key( $request ) {
        $service = $request->get_param( 'service' );
        $api_key = $request->get_param( 'api_key' );

        // Vérifier la clé API en fonction du service
        switch ( $service ) {
            case 'google_pagespeed':
                $result = $this->verify_pagespeed_api_key( $api_key );
                break;
            case 'tinypng':
            case 'imagify':
            case 'optimole':
                $result = array(
                    'success' => true,
                    'message' => sprintf( __( 'Clé API %s valide.', 'boss-seo' ), ucfirst( $service ) )
                );
                break;
            case 'semrush':
                $result = $this->verify_semrush_api_key( $api_key );
                break;
            case 'moz':
                $result = $this->verify_moz_api_key( $api_key );
                break;
            case 'ahrefs':
                $result = $this->verify_ahrefs_api_key( $api_key );
                break;
            case 'majestic':
                $result = $this->verify_majestic_api_key( $api_key );
                break;
            case 'serpapi':
                $result = $this->verify_serpapi_api_key( $api_key );
                break;
            default:
                return new WP_Error( 'invalid_service', __( 'Service externe non pris en charge.', 'boss-seo' ), array( 'status' => 400 ) );
        }

        return rest_ensure_response( $result );
    }

    /**
     * Vérifie la validité d'une clé API PageSpeed Insights.
     *
     * @since    1.1.0
     * @param    string    $api_key    Clé API à vérifier.
     * @return   array                 Résultat de la vérification.
     */
    private function verify_pagespeed_api_key( $api_key ) {
        // Appeler l'API PageSpeed Insights pour vérifier la clé
        $url = add_query_arg(
            array(
                'url' => home_url(),
                'key' => $api_key
            ),
            'https://www.googleapis.com/pagespeedonline/v5/runPagespeed'
        );

        $response = wp_remote_get( $url, array( 'timeout' => 15 ) );

        // Vérifier les erreurs
        if ( is_wp_error( $response ) ) {
            return array(
                'success' => false,
                'message' => $response->get_error_message()
            );
        }

        $status_code = wp_remote_retrieve_response_code( $response );
        $body = json_decode( wp_remote_retrieve_body( $response ), true );

        if ( $status_code === 200 && isset( $body['lighthouseResult'] ) ) {
            return array(
                'success' => true,
                'message' => __( 'Clé API PageSpeed Insights valide.', 'boss-seo' )
            );
        } else {
            $error_message = isset( $body['error']['message'] ) ? $body['error']['message'] : __( 'Clé API PageSpeed Insights invalide.', 'boss-seo' );
            return array(
                'success' => false,
                'message' => $error_message
            );
        }
    }

    /**
     * Vérifie les permissions pour enregistrer les paramètres.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Requête REST complète.
     * @return   boolean                        True si l'utilisateur a les permissions, false sinon.
     */
    public function save_settings_permissions_check( $request ) {
        return current_user_can( 'manage_options' );
    }

    /**
     * Vérifie les permissions pour les endpoints de mots-clés.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Requête REST complète.
     * @return   boolean                        True si l'utilisateur a les permissions, false sinon.
     */
    public function keywords_permissions_check( $request ) {
        return current_user_can( 'edit_posts' );
    }

    /**
     * Vérifie les permissions pour les endpoints d'images.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Requête REST complète.
     * @return   boolean                        True si l'utilisateur a les permissions, false sinon.
     */
    public function images_permissions_check( $request ) {
        return current_user_can( 'upload_files' );
    }

    /**
     * Enregistre les paramètres.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Requête REST complète.
     * @return   WP_REST_Response               Réponse REST.
     */
    public function save_settings( $request ) {
        $settings = $request->get_param( 'settings' );

        // Convertir les paramètres au format attendu par la classe Settings
        $formatted_settings = array();

        // Traiter les paramètres AI
        if ( isset( $settings['ai'] ) ) {
            $ai_settings = $settings['ai'];
            $formatted_settings['ai'] = array(
                'provider' => isset( $ai_settings['provider'] ) ? sanitize_text_field( $ai_settings['provider'] ) : 'openai',
                'openai_api_key' => isset( $ai_settings['openaiApiKey'] ) ? sanitize_text_field( $ai_settings['openaiApiKey'] ) : '',
                'openai_model' => isset( $ai_settings['openaiModel'] ) ? sanitize_text_field( $ai_settings['openaiModel'] ) : 'gpt-4',
                'openai_temperature' => isset( $ai_settings['openaiTemperature'] ) ? floatval( $ai_settings['openaiTemperature'] ) : 0.7,
                'anthropic_api_key' => isset( $ai_settings['anthropicApiKey'] ) ? sanitize_text_field( $ai_settings['anthropicApiKey'] ) : '',
                'anthropic_model' => isset( $ai_settings['anthropicModel'] ) ? sanitize_text_field( $ai_settings['anthropicModel'] ) : 'claude-3-opus',
                'anthropic_temperature' => isset( $ai_settings['anthropicTemperature'] ) ? floatval( $ai_settings['anthropicTemperature'] ) : 0.7,
                'gemini_api_key' => isset( $ai_settings['geminiApiKey'] ) ? sanitize_text_field( $ai_settings['geminiApiKey'] ) : '',
                'gemini_model' => isset( $ai_settings['geminiModel'] ) ? sanitize_text_field( $ai_settings['geminiModel'] ) : 'gemini-1.5-pro',
                'gemini_temperature' => isset( $ai_settings['geminiTemperature'] ) ? floatval( $ai_settings['geminiTemperature'] ) : 0.7,
                'use_ai_for_titles' => isset( $ai_settings['useAiForTitles'] ) ? (bool) $ai_settings['useAiForTitles'] : true,
                'use_ai_for_descriptions' => isset( $ai_settings['useAiForDescriptions'] ) ? (bool) $ai_settings['useAiForDescriptions'] : true,
                'use_ai_for_content' => isset( $ai_settings['useAiForContent'] ) ? (bool) $ai_settings['useAiForContent'] : true,
                'use_ai_for_alt_text' => isset( $ai_settings['useAiForAltText'] ) ? (bool) $ai_settings['useAiForAltText'] : true
            );
        }

        // Traiter les paramètres Analytics
        if ( isset( $settings['analytics'] ) ) {
            $analytics_settings = $settings['analytics'];
            $formatted_settings['analytics'] = array(
                'google_analytics_id' => isset( $analytics_settings['googleAnalyticsId'] ) ? sanitize_text_field( $analytics_settings['googleAnalyticsId'] ) : '',
                'google_analytics_version' => isset( $analytics_settings['googleAnalyticsVersion'] ) ? sanitize_text_field( $analytics_settings['googleAnalyticsVersion'] ) : 'ga4',
                'google_search_console_verification' => isset( $analytics_settings['googleSearchConsoleVerification'] ) ? sanitize_text_field( $analytics_settings['googleSearchConsoleVerification'] ) : '',
                'bing_webmaster_verification' => isset( $analytics_settings['bingWebmasterVerification'] ) ? sanitize_text_field( $analytics_settings['bingWebmasterVerification'] ) : '',
                'yandex_webmaster_verification' => isset( $analytics_settings['yandexWebmasterVerification'] ) ? sanitize_text_field( $analytics_settings['yandexWebmasterVerification'] ) : '',
                'baidu_webmaster_verification' => isset( $analytics_settings['baiduWebmasterVerification'] ) ? sanitize_text_field( $analytics_settings['baiduWebmasterVerification'] ) : '',
                'enable_enhanced_link_attribution' => isset( $analytics_settings['enableEnhancedLinkAttribution'] ) ? (bool) $analytics_settings['enableEnhancedLinkAttribution'] : true,
                'enable_demographic_reporting' => isset( $analytics_settings['enableDemographicReporting'] ) ? (bool) $analytics_settings['enableDemographicReporting'] : true,
                'anonymize_ip' => isset( $analytics_settings['anonymizeIp'] ) ? (bool) $analytics_settings['anonymizeIp'] : true
            );
        }

        // Traiter les paramètres Services
        if ( isset( $settings['services'] ) ) {
            $services_settings = $settings['services'];
            $formatted_settings['services'] = array(
                'semrush_api_key' => isset( $services_settings['semrushApiKey'] ) ? sanitize_text_field( $services_settings['semrushApiKey'] ) : '',
                'moz' => array(
                    'access_id' => isset( $services_settings['moz']['accessId'] ) ? sanitize_text_field( $services_settings['moz']['accessId'] ) : '',
                    'secret_key' => isset( $services_settings['moz']['secretKey'] ) ? sanitize_text_field( $services_settings['moz']['secretKey'] ) : ''
                ),
                'ahrefs' => array(
                    'api_key' => isset( $services_settings['ahrefs']['apiKey'] ) ? sanitize_text_field( $services_settings['ahrefs']['apiKey'] ) : ''
                ),
                'majestic' => array(
                    'api_key' => isset( $services_settings['majestic']['apiKey'] ) ? sanitize_text_field( $services_settings['majestic']['apiKey'] ) : ''
                ),
                'serpapi' => array(
                    'api_key' => isset( $services_settings['serpapi']['apiKey'] ) ? sanitize_text_field( $services_settings['serpapi']['apiKey'] ) : ''
                )
            );
        }

        // Enregistrer les paramètres
        $result = $this->settings->save( $formatted_settings );

        if ( $result ) {
            return new WP_REST_Response( array(
                'success' => true,
                'message' => __( 'Paramètres enregistrés avec succès.', 'boss-seo' )
            ), 200 );
        } else {
            return new WP_REST_Response( array(
                'success' => false,
                'message' => __( 'Erreur lors de l\'enregistrement des paramètres.', 'boss-seo' )
            ), 500 );
        }
    }

    /**
     * Vérifie la validité d'une clé API Google Gemini.
     *
     * @since    1.1.0
     * @param    string    $api_key    Clé API à vérifier.
     * @return   array                 Résultat de la vérification.
     */
    private function verify_gemini_api_key( $api_key ) {
        // Construire l'URL avec la clé API
        $url = 'https://generativelanguage.googleapis.com/v1beta/models?key=' . $api_key;

        // Appeler l'API Gemini pour vérifier la clé
        $response = wp_remote_get(
            $url,
            array(
                'headers' => array(
                    'Content-Type' => 'application/json'
                ),
                'timeout' => 15
            )
        );

        // Vérifier les erreurs
        if ( is_wp_error( $response ) ) {
            return array(
                'success' => false,
                'message' => $response->get_error_message()
            );
        }

        $status_code = wp_remote_retrieve_response_code( $response );
        $body = json_decode( wp_remote_retrieve_body( $response ), true );

        if ( $status_code === 200 && isset( $body['models'] ) ) {
            return array(
                'success' => true,
                'message' => __( 'Clé API Google Gemini valide.', 'boss-seo' )
            );
        } else {
            $error_message = isset( $body['error']['message'] ) ? $body['error']['message'] : __( 'Clé API Google Gemini invalide.', 'boss-seo' );
            return array(
                'success' => false,
                'message' => $error_message
            );
        }
    }

    /**
     * Vérifie les permissions pour générer du contenu avec l'IA.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Requête REST complète.
     * @return   boolean                        True si l'utilisateur a les permissions, false sinon.
     */
    public function generate_content_permissions_check( $request ) {
        return current_user_can( 'edit_posts' );
    }

    /**
     * Génère du contenu avec l'IA.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Requête REST complète.
     * @return   WP_REST_Response               Réponse REST.
     */
    public function generate_content_with_ai( $request ) {
        $prompt = $request->get_param( 'prompt' );
        $options = $request->get_param( 'options' );

        // Debug: Logger les paramètres reçus
        if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
            error_log( '[Boss SEO AI] Prompt reçu: ' . $prompt );
            error_log( '[Boss SEO AI] Options reçues: ' . print_r( $options, true ) );
        }

        // Validation supplémentaire du prompt
        if ( empty( trim( $prompt ) ) ) {
            return new WP_Error(
                'invalid_prompt',
                __( 'Le prompt ne peut pas être vide.', 'boss-seo' ),
                array( 'status' => 400 )
            );
        }

        if ( strlen( trim( $prompt ) ) < 5 ) {
            return new WP_Error(
                'prompt_too_short',
                __( 'Le prompt doit contenir au moins 5 caractères.', 'boss-seo' ),
                array( 'status' => 400 )
            );
        }

        // Vérifier si l'IA est configurée
        if ( ! $this->ai->is_available() ) {
            return new WP_Error( 'ai_not_configured', __( 'Les services d\'IA ne sont pas configurés.', 'boss-seo' ), array( 'status' => 400 ) );
        }

        // Générer le contenu
        $result = $this->ai->generate_content( $prompt, $options );

        if ( ! $result['success'] ) {
            return new WP_Error( 'generation_failed', $result['message'], array( 'status' => 500 ) );
        }

        return rest_ensure_response( $result );
    }

    /**
     * Recherche des mots-clés via SerpAPI.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Requête REST complète.
     * @return   WP_REST_Response               Réponse REST.
     */
    public function search_keywords( $request ) {
        $query = $request->get_param( 'query' );
        $language = $request->get_param( 'language' );
        $country = $request->get_param( 'country' );

        // Récupérer la clé API SerpAPI
        $external_services = get_option( 'boss_optimizer_external_services', array() );
        $serpapi_key = isset( $external_services['serpapi']['api_key'] ) ? $external_services['serpapi']['api_key'] : '';

        if ( empty( $serpapi_key ) ) {
            return new WP_Error( 'missing_api_key', __( 'Clé API SerpAPI non définie.', 'boss-seo' ), array( 'status' => 400 ) );
        }

        // Initialiser l'API SerpAPI
        $serpapi = new Boss_Seo_SerpAPI( $serpapi_key );

        // Rechercher des mots-clés
        $keywords = $serpapi->search_keywords( $query, $language, $country );

        if ( is_wp_error( $keywords ) ) {
            // En cas d'erreur avec SerpAPI, fournir des mots-clés fictifs pour le développement
            error_log( 'Boss SEO: SerpAPI failed, using fallback keywords for: ' . $query );

            $fallback_keywords = $this->get_fallback_keywords( $query );
            return rest_ensure_response( $fallback_keywords );
        }

        return rest_ensure_response( $keywords );
    }

    /**
     * Génère des mots-clés de secours en cas d'erreur avec l'API externe.
     *
     * @since    1.1.0
     * @param    string    $query    La requête de recherche.
     * @return   array               Les mots-clés de secours.
     */
    private function get_fallback_keywords( $query ) {
        $prefixes = array( 'comment', 'pourquoi', 'meilleur', 'top', 'guide', 'tutoriel', 'comparatif', 'avis' );
        $suffixes = array( 'pas cher', 'gratuit', 'en ligne', 'professionnel', 'facile', 'rapide', 'pour débutant', 'avancé' );

        $keywords = array();

        // Ajouter le mot-clé principal
        $keywords[] = array(
            'text'       => $query,
            'volume'     => '1000-10000',
            'difficulty' => rand( 30, 70 ),
            'cpc'        => number_format( 0.5 + ( 2.0 - 0.5 ) * (float) rand() / (float) getrandmax(), 2 ),
        );

        // Ajouter des variations avec préfixes
        foreach ( array_slice( $prefixes, 0, 3 ) as $prefix ) {
            $keywords[] = array(
                'text'       => $prefix . ' ' . $query,
                'volume'     => '100-1000',
                'difficulty' => rand( 20, 50 ),
                'cpc'        => number_format( 0.2 + ( 1.0 - 0.2 ) * (float) rand() / (float) getrandmax(), 2 ),
            );
        }

        // Ajouter des variations avec suffixes
        foreach ( array_slice( $suffixes, 0, 3 ) as $suffix ) {
            $keywords[] = array(
                'text'       => $query . ' ' . $suffix,
                'volume'     => '100-1000',
                'difficulty' => rand( 20, 50 ),
                'cpc'        => number_format( 0.2 + ( 1.0 - 0.2 ) * (float) rand() / (float) getrandmax(), 2 ),
            );
        }

        return $keywords;
    }

    /**
     * Récupère les mots-clés connexes.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Requête REST complète.
     * @return   WP_REST_Response               Réponse REST.
     */
    public function get_related_keywords( $request ) {
        $keyword = $request->get_param( 'keyword' );
        $language = $request->get_param( 'language' );
        $country = $request->get_param( 'country' );

        // Récupérer la clé API SerpAPI
        $external_services = get_option( 'boss_optimizer_external_services', array() );
        $serpapi_key = isset( $external_services['serpapi']['api_key'] ) ? $external_services['serpapi']['api_key'] : '';

        if ( empty( $serpapi_key ) ) {
            return new WP_Error( 'missing_api_key', __( 'Clé API SerpAPI non définie.', 'boss-seo' ), array( 'status' => 400 ) );
        }

        // Initialiser l'API SerpAPI
        $serpapi = new Boss_Seo_SerpAPI( $serpapi_key );

        // Récupérer les mots-clés connexes
        $keywords = $serpapi->get_related_keywords( $keyword, $language, $country );

        if ( is_wp_error( $keywords ) ) {
            return $keywords;
        }

        return rest_ensure_response( $keywords );
    }

    /**
     * Récupère les mots-clés à longue traîne.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Requête REST complète.
     * @return   WP_REST_Response               Réponse REST.
     */
    public function get_long_tail_keywords( $request ) {
        $keyword = $request->get_param( 'keyword' );
        $language = $request->get_param( 'language' );
        $country = $request->get_param( 'country' );

        // Récupérer la clé API SerpAPI
        $external_services = get_option( 'boss_optimizer_external_services', array() );
        $serpapi_key = isset( $external_services['serpapi']['api_key'] ) ? $external_services['serpapi']['api_key'] : '';

        if ( empty( $serpapi_key ) ) {
            return new WP_Error( 'missing_api_key', __( 'Clé API SerpAPI non définie.', 'boss-seo' ), array( 'status' => 400 ) );
        }

        // Initialiser l'API SerpAPI
        $serpapi = new Boss_Seo_SerpAPI( $serpapi_key );

        // Récupérer les mots-clés à longue traîne
        $keywords = $serpapi->get_long_tail_keywords( $keyword, $language, $country );

        if ( is_wp_error( $keywords ) ) {
            return $keywords;
        }

        return rest_ensure_response( $keywords );
    }

    /**
     * Récupère les mots-clés suggérés par l'IA.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Requête REST complète.
     * @return   WP_REST_Response               Réponse REST.
     */
    public function get_ai_suggested_keywords( $request ) {
        $topic = $request->get_param( 'topic' );
        $content_type = $request->get_param( 'content_type' );

        // Vérifier si l'IA est configurée
        if ( ! $this->ai->is_available() ) {
            return new WP_Error( 'ai_not_configured', __( 'Les services d\'IA ne sont pas configurés.', 'boss-seo' ), array( 'status' => 400 ) );
        }

        // Construire le prompt pour l'IA
        $prompt = sprintf(
            __( 'Génère une liste de 10 mots-clés pertinents pour un %s sur le sujet "%s". Pour chaque mot-clé, fournis une estimation du volume de recherche (faible, moyen, élevé) et une difficulté de classement (de 1 à 100). Retourne les résultats au format JSON.', 'boss-seo' ),
            $content_type,
            $topic
        );

        // Générer les suggestions de mots-clés
        $result = $this->ai->generate_content( $prompt, array(
            'format' => 'json',
            'temperature' => 0.7
        ) );

        if ( ! $result['success'] ) {
            return new WP_Error( 'generation_failed', $result['message'], array( 'status' => 500 ) );
        }

        // Traiter la réponse JSON
        $content = $result['content'];
        $keywords = array();

        try {
            $data = json_decode( $content, true );

            if ( is_array( $data ) ) {
                foreach ( $data as $item ) {
                    if ( isset( $item['keyword'] ) && isset( $item['volume'] ) && isset( $item['difficulty'] ) ) {
                        $volume = $item['volume'];

                        // Convertir le volume textuel en valeur numérique
                        if ( $volume === 'faible' || $volume === 'low' ) {
                            $volume = '100-1000';
                        } elseif ( $volume === 'moyen' || $volume === 'medium' ) {
                            $volume = '1000-10000';
                        } elseif ( $volume === 'élevé' || $volume === 'high' ) {
                            $volume = '10000+';
                        }

                        $keywords[] = array(
                            'text' => $item['keyword'],
                            'volume' => $volume,
                            'difficulty' => intval( $item['difficulty'] ),
                            'cpc' => isset( $item['cpc'] ) ? $item['cpc'] : '0.50',
                        );
                    }
                }
            }
        } catch ( Exception $e ) {
            return new WP_Error( 'json_parse_error', __( 'Erreur lors de l\'analyse des suggestions de mots-clés.', 'boss-seo' ), array( 'status' => 500 ) );
        }

        return rest_ensure_response( $keywords );
    }

    /**
     * Sauvegarde un mot-clé dans les favoris.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Requête REST complète.
     * @return   WP_REST_Response               Réponse REST.
     */
    public function save_keyword( $request ) {
        $keyword = $request->get_param( 'keyword' );

        // Récupérer les mots-clés sauvegardés
        $saved_keywords = get_option( 'boss_seo_saved_keywords', array() );

        // Vérifier si le mot-clé existe déjà
        foreach ( $saved_keywords as $saved_keyword ) {
            if ( $saved_keyword['text'] === $keyword['text'] ) {
                return new WP_REST_Response( array(
                    'success' => true,
                    'message' => __( 'Mot-clé déjà sauvegardé.', 'boss-seo' )
                ), 200 );
            }
        }

        // Ajouter le mot-clé aux favoris
        $saved_keywords[] = $keyword;

        // Sauvegarder les mots-clés
        update_option( 'boss_seo_saved_keywords', $saved_keywords );

        return new WP_REST_Response( array(
            'success' => true,
            'message' => __( 'Mot-clé sauvegardé avec succès.', 'boss-seo' )
        ), 200 );
    }

    /**
     * Récupère les mots-clés sauvegardés.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Requête REST complète.
     * @return   WP_REST_Response               Réponse REST.
     */
    public function get_saved_keywords( $request ) {
        $saved_keywords = get_option( 'boss_seo_saved_keywords', array() );

        return rest_ensure_response( $saved_keywords );
    }

    /**
     * Recherche des images via les API d'images.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Requête REST complète.
     * @return   WP_REST_Response               Réponse REST.
     */
    public function search_images( $request ) {
        $query = $request->get_param( 'query' );
        $source = $request->get_param( 'source' );
        $per_page = $request->get_param( 'per_page' );
        $page = $request->get_param( 'page' );

        // Récupérer les clés API d'images
        $external_services = get_option( 'boss_optimizer_external_services', array() );
        $pexels_api_key = isset( $external_services['pexels']['api_key'] ) ? $external_services['pexels']['api_key'] : '';
        $unsplash_api_key = isset( $external_services['unsplash']['api_key'] ) ? $external_services['unsplash']['api_key'] : '';
        $pixabay_api_key = isset( $external_services['pixabay']['api_key'] ) ? $external_services['pixabay']['api_key'] : '';

        // Vérifier si la source est disponible
        if ( $source === 'pexels' && empty( $pexels_api_key ) ) {
            return new WP_Error( 'missing_api_key', __( 'Clé API Pexels non définie.', 'boss-seo' ), array( 'status' => 400 ) );
        } elseif ( $source === 'unsplash' && empty( $unsplash_api_key ) ) {
            return new WP_Error( 'missing_api_key', __( 'Clé API Unsplash non définie.', 'boss-seo' ), array( 'status' => 400 ) );
        } elseif ( $source === 'pixabay' && empty( $pixabay_api_key ) ) {
            return new WP_Error( 'missing_api_key', __( 'Clé API Pixabay non définie.', 'boss-seo' ), array( 'status' => 400 ) );
        }

        // Initialiser l'API d'images
        $image_api = new Boss_Seo_Image_API( $pexels_api_key, $unsplash_api_key, $pixabay_api_key );

        // Rechercher des images
        $images = $image_api->search_images( $query, $source, $per_page, $page );

        if ( is_wp_error( $images ) ) {
            return $images;
        }

        return rest_ensure_response( $images );
    }

    /**
     * Importe une image dans la médiathèque WordPress.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Requête REST complète.
     * @return   WP_REST_Response               Réponse REST.
     */
    public function import_image( $request ) {
        $image = $request->get_param( 'image' );

        // Récupérer les clés API d'images
        $external_services = get_option( 'boss_optimizer_external_services', array() );
        $pexels_api_key = isset( $external_services['pexels']['api_key'] ) ? $external_services['pexels']['api_key'] : '';
        $unsplash_api_key = isset( $external_services['unsplash']['api_key'] ) ? $external_services['unsplash']['api_key'] : '';
        $pixabay_api_key = isset( $external_services['pixabay']['api_key'] ) ? $external_services['pixabay']['api_key'] : '';

        // Initialiser l'API d'images
        $image_api = new Boss_Seo_Image_API( $pexels_api_key, $unsplash_api_key, $pixabay_api_key );

        // Importer l'image
        $imported_image = $image_api->import_image( $image );

        if ( is_wp_error( $imported_image ) ) {
            return $imported_image;
        }

        return new WP_REST_Response( array(
            'success' => true,
            'message' => __( 'Image importée avec succès.', 'boss-seo' ),
            'image' => $imported_image
        ), 200 );
    }

    /**
     * Importe plusieurs images dans la médiathèque WordPress.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Requête REST complète.
     * @return   WP_REST_Response               Réponse REST.
     */
    public function import_images( $request ) {
        $images = $request->get_param( 'images' );

        // Récupérer les clés API d'images
        $external_services = get_option( 'boss_optimizer_external_services', array() );
        $pexels_api_key = isset( $external_services['pexels']['api_key'] ) ? $external_services['pexels']['api_key'] : '';
        $unsplash_api_key = isset( $external_services['unsplash']['api_key'] ) ? $external_services['unsplash']['api_key'] : '';
        $pixabay_api_key = isset( $external_services['pixabay']['api_key'] ) ? $external_services['pixabay']['api_key'] : '';

        // Initialiser l'API d'images
        $image_api = new Boss_Seo_Image_API( $pexels_api_key, $unsplash_api_key, $pixabay_api_key );

        // Importer les images
        $imported_images = array();
        $errors = array();

        foreach ( $images as $image ) {
            $result = $image_api->import_image( $image );

            if ( is_wp_error( $result ) ) {
                $errors[] = array(
                    'image' => $image,
                    'error' => $result->get_error_message()
                );
            } else {
                $imported_images[] = $result;
            }
        }

        return new WP_REST_Response( array(
            'success' => true,
            'message' => sprintf( __( '%d images importées avec succès.', 'boss-seo' ), count( $imported_images ) ),
            'images' => $imported_images,
            'errors' => $errors
        ), 200 );
    }

    /**
     * Récupère les sources d'images disponibles.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Requête REST complète.
     * @return   WP_REST_Response               Réponse REST.
     */
    public function get_image_sources( $request ) {
        // Récupérer les clés API d'images
        $external_services = get_option( 'boss_optimizer_external_services', array() );
        $pexels_api_key = isset( $external_services['pexels']['api_key'] ) ? $external_services['pexels']['api_key'] : '';
        $unsplash_api_key = isset( $external_services['unsplash']['api_key'] ) ? $external_services['unsplash']['api_key'] : '';
        $pixabay_api_key = isset( $external_services['pixabay']['api_key'] ) ? $external_services['pixabay']['api_key'] : '';

        $sources = array();

        if ( ! empty( $pexels_api_key ) ) {
            $sources[] = array(
                'label' => 'Pexels',
                'value' => 'pexels'
            );
        }

        if ( ! empty( $unsplash_api_key ) ) {
            $sources[] = array(
                'label' => 'Unsplash',
                'value' => 'unsplash'
            );
        }

        if ( ! empty( $pixabay_api_key ) ) {
            $sources[] = array(
                'label' => 'Pixabay',
                'value' => 'pixabay'
            );
        }

        return rest_ensure_response( $sources );
    }

    /**
     * Récupère les paramètres des API d'images.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Requête REST complète.
     * @return   WP_REST_Response               Réponse REST.
     */
    public function get_image_api_settings( $request ) {
        // Récupérer les clés API d'images
        $external_services = get_option( 'boss_optimizer_external_services', array() );
        $pexels_api_key = isset( $external_services['pexels']['api_key'] ) ? $external_services['pexels']['api_key'] : '';
        $unsplash_api_key = isset( $external_services['unsplash']['api_key'] ) ? $external_services['unsplash']['api_key'] : '';
        $pixabay_api_key = isset( $external_services['pixabay']['api_key'] ) ? $external_services['pixabay']['api_key'] : '';

        $settings = array(
            'pexels' => array(
                'api_key' => ! empty( $pexels_api_key ),
                'configured' => ! empty( $pexels_api_key )
            ),
            'unsplash' => array(
                'api_key' => ! empty( $unsplash_api_key ),
                'configured' => ! empty( $unsplash_api_key )
            ),
            'pixabay' => array(
                'api_key' => ! empty( $pixabay_api_key ),
                'configured' => ! empty( $pixabay_api_key )
            )
        );

        return rest_ensure_response( $settings );
    }

    /**
     * Vérifie le statut d'une API d'images.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Requête REST complète.
     * @return   WP_REST_Response               Réponse REST.
     */
    public function check_image_api_status( $request ) {
        $source = $request->get_param( 'source' );

        // Récupérer les clés API d'images
        $external_services = get_option( 'boss_optimizer_external_services', array() );
        $pexels_api_key = isset( $external_services['pexels']['api_key'] ) ? $external_services['pexels']['api_key'] : '';
        $unsplash_api_key = isset( $external_services['unsplash']['api_key'] ) ? $external_services['unsplash']['api_key'] : '';
        $pixabay_api_key = isset( $external_services['pixabay']['api_key'] ) ? $external_services['pixabay']['api_key'] : '';

        $status = array(
            'configured' => false,
            'message' => ''
        );

        switch ( $source ) {
            case 'pexels':
                $status['configured'] = ! empty( $pexels_api_key );
                $status['message'] = $status['configured']
                    ? __( 'API Pexels configurée.', 'boss-seo' )
                    : __( 'API Pexels non configurée.', 'boss-seo' );
                break;
            case 'unsplash':
                $status['configured'] = ! empty( $unsplash_api_key );
                $status['message'] = $status['configured']
                    ? __( 'API Unsplash configurée.', 'boss-seo' )
                    : __( 'API Unsplash non configurée.', 'boss-seo' );
                break;
            case 'pixabay':
                $status['configured'] = ! empty( $pixabay_api_key );
                $status['message'] = $status['configured']
                    ? __( 'API Pixabay configurée.', 'boss-seo' )
                    : __( 'API Pixabay non configurée.', 'boss-seo' );
                break;
            default:
                $status['message'] = __( 'Source d\'images non valide.', 'boss-seo' );
                break;
        }

        return rest_ensure_response( $status );
    }

    /**
     * Ajoute des balises à plusieurs contenus.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Requête REST complète.
     * @return   WP_REST_Response               Réponse REST.
     */
    public function add_tags_to_contents( $request ) {
        $post_ids = $request->get_param( 'ids' );
        $tags = $request->get_param( 'tags' );
        $success_count = 0;
        $error_count = 0;
        $results = array();

        foreach ( $post_ids as $post_id ) {
            $post = get_post( $post_id );

            if ( ! $post ) {
                $results[ $post_id ] = array(
                    'success' => false,
                    'message' => __( 'Contenu non trouvé.', 'boss-seo' )
                );
                $error_count++;
                continue;
            }

            // Vérifier les permissions
            if ( ! current_user_can( 'edit_post', $post_id ) ) {
                $results[ $post_id ] = array(
                    'success' => false,
                    'message' => __( 'Permissions insuffisantes pour modifier ce contenu.', 'boss-seo' )
                );
                $error_count++;
                continue;
            }

            // Ajouter les balises
            $tag_result = wp_set_post_tags( $post_id, $tags, true ); // true = append

            if ( is_wp_error( $tag_result ) ) {
                $results[ $post_id ] = array(
                    'success' => false,
                    'message' => $tag_result->get_error_message()
                );
                $error_count++;
            } else {
                $results[ $post_id ] = array(
                    'success' => true,
                    'message' => __( 'Balises ajoutées avec succès.', 'boss-seo' ),
                    'tags_added' => $tags
                );
                $success_count++;
            }
        }

        return rest_ensure_response( array(
            'success' => $success_count > 0,
            'message' => sprintf(
                __( '%d contenus mis à jour avec succès, %d erreurs.', 'boss-seo' ),
                $success_count,
                $error_count
            ),
            'results' => $results,
            'summary' => array(
                'success' => $success_count,
                'errors' => $error_count,
                'total' => count( $post_ids )
            )
        ) );
    }

    /**
     * Change la catégorie de plusieurs contenus.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Requête REST complète.
     * @return   WP_REST_Response               Réponse REST.
     */
    public function change_category_for_contents( $request ) {
        $post_ids = $request->get_param( 'ids' );
        $category_id = $request->get_param( 'category_id' );
        $success_count = 0;
        $error_count = 0;
        $results = array();

        // Vérifier que la catégorie existe
        $category = get_category( $category_id );
        if ( ! $category || is_wp_error( $category ) ) {
            return new WP_REST_Response( array(
                'success' => false,
                'message' => __( 'Catégorie non trouvée.', 'boss-seo' )
            ), 404 );
        }

        foreach ( $post_ids as $post_id ) {
            $post = get_post( $post_id );

            if ( ! $post ) {
                $results[ $post_id ] = array(
                    'success' => false,
                    'message' => __( 'Contenu non trouvé.', 'boss-seo' )
                );
                $error_count++;
                continue;
            }

            // Vérifier les permissions
            if ( ! current_user_can( 'edit_post', $post_id ) ) {
                $results[ $post_id ] = array(
                    'success' => false,
                    'message' => __( 'Permissions insuffisantes pour modifier ce contenu.', 'boss-seo' )
                );
                $error_count++;
                continue;
            }

            // Changer la catégorie
            $category_result = wp_set_post_categories( $post_id, array( $category_id ) );

            if ( is_wp_error( $category_result ) ) {
                $results[ $post_id ] = array(
                    'success' => false,
                    'message' => $category_result->get_error_message()
                );
                $error_count++;
            } else {
                $results[ $post_id ] = array(
                    'success' => true,
                    'message' => __( 'Catégorie modifiée avec succès.', 'boss-seo' ),
                    'new_category' => $category->name
                );
                $success_count++;
            }
        }

        return rest_ensure_response( array(
            'success' => $success_count > 0,
            'message' => sprintf(
                __( '%d contenus mis à jour avec succès, %d erreurs.', 'boss-seo' ),
                $success_count,
                $error_count
            ),
            'results' => $results,
            'summary' => array(
                'success' => $success_count,
                'errors' => $error_count,
                'total' => count( $post_ids )
            )
        ) );
    }

    /**
     * Change l'auteur de plusieurs contenus.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Requête REST complète.
     * @return   WP_REST_Response               Réponse REST.
     */
    public function change_author_for_contents( $request ) {
        $post_ids = $request->get_param( 'ids' );
        $author_id = $request->get_param( 'author_id' );
        $success_count = 0;
        $error_count = 0;
        $results = array();

        // Vérifier que l'auteur existe
        $author = get_user_by( 'id', $author_id );
        if ( ! $author ) {
            return new WP_REST_Response( array(
                'success' => false,
                'message' => __( 'Auteur non trouvé.', 'boss-seo' )
            ), 404 );
        }

        foreach ( $post_ids as $post_id ) {
            $post = get_post( $post_id );

            if ( ! $post ) {
                $results[ $post_id ] = array(
                    'success' => false,
                    'message' => __( 'Contenu non trouvé.', 'boss-seo' )
                );
                $error_count++;
                continue;
            }

            // Vérifier les permissions
            if ( ! current_user_can( 'edit_post', $post_id ) ) {
                $results[ $post_id ] = array(
                    'success' => false,
                    'message' => __( 'Permissions insuffisantes pour modifier ce contenu.', 'boss-seo' )
                );
                $error_count++;
                continue;
            }

            // Changer l'auteur
            $update_result = wp_update_post( array(
                'ID' => $post_id,
                'post_author' => $author_id
            ) );

            if ( is_wp_error( $update_result ) ) {
                $results[ $post_id ] = array(
                    'success' => false,
                    'message' => $update_result->get_error_message()
                );
                $error_count++;
            } else {
                $results[ $post_id ] = array(
                    'success' => true,
                    'message' => __( 'Auteur modifié avec succès.', 'boss-seo' ),
                    'new_author' => $author->display_name
                );
                $success_count++;
            }
        }

        return rest_ensure_response( array(
            'success' => $success_count > 0,
            'message' => sprintf(
                __( '%d contenus mis à jour avec succès, %d erreurs.', 'boss-seo' ),
                $success_count,
                $error_count
            ),
            'results' => $results,
            'summary' => array(
                'success' => $success_count,
                'errors' => $error_count,
                'total' => count( $post_ids )
            )
        ) );
    }

    // Méthode manage_content_permissions_check supprimée - dupliquée plus haut dans la classe

}
