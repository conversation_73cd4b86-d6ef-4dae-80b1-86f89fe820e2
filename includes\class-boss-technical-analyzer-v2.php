<?php
/**
 * Analyseur technique Boss SEO v2.0 - Version corrigée et fonctionnelle
 *
 * @link       https://bossseo.com
 * @since      1.1.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 */

// Empêcher l'accès direct
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Analyseur technique Boss SEO v2.0
 *
 * Cette classe gère l'analyse technique complète d'un site web en utilisant
 * l'API Google PageSpeed Insights et génère des suggestions IA intelligentes.
 *
 * @since      1.1.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 * <AUTHOR> SEO Team
 */
class Boss_Technical_Analyzer_V2 {

    /**
     * Le nom du plugin.
     *
     * @since    1.1.0
     * @access   private
     * @var      string    $plugin_name    Le nom du plugin.
     */
    private $plugin_name;

    /**
     * La version du plugin.
     *
     * @since    1.1.0
     * @access   private
     * @var      string    $version    La version du plugin.
     */
    private $version;

    /**
     * Instance du gestionnaire PageSpeed.
     *
     * @since    1.1.0
     * @access   private
     * @var      Boss_PageSpeed_Manager    $pagespeed_manager    Instance du gestionnaire PageSpeed.
     */
    private $pagespeed_manager;

    /**
     * Instance du générateur de suggestions IA.
     *
     * @since    1.1.0
     * @access   private
     * @var      Boss_AI_Suggestions_Generator    $ai_generator    Instance du générateur IA.
     */
    private $ai_generator;

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.1.0
     * @param    string    $plugin_name       Le nom du plugin.
     * @param    string    $version           La version du plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;

        // Charger les dépendances
        $this->load_dependencies();
    }

    /**
     * Charge les dépendances nécessaires.
     *
     * @since    1.1.0
     * @access   private
     */
    private function load_dependencies() {
        // Charger le gestionnaire PageSpeed
        if ( ! class_exists( 'Boss_PageSpeed_Manager' ) ) {
            require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-boss-pagespeed-manager.php';
        }

        // Charger le générateur de suggestions IA
        if ( ! class_exists( 'Boss_AI_Suggestions_Generator' ) ) {
            require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-boss-ai-suggestions-generator.php';
        }

        try {
            // Initialiser le gestionnaire PageSpeed
            $this->pagespeed_manager = new Boss_PageSpeed_Manager();

            // Initialiser le générateur de suggestions IA
            $this->ai_generator = new Boss_AI_Suggestions_Generator( $this->plugin_name, $this->version );
        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur lors de l\'initialisation de l\'analyseur technique v2: ' . $e->getMessage() );
        }
    }

    /**
     * Enregistre les hooks pour cette classe.
     *
     * @since    1.1.0
     */
    public function register_hooks() {
        // Enregistrer les routes REST API
        add_action( 'rest_api_init', array( $this, 'register_rest_routes' ) );
    }

    /**
     * Enregistre les routes REST API.
     *
     * @since    1.1.0
     */
    public function register_rest_routes() {
        // Route pour obtenir la liste des pages
        register_rest_route( 'boss-seo/v2', '/technical/pages', array(
            'methods'             => WP_REST_Server::READABLE,
            'callback'            => array( $this, 'get_pages_list' ),
            'permission_callback' => array( $this, 'check_permissions' ),
        ) );

        // Route pour analyser une page
        register_rest_route( 'boss-seo/v2', '/technical/analyze', array(
            'methods'             => WP_REST_Server::CREATABLE,
            'callback'            => array( $this, 'analyze_page' ),
            'permission_callback' => array( $this, 'check_permissions' ),
            'args'                => array(
                'url' => array(
                    'required'          => true,
                    'validate_callback' => array( $this, 'validate_url' ),
                ),
                'strategy' => array(
                    'default'           => 'mobile',
                    'validate_callback' => array( $this, 'validate_strategy' ),
                ),
                'include_ai_suggestions' => array(
                    'default' => true,
                    'type'    => 'boolean',
                ),
            ),
        ) );

        // Route pour l'historique des analyses
        register_rest_route( 'boss-seo/v2', '/technical/history', array(
            'methods'             => WP_REST_Server::READABLE,
            'callback'            => array( $this, 'get_analysis_history' ),
            'permission_callback' => array( $this, 'check_permissions' ),
            'args'                => array(
                'url' => array(
                    'validate_callback' => array( $this, 'validate_url' ),
                ),
                'limit' => array(
                    'default'           => 10,
                    'validate_callback' => array( $this, 'validate_limit' ),
                ),
            ),
        ) );

        // Route pour les suggestions IA
        register_rest_route( 'boss-seo/v2', '/technical/ai-suggestions', array(
            'methods'             => WP_REST_Server::CREATABLE,
            'callback'            => array( $this, 'generate_ai_suggestions' ),
            'permission_callback' => array( $this, 'check_permissions' ),
            'args'                => array(
                'analysis_data' => array(
                    'required' => true,
                    'type'     => 'object',
                ),
                'url' => array(
                    'required'          => true,
                    'validate_callback' => array( $this, 'validate_url' ),
                ),
            ),
        ) );
    }

    /**
     * Vérifie les permissions pour les routes REST.
     *
     * @since    1.1.0
     * @return   bool    True si l'utilisateur a les permissions.
     */
    public function check_permissions() {
        return current_user_can( 'manage_options' );
    }

    /**
     * Valide une URL.
     *
     * @since    1.1.0
     * @param    string    $url    URL à valider.
     * @return   bool              True si l'URL est valide.
     */
    public function validate_url( $url ) {
        return filter_var( $url, FILTER_VALIDATE_URL ) !== false;
    }

    /**
     * Valide une stratégie d'analyse.
     *
     * @since    1.1.0
     * @param    string    $strategy    Stratégie à valider.
     * @return   bool                   True si la stratégie est valide.
     */
    public function validate_strategy( $strategy ) {
        return in_array( $strategy, array( 'mobile', 'desktop' ), true );
    }

    /**
     * Valide une limite.
     *
     * @since    1.1.0
     * @param    int    $limit    Limite à valider.
     * @return   bool             True si la limite est valide.
     */
    public function validate_limit( $limit ) {
        return is_numeric( $limit ) && $limit > 0 && $limit <= 100;
    }

    /**
     * Obtient la liste des pages disponibles pour l'analyse.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Requête REST.
     * @return   WP_REST_Response               Réponse REST.
     */
    public function get_pages_list( $request ) {
        try {
            $pages = array();

            // Ajouter la page d'accueil
            $pages[] = array(
                'id'    => 0,
                'title' => __( 'Page d\'accueil', 'boss-seo' ),
                'url'   => home_url(),
                'type'  => 'home',
            );

            // Obtenir toutes les pages publiées
            $wp_pages = get_pages( array(
                'post_status' => 'publish',
                'number'      => 50,
            ) );

            foreach ( $wp_pages as $page ) {
                $pages[] = array(
                    'id'    => $page->ID,
                    'title' => $page->post_title,
                    'url'   => get_permalink( $page->ID ),
                    'type'  => 'page',
                );
            }

            // Obtenir tous les articles publiés (limité à 20)
            $posts = get_posts( array(
                'post_status'    => 'publish',
                'posts_per_page' => 20,
                'post_type'      => 'post',
            ) );

            foreach ( $posts as $post ) {
                $pages[] = array(
                    'id'    => $post->ID,
                    'title' => $post->post_title,
                    'url'   => get_permalink( $post->ID ),
                    'type'  => 'post',
                );
            }

            return rest_ensure_response( array(
                'success' => true,
                'data'    => $pages,
                'total'   => count( $pages ),
            ) );

        } catch ( Exception $e ) {
            return new WP_Error(
                'pages_list_error',
                __( 'Erreur lors de la récupération de la liste des pages: ', 'boss-seo' ) . $e->getMessage(),
                array( 'status' => 500 )
            );
        }
    }

    /**
     * Analyse une page spécifique avec PageSpeed Insights et génère des suggestions IA.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Requête REST.
     * @return   WP_REST_Response               Réponse REST.
     */
    public function analyze_page( $request ) {
        $url = $request->get_param( 'url' );
        $strategy = $request->get_param( 'strategy' );
        $include_ai_suggestions = $request->get_param( 'include_ai_suggestions' );

        try {
            // Vérifier que l'API PageSpeed est configurée
            if ( ! $this->is_pagespeed_api_configured() ) {
                return new WP_Error(
                    'api_not_configured',
                    __( 'L\'API Google PageSpeed Insights n\'est pas configurée. Veuillez configurer votre clé API dans Paramètres > Services externes.', 'boss-seo' ),
                    array( 'status' => 400 )
                );
            }

            // Démarrer l'analyse
            $analysis_start_time = microtime( true );

            // 1. Analyse PageSpeed Insights
            $pagespeed_data = $this->pagespeed_manager->analyze_url( $url, $strategy );

            if ( ! $pagespeed_data || isset( $pagespeed_data['error'] ) ) {
                return new WP_Error(
                    'pagespeed_analysis_failed',
                    __( 'L\'analyse PageSpeed Insights a échoué: ', 'boss-seo' ) . ( $pagespeed_data['error'] ?? __( 'Erreur inconnue', 'boss-seo' ) ),
                    array( 'status' => 500 )
                );
            }

            // 2. Analyse technique complémentaire
            $technical_analysis = $this->perform_basic_technical_analysis( $url );

            // 3. Combiner les données
            $combined_analysis = array(
                'pagespeed' => $pagespeed_data,
                'technical' => $technical_analysis,
            );

            // 4. Générer des suggestions IA si demandé
            $ai_suggestions = array();
            if ( $include_ai_suggestions && $this->is_ai_configured() ) {
                try {
                    $ai_suggestions = $this->ai_generator->generate_suggestions( $combined_analysis, $url );
                } catch ( Exception $e ) {
                    error_log( 'Boss SEO: Erreur lors de la génération des suggestions IA: ' . $e->getMessage() );
                    $ai_suggestions = array(
                        'error' => __( 'Impossible de générer les suggestions IA', 'boss-seo' ),
                    );
                }
            }

            // 5. Sauvegarder l'analyse dans l'historique
            $this->save_analysis_to_history( $url, $combined_analysis, $ai_suggestions );

            // 6. Préparer la réponse finale
            $analysis_end_time = microtime( true );
            $analysis_duration = round( ( $analysis_end_time - $analysis_start_time ) * 1000 );

            $response = array(
                'success'         => true,
                'url'             => $url,
                'strategy'        => $strategy,
                'timestamp'       => current_time( 'mysql' ),
                'analysis_time'   => $analysis_duration,
                'pagespeed'       => $pagespeed_data,
                'technical'       => $technical_analysis,
                'ai_suggestions'  => $ai_suggestions,
                'has_ai_suggestions' => ! empty( $ai_suggestions ) && ! isset( $ai_suggestions['error'] ),
            );

            return rest_ensure_response( $response );

        } catch ( Exception $e ) {
            return new WP_Error(
                'analysis_error',
                __( 'Erreur lors de l\'analyse: ', 'boss-seo' ) . $e->getMessage(),
                array( 'status' => 500 )
            );
        }
    }

    /**
     * Obtient l'historique des analyses.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Requête REST.
     * @return   WP_REST_Response               Réponse REST.
     */
    public function get_analysis_history( $request ) {
        $url = $request->get_param( 'url' );
        $limit = $request->get_param( 'limit' );

        try {
            $history = get_option( 'boss_seo_technical_analysis_history', array() );

            // Filtrer par URL si spécifiée
            if ( $url ) {
                $history = array_filter( $history, function( $item ) use ( $url ) {
                    return isset( $item['url'] ) && $item['url'] === $url;
                } );
            }

            // Trier par date (plus récent en premier)
            usort( $history, function( $a, $b ) {
                return strtotime( $b['timestamp'] ) - strtotime( $a['timestamp'] );
            } );

            // Limiter les résultats
            if ( $limit && count( $history ) > $limit ) {
                $history = array_slice( $history, 0, $limit );
            }

            return rest_ensure_response( array(
                'success' => true,
                'data'    => $history,
                'total'   => count( $history ),
            ) );

        } catch ( Exception $e ) {
            return new WP_Error(
                'history_error',
                __( 'Erreur lors de la récupération de l\'historique: ', 'boss-seo' ) . $e->getMessage(),
                array( 'status' => 500 )
            );
        }
    }

    /**
     * Génère des suggestions IA pour une analyse.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Requête REST.
     * @return   WP_REST_Response               Réponse REST.
     */
    public function generate_ai_suggestions( $request ) {
        $analysis_data = $request->get_param( 'analysis_data' );
        $url = $request->get_param( 'url' );

        try {
            if ( ! $this->is_ai_configured() ) {
                return new WP_Error(
                    'ai_not_configured',
                    __( 'L\'IA n\'est pas configurée. Veuillez configurer votre clé API dans Paramètres > API.', 'boss-seo' ),
                    array( 'status' => 400 )
                );
            }

            $suggestions = $this->ai_generator->generate_suggestions( $analysis_data, $url );

            return rest_ensure_response( array(
                'success'     => true,
                'suggestions' => $suggestions,
                'timestamp'   => current_time( 'mysql' ),
            ) );

        } catch ( Exception $e ) {
            return new WP_Error(
                'ai_suggestions_error',
                __( 'Erreur lors de la génération des suggestions IA: ', 'boss-seo' ) . $e->getMessage(),
                array( 'status' => 500 )
            );
        }
    }

    /**
     * Effectue une analyse technique de base.
     *
     * @since    1.1.0
     * @param    string    $url    URL à analyser.
     * @return   array             Résultats de l'analyse technique.
     */
    private function perform_basic_technical_analysis( $url ) {
        $analysis = array(
            'url'           => $url,
            'timestamp'     => current_time( 'mysql' ),
            'ssl_check'     => $this->check_ssl( $url ),
            'meta_tags'     => $this->analyze_meta_tags( $url ),
            'headings'      => $this->analyze_headings( $url ),
            'images'        => $this->analyze_images( $url ),
            'links'         => $this->analyze_links( $url ),
        );

        return $analysis;
    }

    /**
     * Vérifie si l'API PageSpeed est configurée.
     *
     * @since    1.1.0
     * @return   bool    True si l'API est configurée.
     */
    private function is_pagespeed_api_configured() {
        try {
            return $this->pagespeed_manager && $this->pagespeed_manager->is_configured();
        } catch ( Exception $e ) {
            return false;
        }
    }

    /**
     * Vérifie si l'IA est configurée.
     *
     * @since    1.1.0
     * @return   bool    True si l'IA est configurée.
     */
    private function is_ai_configured() {
        try {
            return $this->ai_generator && $this->ai_generator->is_configured();
        } catch ( Exception $e ) {
            return false;
        }
    }

    /**
     * Sauvegarde une analyse dans l'historique.
     *
     * @since    1.1.0
     * @param    string    $url               URL analysée.
     * @param    array     $analysis_data     Données d'analyse.
     * @param    array     $ai_suggestions    Suggestions IA.
     */
    private function save_analysis_to_history( $url, $analysis_data, $ai_suggestions ) {
        try {
            $history = get_option( 'boss_seo_technical_analysis_history', array() );

            $new_entry = array(
                'id'             => uniqid(),
                'url'            => $url,
                'timestamp'      => current_time( 'mysql' ),
                'analysis_data'  => $analysis_data,
                'ai_suggestions' => $ai_suggestions,
                'score'          => $this->calculate_overall_score( $analysis_data ),
            );

            // Ajouter au début du tableau
            array_unshift( $history, $new_entry );

            // Limiter à 100 entrées maximum
            if ( count( $history ) > 100 ) {
                $history = array_slice( $history, 0, 100 );
            }

            update_option( 'boss_seo_technical_analysis_history', $history );

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur lors de la sauvegarde de l\'historique: ' . $e->getMessage() );
        }
    }

    /**
     * Calcule le score global d'une analyse.
     *
     * @since    1.1.0
     * @param    array    $analysis_data    Données d'analyse.
     * @return   int                        Score global (0-100).
     */
    private function calculate_overall_score( $analysis_data ) {
        $score = 0;
        $total_weight = 0;

        // Score PageSpeed (poids: 60%)
        if ( isset( $analysis_data['pagespeed']['lighthouseResult']['categories']['performance']['score'] ) ) {
            $performance_score = $analysis_data['pagespeed']['lighthouseResult']['categories']['performance']['score'] * 100;
            $score += $performance_score * 0.6;
            $total_weight += 0.6;
        }

        // Score SEO (poids: 20%)
        if ( isset( $analysis_data['pagespeed']['lighthouseResult']['categories']['seo']['score'] ) ) {
            $seo_score = $analysis_data['pagespeed']['lighthouseResult']['categories']['seo']['score'] * 100;
            $score += $seo_score * 0.2;
            $total_weight += 0.2;
        }

        // Score technique de base (poids: 20%)
        if ( isset( $analysis_data['technical'] ) ) {
            $technical_score = $this->calculate_technical_score( $analysis_data['technical'] );
            $score += $technical_score * 0.2;
            $total_weight += 0.2;
        }

        return $total_weight > 0 ? round( $score / $total_weight ) : 0;
    }

    /**
     * Calcule le score technique de base.
     *
     * @since    1.1.0
     * @param    array    $technical_data    Données techniques.
     * @return   int                         Score technique (0-100).
     */
    private function calculate_technical_score( $technical_data ) {
        $score = 0;
        $checks = 0;

        // SSL
        if ( isset( $technical_data['ssl_check']['valid'] ) ) {
            $score += $technical_data['ssl_check']['valid'] ? 100 : 0;
            $checks++;
        }

        // Meta tags
        if ( isset( $technical_data['meta_tags'] ) ) {
            $meta_score = 0;
            if ( ! empty( $technical_data['meta_tags']['title'] ) ) $meta_score += 25;
            if ( ! empty( $technical_data['meta_tags']['description'] ) ) $meta_score += 25;
            if ( ! empty( $technical_data['meta_tags']['keywords'] ) ) $meta_score += 25;
            if ( isset( $technical_data['meta_tags']['viewport'] ) ) $meta_score += 25;
            $score += $meta_score;
            $checks++;
        }

        return $checks > 0 ? round( $score / $checks ) : 0;
    }

    /**
     * Vérifie le SSL d'une URL.
     *
     * @since    1.1.0
     * @param    string    $url    URL à vérifier.
     * @return   array             Résultats de la vérification SSL.
     */
    private function check_ssl( $url ) {
        $ssl_info = array(
            'valid'      => false,
            'https'      => false,
            'certificate' => array(),
        );

        try {
            $ssl_info['https'] = strpos( $url, 'https://' ) === 0;

            if ( $ssl_info['https'] ) {
                $context = stream_context_create( array(
                    'http' => array(
                        'timeout' => 10,
                    ),
                ) );

                $response = wp_remote_get( $url, array(
                    'timeout' => 10,
                    'sslverify' => true,
                ) );

                $ssl_info['valid'] = ! is_wp_error( $response );
            }
        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur lors de la vérification SSL: ' . $e->getMessage() );
        }

        return $ssl_info;
    }

    /**
     * Analyse les meta tags d'une URL.
     *
     * @since    1.1.0
     * @param    string    $url    URL à analyser.
     * @return   array             Meta tags trouvés.
     */
    private function analyze_meta_tags( $url ) {
        $meta_tags = array();

        try {
            $response = wp_remote_get( $url, array( 'timeout' => 15 ) );

            if ( ! is_wp_error( $response ) ) {
                $html = wp_remote_retrieve_body( $response );

                // Titre
                if ( preg_match( '/<title[^>]*>(.*?)<\/title>/is', $html, $matches ) ) {
                    $meta_tags['title'] = trim( strip_tags( $matches[1] ) );
                }

                // Description
                if ( preg_match( '/<meta[^>]*name=["\']description["\'][^>]*content=["\']([^"\']*)["\'][^>]*>/i', $html, $matches ) ) {
                    $meta_tags['description'] = trim( $matches[1] );
                }

                // Keywords
                if ( preg_match( '/<meta[^>]*name=["\']keywords["\'][^>]*content=["\']([^"\']*)["\'][^>]*>/i', $html, $matches ) ) {
                    $meta_tags['keywords'] = trim( $matches[1] );
                }

                // Viewport
                if ( preg_match( '/<meta[^>]*name=["\']viewport["\'][^>]*>/i', $html ) ) {
                    $meta_tags['viewport'] = true;
                }
            }
        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur lors de l\'analyse des meta tags: ' . $e->getMessage() );
        }

        return $meta_tags;
    }

    /**
     * Analyse les en-têtes d'une URL.
     *
     * @since    1.1.0
     * @param    string    $url    URL à analyser.
     * @return   array             En-têtes trouvés.
     */
    private function analyze_headings( $url ) {
        $headings = array();

        try {
            $response = wp_remote_get( $url, array( 'timeout' => 15 ) );

            if ( ! is_wp_error( $response ) ) {
                $html = wp_remote_retrieve_body( $response );

                for ( $i = 1; $i <= 6; $i++ ) {
                    if ( preg_match_all( "/<h{$i}[^>]*>(.*?)<\/h{$i}>/is", $html, $matches ) ) {
                        $headings["h{$i}"] = array_map( function( $heading ) {
                            return trim( strip_tags( $heading ) );
                        }, $matches[1] );
                    }
                }
            }
        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur lors de l\'analyse des en-têtes: ' . $e->getMessage() );
        }

        return $headings;
    }

    /**
     * Analyse les images d'une URL.
     *
     * @since    1.1.0
     * @param    string    $url    URL à analyser.
     * @return   array             Informations sur les images.
     */
    private function analyze_images( $url ) {
        $images = array(
            'total' => 0,
            'without_alt' => 0,
            'large_images' => 0,
        );

        try {
            $response = wp_remote_get( $url, array( 'timeout' => 15 ) );

            if ( ! is_wp_error( $response ) ) {
                $html = wp_remote_retrieve_body( $response );

                if ( preg_match_all( '/<img[^>]*>/i', $html, $matches ) ) {
                    $images['total'] = count( $matches[0] );

                    foreach ( $matches[0] as $img_tag ) {
                        if ( ! preg_match( '/alt=["\'][^"\']*["\']/', $img_tag ) ) {
                            $images['without_alt']++;
                        }
                    }
                }
            }
        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur lors de l\'analyse des images: ' . $e->getMessage() );
        }

        return $images;
    }

    /**
     * Analyse les liens d'une URL.
     *
     * @since    1.1.0
     * @param    string    $url    URL à analyser.
     * @return   array             Informations sur les liens.
     */
    private function analyze_links( $url ) {
        $links = array(
            'internal' => 0,
            'external' => 0,
            'nofollow' => 0,
        );

        try {
            $response = wp_remote_get( $url, array( 'timeout' => 15 ) );

            if ( ! is_wp_error( $response ) ) {
                $html = wp_remote_retrieve_body( $response );
                $domain = parse_url( $url, PHP_URL_HOST );

                if ( preg_match_all( '/<a[^>]*href=["\']([^"\']*)["\'][^>]*>/i', $html, $matches ) ) {
                    foreach ( $matches[1] as $link ) {
                        $link_domain = parse_url( $link, PHP_URL_HOST );

                        if ( $link_domain === $domain || empty( $link_domain ) ) {
                            $links['internal']++;
                        } else {
                            $links['external']++;
                        }

                        if ( preg_match( '/rel=["\'][^"\']*nofollow[^"\']*["\']/', $matches[0][array_search( $link, $matches[1] )] ) ) {
                            $links['nofollow']++;
                        }
                    }
                }
            }
        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur lors de l\'analyse des liens: ' . $e->getMessage() );
        }

        return $links;
    }
}