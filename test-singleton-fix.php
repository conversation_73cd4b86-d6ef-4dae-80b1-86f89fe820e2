<?php
/**
 * <PERSON>ript de test spécifique pour vérifier la correction du problème de Singleton.
 * 
 * Ce script teste que l'erreur "Call to private Boss_Optimizer_Cache::__construct()" est corrigée.
 */

echo "🔧 TEST DE LA CORRECTION DU PROBLÈME SINGLETON\n";
echo "==============================================\n\n";

// Simuler l'environnement WordPress minimal
if (!function_exists('wp_cache_set')) {
    function wp_cache_set($key, $value, $group = '', $expiration = 0) { return true; }
}
if (!function_exists('wp_cache_get')) {
    function wp_cache_get($key, $group = '') { return false; }
}
if (!function_exists('wp_cache_delete')) {
    function wp_cache_delete($key, $group = '') { return true; }
}
if (!function_exists('wp_cache_flush_group')) {
    function wp_cache_flush_group($group) { return true; }
}
if (!function_exists('get_option')) {
    function get_option($option, $default = false) { return $default; }
}
if (!function_exists('update_option')) {
    function update_option($option, $value) { return true; }
}
if (!function_exists('error_log')) {
    function error_log($message) { echo "LOG: $message\n"; return true; }
}
if (!function_exists('get_post_status')) {
    function get_post_status($post_id) { return 'publish'; }
}
if (!function_exists('do_action')) {
    function do_action($hook, ...$args) { return true; }
}
if (!function_exists('add_action')) {
    function add_action($hook, $callback, $priority = 10, $args = 1) { return true; }
}

$tests_passed = 0;
$tests_failed = 0;

function test_result($test_name, $success, $message = '') {
    global $tests_passed, $tests_failed;
    
    if ($success) {
        echo "✅ {$test_name}\n";
        if ($message) echo "   → {$message}\n";
        $tests_passed++;
    } else {
        echo "❌ {$test_name}\n";
        if ($message) echo "   → {$message}\n";
        $tests_failed++;
    }
    echo "\n";
}

// Test 1: Charger les classes nécessaires
echo "📋 Test 1: Chargement des Classes\n";
echo "---------------------------------\n";

try {
    // Charger Boss_Optimizer_Cache
    if (file_exists('includes/class-boss-optimizer-cache.php')) {
        require_once 'includes/class-boss-optimizer-cache.php';
        test_result('Boss_Optimizer_Cache chargée', class_exists('Boss_Optimizer_Cache'));
    } else {
        test_result('Boss_Optimizer_Cache', false, 'Fichier non trouvé');
    }
    
    // Charger Boss_Cache_Manager
    if (file_exists('includes/class-boss-cache-manager.php')) {
        require_once 'includes/class-boss-cache-manager.php';
        test_result('Boss_Cache_Manager chargée', class_exists('Boss_Cache_Manager'));
    } else {
        test_result('Boss_Cache_Manager', false, 'Fichier non trouvé');
    }
    
} catch (Exception $e) {
    test_result('Chargement des classes', false, 'Erreur: ' . $e->getMessage());
}

// Test 2: Vérifier que Boss_Optimizer_Cache est bien un Singleton
echo "🔒 Test 2: Vérification du Singleton\n";
echo "------------------------------------\n";

if (class_exists('Boss_Optimizer_Cache')) {
    try {
        // Vérifier que get_instance() existe
        $has_get_instance = method_exists('Boss_Optimizer_Cache', 'get_instance');
        test_result('Méthode get_instance() existe', $has_get_instance);
        
        if ($has_get_instance) {
            // Tester l'instanciation via get_instance()
            $instance = Boss_Optimizer_Cache::get_instance();
            test_result('Instanciation via get_instance()', $instance instanceof Boss_Optimizer_Cache);
            
            // Vérifier que flush() existe
            $has_flush = method_exists($instance, 'flush');
            test_result('Méthode flush() existe', $has_flush);
        }
        
        // Tester que l'instanciation directe échoue (constructeur privé)
        try {
            $direct_instance = new Boss_Optimizer_Cache();
            test_result('Constructeur privé protégé', false, 'Instanciation directe réussie (ne devrait pas)');
        } catch (Error $e) {
            test_result('Constructeur privé protégé', true, 'Instanciation directe bloquée comme attendu');
        }
        
    } catch (Exception $e) {
        test_result('Test Singleton', false, 'Erreur: ' . $e->getMessage());
    }
}

// Test 3: Tester la correction dans Boss_Cache_Manager
echo "🛠️ Test 3: Correction dans Boss_Cache_Manager\n";
echo "----------------------------------------------\n";

if (class_exists('Boss_Cache_Manager')) {
    try {
        $cache_manager = Boss_Cache_Manager::get_instance();
        test_result('Cache Manager instancié', $cache_manager instanceof Boss_Cache_Manager);
        
        // Test de flush_module_cache avec le module optimizer
        echo "   🔍 Test du vidage du cache optimizer...\n";
        $result = $cache_manager->flush_module_cache('optimizer');
        test_result('Vidage cache optimizer', is_bool($result), 'Aucune erreur fatale générée');
        
        // Test avec un module inexistant
        echo "   🔍 Test avec un module inexistant...\n";
        $result = $cache_manager->flush_module_cache('nonexistent_module');
        test_result('Gestion module inexistant', $result === false, 'Retourne false comme attendu');
        
    } catch (Exception $e) {
        test_result('Test Cache Manager', false, 'Exception: ' . $e->getMessage());
    } catch (Error $e) {
        test_result('Test Cache Manager', false, 'Erreur fatale: ' . $e->getMessage());
    }
}

// Test 4: Simuler la sauvegarde d'un post (scénario de l'erreur originale)
echo "💾 Test 4: Simulation Sauvegarde Post\n";
echo "-------------------------------------\n";

if (class_exists('Boss_Cache_Manager')) {
    try {
        $cache_manager = Boss_Cache_Manager::get_instance();
        
        echo "   🔍 Simulation de invalidate_post_cache()...\n";
        $cache_manager->invalidate_post_cache(123);
        test_result('Invalidation cache post', true, 'Aucune erreur fatale lors de la sauvegarde');
        
    } catch (Exception $e) {
        test_result('Simulation sauvegarde post', false, 'Exception: ' . $e->getMessage());
    } catch (Error $e) {
        test_result('Simulation sauvegarde post', false, 'Erreur fatale: ' . $e->getMessage());
    }
}

// Test 5: Vérifier la gestion d'erreur avec ReflectionClass
echo "🔍 Test 5: Gestion d'Erreur Avancée\n";
echo "-----------------------------------\n";

if (class_exists('Boss_Cache_Manager')) {
    try {
        // Créer une classe de test avec constructeur privé mais sans get_instance
        eval('
        class Test_Private_Constructor_Cache {
            private function __construct() {}
            public function flush() { return true; }
        }
        ');
        
        $cache_manager = Boss_Cache_Manager::get_instance();
        
        // Tester avec cette classe problématique
        echo "   🔍 Test avec classe à constructeur privé sans get_instance()...\n";
        
        // Simuler le test en modifiant temporairement la liste des modules
        $reflection = new ReflectionClass($cache_manager);
        $property = $reflection->getProperty('cached_modules');
        $property->setAccessible(true);
        $original_modules = $property->getValue($cache_manager);
        $property->setValue($cache_manager, array('test_private_constructor'));
        
        $result = $cache_manager->flush_module_cache('test_private_constructor');
        test_result('Gestion constructeur privé sans get_instance', is_bool($result), 'Aucune erreur fatale');
        
        // Restaurer les modules originaux
        $property->setValue($cache_manager, $original_modules);
        
    } catch (Exception $e) {
        test_result('Test gestion erreur avancée', false, 'Exception: ' . $e->getMessage());
    } catch (Error $e) {
        test_result('Test gestion erreur avancée', false, 'Erreur fatale: ' . $e->getMessage());
    }
}

// Résumé final
echo "📊 RÉSUMÉ DES TESTS\n";
echo "==================\n";
echo "✅ Tests réussis: {$tests_passed}\n";
echo "❌ Tests échoués: {$tests_failed}\n";

$success_rate = $tests_passed / ($tests_passed + $tests_failed) * 100;
echo "📈 Taux de réussite: " . round($success_rate, 1) . "%\n\n";

if ($tests_failed === 0) {
    echo "🎉 CORRECTION DU SINGLETON RÉUSSIE !\n";
    echo "====================================\n";
    echo "✅ Boss_Optimizer_Cache utilise correctement get_instance()\n";
    echo "✅ Boss_Cache_Manager gère les Singletons intelligemment\n";
    echo "✅ Aucune erreur fatale lors du vidage de cache\n";
    echo "✅ Sauvegarde de posts fonctionne sans erreur\n";
    echo "✅ Gestion d'erreur robuste avec ReflectionClass\n\n";
    
    echo "🚀 LE PROBLÈME EST RÉSOLU !\n";
    echo "L'erreur 'Call to private Boss_Optimizer_Cache::__construct()' ne devrait plus se produire.\n\n";
    
    echo "📋 ACTIONS RECOMMANDÉES:\n";
    echo "1. Uploadez le fichier corrigé sur votre serveur\n";
    echo "2. Testez la sauvegarde d'un post dans WordPress\n";
    echo "3. Vérifiez les logs pour confirmer l'absence d'erreurs\n";
    echo "4. Testez le menu 'Boss SEO Cache' dans la barre d'administration\n";
    
} else {
    echo "⚠️ QUELQUES PROBLÈMES DÉTECTÉS\n";
    echo "==============================\n";
    echo "Certains tests ont échoué. Vérifiez les erreurs ci-dessus.\n";
    echo "La correction principale devrait néanmoins fonctionner.\n";
}

echo "\n🏁 FIN DES TESTS DE CORRECTION SINGLETON\n";
?>
