/**
 * Composant d'éditeur de texte riche moderne pour Boss SEO
 */
import { useState, useEffect, useRef } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  Button,
  ButtonGroup,
  Toolbar,
  ToolbarButton,
  Dashicon,
  Popover
} from '@wordpress/components';

/**
 * Éditeur de texte riche avec barre d'outils
 */
const RichTextEditor = ({
  label,
  value = '',
  onChange,
  placeholder = '',
  className = '',
  help = '',
  rows = 15
}) => {
  const [content, setContent] = useState(value);
  const [isToolbarVisible, setIsToolbarVisible] = useState(false);
  const [selectedText, setSelectedText] = useState('');
  const [showPreview, setShowPreview] = useState(false);
  const editorRef = useRef(null);
  const toolbarRef = useRef(null);

  // Synchroniser avec la valeur externe
  useEffect(() => {
    if (value !== content) {
      setContent(value);
    }
  }, [value]);

  // Notifier les changements
  const handleContentChange = (newContent) => {
    setContent(newContent);
    if (onChange) {
      onChange(newContent);
    }
  };

  // Insérer du formatage
  const insertFormatting = (before, after = '') => {
    const textarea = editorRef.current;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = textarea.value.substring(start, end);

    const newText =
      textarea.value.substring(0, start) +
      before + selectedText + after +
      textarea.value.substring(end);

    handleContentChange(newText);

    // Repositionner le curseur
    setTimeout(() => {
      textarea.focus();
      const newCursorPos = start + before.length + selectedText.length + after.length;
      textarea.setSelectionRange(newCursorPos, newCursorPos);
    }, 0);
  };

  // Insérer une liste
  const insertList = (type = 'ul') => {
    const textarea = editorRef.current;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const lines = textarea.value.substring(0, start).split('\n');
    const currentLine = lines[lines.length - 1];

    let listItem = '';
    if (type === 'ul') {
      listItem = '\n- ';
    } else {
      listItem = '\n1. ';
    }

    // Si on est déjà sur une ligne vide, commencer la liste
    if (currentLine.trim() === '') {
      insertFormatting(listItem.substring(1)); // Enlever le \n du début
    } else {
      insertFormatting(listItem);
    }
  };

  // Insérer un lien
  const insertLink = () => {
    const url = prompt(__('Entrez l\'URL du lien:', 'boss-seo'));
    if (url) {
      const textarea = editorRef.current;
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      const selectedText = textarea.value.substring(start, end);
      const linkText = selectedText || __('Texte du lien', 'boss-seo');

      insertFormatting(`[${linkText}](${url})`);
    }
  };

  // Insérer un titre
  const insertHeading = (level = 2) => {
    const prefix = '#'.repeat(level) + ' ';
    insertFormatting('\n' + prefix, '\n');
  };

  // Gérer la sélection de texte
  const handleTextSelection = () => {
    const textarea = editorRef.current;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;

    if (start !== end) {
      setSelectedText(textarea.value.substring(start, end));
      setIsToolbarVisible(true);
    } else {
      setIsToolbarVisible(false);
      setSelectedText('');
    }
  };

  // Fonction simple pour convertir le Markdown en HTML (basique)
  const renderMarkdown = (text) => {
    if (!text) return '';

    return text
      // Titres
      .replace(/^### (.*$)/gim, '<h3>$1</h3>')
      .replace(/^## (.*$)/gim, '<h2>$1</h2>')
      .replace(/^# (.*$)/gim, '<h1>$1</h1>')
      // Gras et italique
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      // Barré
      .replace(/~~(.*?)~~/g, '<del>$1</del>')
      // Liens
      .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank" rel="noopener">$1</a>')
      // Listes
      .replace(/^\- (.*$)/gim, '<li>$1</li>')
      .replace(/^(\d+)\. (.*$)/gim, '<li>$1. $2</li>')
      // Citations
      .replace(/^> (.*$)/gim, '<blockquote>$1</blockquote>')
      // Code
      .replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>')
      .replace(/`(.*?)`/g, '<code>$1</code>')
      // Sauts de ligne
      .replace(/\n/g, '<br>');
  };

  return (
    <div className={`boss-rich-text-editor ${className}`}>
      {label && (
        <label className="boss-block boss-text-sm boss-font-medium boss-text-gray-700 boss-mb-2">
          {label}
        </label>
      )}

      {/* Barre d'outils */}
      <div className="boss-border boss-border-gray-300 boss-rounded-t-md boss-bg-gray-50 boss-p-2">
        <div className="boss-flex boss-flex-wrap boss-gap-1">
          {/* Formatage de base */}
          <ButtonGroup>
            <Button
              isSmall
              onClick={() => insertFormatting('**', '**')}
              title={__('Gras', 'boss-seo')}
              className="boss-p-1"
            >
              <Dashicon icon="editor-bold" size={16} />
            </Button>
            <Button
              isSmall
              onClick={() => insertFormatting('*', '*')}
              title={__('Italique', 'boss-seo')}
              className="boss-p-1"
            >
              <Dashicon icon="editor-italic" size={16} />
            </Button>
            <Button
              isSmall
              onClick={() => insertFormatting('~~', '~~')}
              title={__('Barré', 'boss-seo')}
              className="boss-p-1"
            >
              <Dashicon icon="editor-strikethrough" size={16} />
            </Button>
          </ButtonGroup>

          {/* Titres */}
          <ButtonGroup>
            <Button
              isSmall
              onClick={() => insertHeading(2)}
              title={__('Titre H2', 'boss-seo')}
              className="boss-p-1"
            >
              H2
            </Button>
            <Button
              isSmall
              onClick={() => insertHeading(3)}
              title={__('Titre H3', 'boss-seo')}
              className="boss-p-1"
            >
              H3
            </Button>
            <Button
              isSmall
              onClick={() => insertHeading(4)}
              title={__('Titre H4', 'boss-seo')}
              className="boss-p-1"
            >
              H4
            </Button>
          </ButtonGroup>

          {/* Listes */}
          <ButtonGroup>
            <Button
              isSmall
              onClick={() => insertList('ul')}
              title={__('Liste à puces', 'boss-seo')}
              className="boss-p-1"
            >
              <Dashicon icon="editor-ul" size={16} />
            </Button>
            <Button
              isSmall
              onClick={() => insertList('ol')}
              title={__('Liste numérotée', 'boss-seo')}
              className="boss-p-1"
            >
              <Dashicon icon="editor-ol" size={16} />
            </Button>
          </ButtonGroup>

          {/* Liens et autres */}
          <ButtonGroup>
            <Button
              isSmall
              onClick={insertLink}
              title={__('Insérer un lien', 'boss-seo')}
              className="boss-p-1"
            >
              <Dashicon icon="admin-links" size={16} />
            </Button>
            <Button
              isSmall
              onClick={() => insertFormatting('\n> ', '\n')}
              title={__('Citation', 'boss-seo')}
              className="boss-p-1"
            >
              <Dashicon icon="editor-quote" size={16} />
            </Button>
            <Button
              isSmall
              onClick={() => insertFormatting('\n```\n', '\n```\n')}
              title={__('Code', 'boss-seo')}
              className="boss-p-1"
            >
              <Dashicon icon="editor-code" size={16} />
            </Button>
          </ButtonGroup>

          {/* Prévisualisation */}
          <ButtonGroup>
            <Button
              isSmall
              onClick={() => setShowPreview(!showPreview)}
              title={showPreview ? __('Masquer la prévisualisation', 'boss-seo') : __('Afficher la prévisualisation', 'boss-seo')}
              className={`boss-p-1 ${showPreview ? 'boss-bg-blue-100 boss-text-blue-700' : ''}`}
            >
              <Dashicon icon={showPreview ? "edit" : "visibility"} size={16} />
            </Button>
          </ButtonGroup>
        </div>
      </div>

      {/* Zone d'édition et prévisualisation */}
      <div className={`boss-grid ${showPreview ? 'boss-grid-cols-2 boss-gap-0' : 'boss-grid-cols-1'}`}>
        {/* Zone d'édition */}
        <div className={showPreview ? 'boss-border-r-0' : ''}>
          <textarea
            ref={editorRef}
            value={content}
            onChange={(e) => handleContentChange(e.target.value)}
            onSelect={handleTextSelection}
            onMouseUp={handleTextSelection}
            onKeyUp={handleTextSelection}
            placeholder={placeholder}
            rows={rows}
            className={`boss-w-full boss-border boss-border-t-0 boss-border-gray-300 ${showPreview ? 'boss-rounded-bl-md boss-border-r-0' : 'boss-rounded-b-md'} boss-p-3 boss-text-sm boss-font-mono boss-resize-y boss-min-h-[300px] focus:boss-ring-2 focus:boss-ring-blue-500 focus:boss-border-blue-500`}
            style={{
              fontFamily: 'ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace'
            }}
          />
        </div>

        {/* Zone de prévisualisation */}
        {showPreview && (
          <div className="boss-border boss-border-t-0 boss-border-l-0 boss-border-gray-300 boss-rounded-br-md boss-p-3 boss-bg-white boss-min-h-[300px] boss-overflow-y-auto">
            <div className="boss-text-sm boss-text-gray-600 boss-mb-2 boss-font-medium">
              {__('Prévisualisation', 'boss-seo')}
            </div>
            <div
              className="boss-prose boss-prose-sm boss-max-w-none"
              dangerouslySetInnerHTML={{ __html: renderMarkdown(content) }}
            />
          </div>
        )}
      </div>

      {/* Aide */}
      {help && (
        <p className="boss-mt-2 boss-text-sm boss-text-gray-600">
          {help}
        </p>
      )}

      {/* Compteur de caractères */}
      <div className="boss-mt-2 boss-flex boss-justify-between boss-text-xs boss-text-gray-500">
        <span>
          {__('Caractères:', 'boss-seo')} {content.length}
        </span>
        <span>
          {__('Mots:', 'boss-seo')} {content.trim() ? content.trim().split(/\s+/).length : 0}
        </span>
      </div>
    </div>
  );
};

export default RichTextEditor;
