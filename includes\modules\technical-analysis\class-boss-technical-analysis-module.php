<?php
/**
 * Module d'Analyse Technique Boss SEO - Interface Professionnelle à Onglets
 *
 * @link       https://boss-seo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/modules/technical-analysis
 */

/**
 * Module d'analyse technique avec interface professionnelle à onglets.
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/modules/technical-analysis
 * <AUTHOR> SEO Team
 */
class Boss_Technical_Analysis_Module {

    /**
     * Le nom du plugin.
     *
     * @since    1.2.0
     * @access   private
     * @var      string    $plugin_name    Le nom du plugin.
     */
    private $plugin_name;

    /**
     * La version du plugin.
     *
     * @since    1.2.0
     * @access   private
     * @var      string    $version    La version du plugin.
     */
    private $version;

    /**
     * Instance de l'analyseur technique.
     *
     * @since    1.2.0
     * @access   private
     * @var      Boss_Technical_Analyzer_V2    $analyzer    Instance de l'analyseur.
     */
    private $analyzer;

    /**
     * Initialise le module.
     *
     * @since    1.2.0
     * @param    string    $plugin_name       Le nom du plugin.
     * @param    string    $version          La version du plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
        
        // Charger l'analyseur technique
        if ( ! class_exists( 'Boss_Technical_Analyzer_V2' ) ) {
            require_once plugin_dir_path( dirname( dirname( __FILE__ ) ) ) . 'class-boss-technical-analyzer-v2.php';
        }
        
        try {
            $this->analyzer = new Boss_Technical_Analyzer_V2( $plugin_name, $version );
        } catch ( Exception $e ) {
            error_log( 'Boss SEO Technical Analysis Module: Erreur lors de l\'initialisation de l\'analyseur: ' . $e->getMessage() );
        }
    }

    /**
     * Enregistre les hooks WordPress.
     *
     * @since    1.2.0
     */
    public function register_hooks() {
        // Ajouter le menu d'administration
        add_action( 'admin_menu', array( $this, 'add_admin_menu' ) );
        
        // Enregistrer les assets
        add_action( 'admin_enqueue_scripts', array( $this, 'enqueue_admin_assets' ) );
        
        // Enregistrer les routes REST API
        add_action( 'rest_api_init', array( $this, 'register_rest_routes' ) );
        
        // Ajouter les logs de débogage
        add_action( 'init', array( $this, 'debug_log_init' ) );
    }

    /**
     * Ajoute le menu d'administration.
     *
     * @since    1.2.0
     */
    public function add_admin_menu() {
        add_submenu_page(
            'boss-seo',
            __( 'Analyse Technique', 'boss-seo' ),
            __( 'Analyse Technique', 'boss-seo' ),
            'manage_options',
            'boss-seo-technical-analysis',
            array( $this, 'render_admin_page' )
        );
    }

    /**
     * Enregistre les assets admin.
     *
     * @since    1.2.0
     * @param    string    $hook    Hook de la page admin.
     */
    public function enqueue_admin_assets( $hook ) {
        // Vérifier si nous sommes sur la bonne page
        if ( $hook !== 'boss-seo_page_boss-seo-technical-analysis' ) {
            return;
        }

        console_log( 'Boss SEO Technical Analysis: Chargement des assets pour la page: ' . $hook );

        // Enregistrer les styles
        wp_enqueue_style(
            'boss-technical-analysis-module',
            plugin_dir_url( __FILE__ ) . 'assets/css/technical-analysis.css',
            array(),
            $this->version,
            'all'
        );

        // Enregistrer les scripts
        wp_enqueue_script(
            'boss-technical-analysis-module',
            plugin_dir_url( __FILE__ ) . 'assets/js/technical-analysis.js',
            array( 'jquery', 'wp-api-fetch' ),
            $this->version,
            true
        );

        // Configuration JavaScript
        wp_localize_script(
            'boss-technical-analysis-module',
            'bossTechnicalAnalysis',
            array(
                'apiUrl'       => rest_url( 'boss-seo/v2/' ),
                'nonce'        => wp_create_nonce( 'wp_rest' ),
                'siteUrl'      => home_url(),
                'adminUrl'     => admin_url(),
                'pluginUrl'    => plugin_dir_url( dirname( dirname( dirname( __FILE__ ) ) ) ),
                'version'      => $this->version,
                'debug'        => defined( 'WP_DEBUG' ) && WP_DEBUG,
                'strings'      => array(
                    'loading'              => __( 'Chargement...', 'boss-seo' ),
                    'analyzing'            => __( 'Analyse en cours...', 'boss-seo' ),
                    'analyzeComplete'      => __( 'Analyse terminée', 'boss-seo' ),
                    'analyzeError'         => __( 'Erreur lors de l\'analyse', 'boss-seo' ),
                    'selectPage'           => __( 'Sélectionnez une page', 'boss-seo' ),
                    'noResults'            => __( 'Aucun résultat disponible', 'boss-seo' ),
                    'configureApi'         => __( 'Configurer l\'API', 'boss-seo' ),
                    'globalAnalysis'       => __( 'Analyse Globale', 'boss-seo' ),
                    'pagespeedTest'        => __( 'Test PageSpeed', 'boss-seo' ),
                    'aiSuggestions'        => __( 'Suggestions IA', 'boss-seo' ),
                    'technicalDetails'     => __( 'Détails Techniques', 'boss-seo' ),
                    'settingsUrl'          => admin_url( 'admin.php?page=boss-seo-settings&tab=external-services' ),
                ),
            )
        );

        console_log( 'Boss SEO Technical Analysis: Assets chargés avec succès' );
    }

    /**
     * Enregistre les routes REST API.
     *
     * @since    1.2.0
     */
    public function register_rest_routes() {
        if ( $this->analyzer ) {
            console_log( 'Boss SEO Technical Analysis: Enregistrement des routes REST API' );
            $this->analyzer->register_rest_routes();
        } else {
            console_log( 'Boss SEO Technical Analysis: ERREUR - Analyseur non disponible pour les routes REST' );
        }
    }

    /**
     * Log de débogage pour l'initialisation.
     *
     * @since    1.2.0
     */
    public function debug_log_init() {
        if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
            console_log( 'Boss SEO Technical Analysis Module: Initialisé avec succès' );
            console_log( 'Boss SEO Technical Analysis Module: Version ' . $this->version );
            console_log( 'Boss SEO Technical Analysis Module: Analyseur disponible: ' . ( $this->analyzer ? 'OUI' : 'NON' ) );
        }
    }

    /**
     * Rend la page d'administration.
     *
     * @since    1.2.0
     */
    public function render_admin_page() {
        console_log( 'Boss SEO Technical Analysis: Rendu de la page d\'administration' );
        
        // Inclure le template de la page
        include_once plugin_dir_path( __FILE__ ) . 'templates/admin-page.php';
    }

    /**
     * Vérifie si l'API PageSpeed est configurée.
     *
     * @since    1.2.0
     * @return   bool    True si l'API est configurée.
     */
    public function is_pagespeed_configured() {
        $external_services = get_option( 'boss_optimizer_external_services', array() );
        $api_key = isset( $external_services['google_pagespeed']['api_key'] ) ? $external_services['google_pagespeed']['api_key'] : '';
        
        if ( empty( $api_key ) ) {
            $api_key = get_option( 'boss_optimizer_pagespeed_api_key', '' );
        }
        
        return ! empty( $api_key );
    }

    /**
     * Vérifie si l'IA est configurée.
     *
     * @since    1.2.0
     * @return   bool    True si l'IA est configurée.
     */
    public function is_ai_configured() {
        $ai_settings = get_option( 'boss_seo_ai_settings', array() );
        
        return ! empty( $ai_settings['openai_api_key'] ) || 
               ! empty( $ai_settings['claude_api_key'] ) || 
               ! empty( $ai_settings['gemini_api_key'] );
    }
}

/**
 * Fonction helper pour les logs de console.
 *
 * @since    1.2.0
 * @param    mixed    $data    Données à logger.
 */
function console_log( $data ) {
    if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
        error_log( '[Boss SEO Technical Analysis] ' . ( is_string( $data ) ? $data : print_r( $data, true ) ) );
    }
}
