<?php
/**
 * Script de vérification de l'implémentation du cache Boss SEO.
 * 
 * Ce script vérifie que tous les fichiers sont créés et que la structure est correcte.
 */

echo "🔍 VÉRIFICATION DE L'IMPLÉMENTATION DU CACHE BOSS SEO\n";
echo "====================================================\n\n";

$checks_passed = 0;
$checks_failed = 0;

/**
 * Fonction helper pour vérifier les fichiers
 */
function check_file($file_path, $description) {
    global $checks_passed, $checks_failed;
    
    if (file_exists($file_path)) {
        echo "✅ {$description}\n";
        echo "   📁 {$file_path}\n";
        $checks_passed++;
        return true;
    } else {
        echo "❌ {$description}\n";
        echo "   📁 {$file_path} (MANQUANT)\n";
        $checks_failed++;
        return false;
    }
}

/**
 * Fonction helper pour vérifier le contenu des fichiers
 */
function check_file_content($file_path, $search_terms, $description) {
    global $checks_passed, $checks_failed;
    
    if (!file_exists($file_path)) {
        echo "❌ {$description} - Fichier manquant\n";
        $checks_failed++;
        return false;
    }
    
    $content = file_get_contents($file_path);
    $all_found = true;
    $missing_terms = [];
    
    foreach ($search_terms as $term) {
        if (strpos($content, $term) === false) {
            $all_found = false;
            $missing_terms[] = $term;
        }
    }
    
    if ($all_found) {
        echo "✅ {$description}\n";
        $checks_passed++;
        return true;
    } else {
        echo "❌ {$description}\n";
        echo "   🔍 Termes manquants: " . implode(', ', $missing_terms) . "\n";
        $checks_failed++;
        return false;
    }
}

echo "📂 1. VÉRIFICATION DES FICHIERS CRÉÉS\n";
echo "=====================================\n";

// Vérifier les fichiers principaux
$files_to_check = [
    'includes/class-boss-cache-manager.php' => 'Gestionnaire de cache principal',
    'includes/api/class-boss-cache-api.php' => 'API REST pour le cache',
    'includes/class-boss-cache-admin-bar.php' => 'Menu barre d\'administration',
    'src/components/cache/CacheManager.js' => 'Composant React de gestion du cache',
    'src/services/CacheService.js' => 'Service JavaScript pour le cache',
    'test-cache-system.php' => 'Script de test du système',
    'docs/cache-system-guide.md' => 'Documentation du système'
];

foreach ($files_to_check as $file => $description) {
    check_file($file, $description);
}

echo "\n📋 2. VÉRIFICATION DU CONTENU DES FICHIERS\n";
echo "==========================================\n";

// Vérifier le contenu du gestionnaire de cache
check_file_content(
    'includes/class-boss-cache-manager.php',
    [
        'class Boss_Cache_Manager',
        'get_instance()',
        'get_assets_version()',
        'flush_all_cache()',
        'force_assets_refresh()'
    ],
    'Gestionnaire de cache - Méthodes principales'
);

// Vérifier le contenu de l'API
check_file_content(
    'includes/api/class-boss-cache-api.php',
    [
        'class Boss_Cache_API',
        'register_routes()',
        '/boss-seo/v1/cache/flush-all',
        '/boss-seo/v1/cache/stats',
        'check_permissions()'
    ],
    'API REST - Routes et méthodes'
);

// Vérifier le contenu de la barre d'administration
check_file_content(
    'includes/class-boss-cache-admin-bar.php',
    [
        'class Boss_Cache_Admin_Bar',
        'add_cache_menu()',
        'handle_cache_actions()',
        'boss-seo-cache'
    ],
    'Barre d\'administration - Fonctionnalités'
);

// Vérifier le composant React
check_file_content(
    'src/components/cache/CacheManager.js',
    [
        'const CacheManager',
        'flushAllCache',
        'flushAssetsCache',
        'loadCacheStats',
        'apiFetch'
    ],
    'Composant React - Fonctionnalités'
);

// Vérifier le service JavaScript
check_file_content(
    'src/services/CacheService.js',
    [
        'class CacheService',
        'flushAllCache()',
        'flushModuleCache(',
        'getCacheStats()',
        'reloadPageWithNewCache('
    ],
    'Service JavaScript - Méthodes'
);

echo "\n🔗 3. VÉRIFICATION DES INTÉGRATIONS\n";
echo "===================================\n";

// Vérifier l'intégration dans le fichier principal
check_file_content(
    'includes/class-boss-seo.php',
    [
        'Boss_Cache_Manager',
        'Boss_Cache_API',
        'Boss_Cache_Admin_Bar',
        'cache_manager',
        'cache_api'
    ],
    'Intégration dans le fichier principal'
);

// Vérifier l'intégration dans l'admin
check_file_content(
    'admin/class-boss-seo-admin.php',
    [
        'Boss_Cache_Manager',
        'get_assets_version()',
        'cache_manager'
    ],
    'Intégration dans l\'administration'
);

echo "\n⚙️ 4. VÉRIFICATION DE LA STRUCTURE\n";
echo "==================================\n";

// Vérifier que les dossiers existent
$directories_to_check = [
    'includes/api' => 'Dossier API',
    'src/components/cache' => 'Dossier composants cache',
    'src/services' => 'Dossier services',
    'docs' => 'Dossier documentation'
];

foreach ($directories_to_check as $dir => $description) {
    if (is_dir($dir)) {
        echo "✅ {$description}\n";
        echo "   📁 {$dir}/\n";
        $checks_passed++;
    } else {
        echo "❌ {$description}\n";
        echo "   📁 {$dir}/ (MANQUANT)\n";
        $checks_failed++;
    }
}

echo "\n🧪 5. VÉRIFICATION DE LA SYNTAXE PHP\n";
echo "====================================\n";

$php_files = [
    'includes/class-boss-cache-manager.php',
    'includes/api/class-boss-cache-api.php',
    'includes/class-boss-cache-admin-bar.php'
];

foreach ($php_files as $file) {
    if (file_exists($file)) {
        // Vérification basique de la syntaxe
        $content = file_get_contents($file);
        
        // Vérifier les balises PHP
        $has_opening_tag = strpos($content, '<?php') !== false;
        $has_class = preg_match('/class\s+\w+/', $content);
        $has_closing_brace = substr_count($content, '{') === substr_count($content, '}');
        
        if ($has_opening_tag && $has_class && $has_closing_brace) {
            echo "✅ Syntaxe PHP - " . basename($file) . "\n";
            $checks_passed++;
        } else {
            echo "❌ Syntaxe PHP - " . basename($file) . "\n";
            if (!$has_opening_tag) echo "   🔍 Balise <?php manquante\n";
            if (!$has_class) echo "   🔍 Déclaration de classe manquante\n";
            if (!$has_closing_brace) echo "   🔍 Accolades non équilibrées\n";
            $checks_failed++;
        }
    }
}

echo "\n📊 RÉSUMÉ DE LA VÉRIFICATION\n";
echo "============================\n";
echo "✅ Vérifications réussies: {$checks_passed}\n";
echo "❌ Vérifications échouées: {$checks_failed}\n";

if ($checks_failed === 0) {
    echo "\n🎉 IMPLÉMENTATION COMPLÈTE ET CORRECTE !\n";
    echo "========================================\n";
    echo "✅ Tous les fichiers sont créés\n";
    echo "✅ Le contenu est correct\n";
    echo "✅ Les intégrations sont en place\n";
    echo "✅ La structure est valide\n";
    echo "✅ La syntaxe PHP est correcte\n\n";
    
    echo "🚀 PROCHAINES ÉTAPES:\n";
    echo "1. Activez le plugin Boss SEO\n";
    echo "2. Vérifiez la barre d'administration WordPress\n";
    echo "3. Testez le menu 'Boss SEO Cache'\n";
    echo "4. Vérifiez que les assets se rechargent\n";
    echo "5. Testez en mode debug (WP_DEBUG = true)\n\n";
    
    echo "📋 FONCTIONNALITÉS DISPONIBLES:\n";
    echo "• Menu rapide dans la barre d'administration\n";
    echo "• API REST pour les opérations de cache\n";
    echo "• Composant React pour l'interface avancée\n";
    echo "• Service JavaScript pour les développeurs\n";
    echo "• Cache busting intelligent automatique\n";
    echo "• Mode debug avec désactivation auto du cache\n";
    echo "• Intégration avec les plugins de cache tiers\n";
    
} else {
    echo "\n⚠️ IMPLÉMENTATION INCOMPLÈTE\n";
    echo "============================\n";
    echo "Certains fichiers ou fonctionnalités sont manquants.\n";
    echo "Veuillez corriger les problèmes signalés ci-dessus.\n";
}

echo "\n📖 DOCUMENTATION:\n";
echo "Consultez docs/cache-system-guide.md pour le guide complet d'utilisation.\n";

echo "\n🏁 FIN DE LA VÉRIFICATION\n";
?>
