<?php
/**
 * Script de test pour vérifier que tous les fichiers partials existent.
 * 
 * Ce script vérifie que tous les fichiers partials requis par l'administration sont présents.
 */

echo "🔍 VÉRIFICATION DES FICHIERS PARTIALS BOSS SEO\n";
echo "==============================================\n\n";

$tests_passed = 0;
$tests_failed = 0;

function test_result($test_name, $success, $message = '') {
    global $tests_passed, $tests_failed;
    
    if ($success) {
        echo "✅ {$test_name}\n";
        if ($message) echo "   → {$message}\n";
        $tests_passed++;
    } else {
        echo "❌ {$test_name}\n";
        if ($message) echo "   → {$message}\n";
        $tests_failed++;
    }
    echo "\n";
}

// Liste des fichiers partials requis (extraits du fichier admin)
$required_partials = array(
    'boss-seo-admin-dashboard.php' => 'Dashboard principal',
    'boss-seo-admin-optimizer.php' => 'Boss Optimizer (CORRIGÉ)',
    'boss-seo-admin-technical.php' => 'Analyse technique',
    'boss-seo-admin-schema.php' => 'Schémas structurés',
    'boss-seo-admin-analytics.php' => 'Analytics',
    'boss-seo-admin-local.php' => 'SEO Local',
    'boss-seo-admin-technical-management.php' => 'Gestion technique',
    'boss-seo-admin-reports.php' => 'Rapports',
    'boss-seo-admin-settings.php' => 'Paramètres',
    'boss-seo-admin-help.php' => 'Aide'
);

echo "📋 Test 1: Vérification de l'Existence des Fichiers\n";
echo "---------------------------------------------------\n";

foreach ($required_partials as $filename => $description) {
    $filepath = "admin/partials/{$filename}";
    $exists = file_exists($filepath);
    $size = $exists ? filesize($filepath) : 0;
    
    test_result(
        $description,
        $exists && $size > 0,
        $exists ? "Taille: {$size} octets" : "Fichier manquant"
    );
}

echo "📝 Test 2: Vérification du Contenu des Fichiers\n";
echo "-----------------------------------------------\n";

foreach ($required_partials as $filename => $description) {
    $filepath = "admin/partials/{$filename}";
    
    if (file_exists($filepath)) {
        $content = file_get_contents($filepath);
        
        // Vérifications basiques du contenu
        $has_php_tag = strpos($content, '<?php') !== false;
        $has_wrap_div = strpos($content, 'class="wrap"') !== false;
        $has_app_div = preg_match('/id="boss-seo-\w+-app"/', $content);
        $has_title = strpos($content, 'get_admin_page_title()') !== false;
        
        $content_valid = $has_php_tag && $has_wrap_div && $has_app_div && $has_title;
        
        test_result(
            "Contenu {$description}",
            $content_valid,
            $content_valid ? 'Structure correcte' : 'Structure incomplète'
        );
        
        // Vérification spéciale pour le fichier optimizer (nouvellement créé)
        if ($filename === 'boss-seo-admin-optimizer.php') {
            $has_optimizer_app = strpos($content, 'boss-seo-optimizer-app') !== false;
            test_result(
                "ID spécifique Optimizer",
                $has_optimizer_app,
                $has_optimizer_app ? 'ID "boss-seo-optimizer-app" présent' : 'ID manquant'
            );
        }
    }
}

echo "🔗 Test 3: Vérification des Inclusions dans l'Admin\n";
echo "---------------------------------------------------\n";

if (file_exists('admin/class-boss-seo-admin.php')) {
    $admin_content = file_get_contents('admin/class-boss-seo-admin.php');
    
    foreach ($required_partials as $filename => $description) {
        $include_pattern = "include_once 'partials/{$filename}'";
        $has_include = strpos($admin_content, $include_pattern) !== false;
        
        test_result(
            "Inclusion {$description}",
            $has_include,
            $has_include ? 'Include trouvé dans admin' : 'Include manquant'
        );
    }
} else {
    test_result(
        'Fichier admin principal',
        false,
        'admin/class-boss-seo-admin.php non trouvé'
    );
}

echo "⚙️ Test 4: Vérification de la Syntaxe PHP\n";
echo "-----------------------------------------\n";

foreach ($required_partials as $filename => $description) {
    $filepath = "admin/partials/{$filename}";
    
    if (file_exists($filepath)) {
        // Test de syntaxe basique
        $output = array();
        $return_code = 0;
        exec("php -l \"{$filepath}\" 2>&1", $output, $return_code);
        
        $syntax_ok = $return_code === 0;
        $message = $syntax_ok ? 'Syntaxe PHP valide' : 'Erreur de syntaxe: ' . implode(' ', $output);
        
        test_result(
            "Syntaxe {$description}",
            $syntax_ok,
            $message
        );
    }
}

echo "🎯 Test 5: Test de Chargement Simulé\n";
echo "------------------------------------\n";

// Simuler l'environnement WordPress minimal
if (!function_exists('esc_html')) {
    function esc_html($text) { return htmlspecialchars($text, ENT_QUOTES, 'UTF-8'); }
}
if (!function_exists('get_admin_page_title')) {
    function get_admin_page_title() { return 'Boss SEO - Page Test'; }
}

foreach ($required_partials as $filename => $description) {
    $filepath = "admin/partials/{$filename}";
    
    if (file_exists($filepath)) {
        try {
            // Capturer la sortie
            ob_start();
            include $filepath;
            $output = ob_get_clean();
            
            $has_output = !empty(trim($output));
            $has_html = strpos($output, '<div') !== false;
            
            test_result(
                "Chargement {$description}",
                $has_output && $has_html,
                $has_output ? 'Génère du HTML valide' : 'Aucune sortie HTML'
            );
            
        } catch (Exception $e) {
            test_result(
                "Chargement {$description}",
                false,
                'Erreur: ' . $e->getMessage()
            );
        } catch (Error $e) {
            test_result(
                "Chargement {$description}",
                false,
                'Erreur fatale: ' . $e->getMessage()
            );
        }
    }
}

// Résumé final
echo "📊 RÉSUMÉ DES TESTS\n";
echo "==================\n";
echo "✅ Tests réussis: {$tests_passed}\n";
echo "❌ Tests échoués: {$tests_failed}\n";

$success_rate = $tests_passed / ($tests_passed + $tests_failed) * 100;
echo "📈 Taux de réussite: " . round($success_rate, 1) . "%\n\n";

if ($tests_failed === 0) {
    echo "🎉 TOUS LES FICHIERS PARTIALS SONT CORRECTS !\n";
    echo "==============================================\n";
    echo "✅ Tous les fichiers partials existent\n";
    echo "✅ Le contenu est structuré correctement\n";
    echo "✅ Les inclusions sont présentes dans l'admin\n";
    echo "✅ La syntaxe PHP est valide\n";
    echo "✅ Les fichiers se chargent sans erreur\n\n";
    
    echo "🚀 PROBLÈME RÉSOLU !\n";
    echo "L'erreur 'Failed to open stream: No such file or directory' ne devrait plus se produire.\n\n";
    
    echo "📋 FICHIERS PARTIALS DISPONIBLES:\n";
    foreach ($required_partials as $filename => $description) {
        echo "• {$description} → admin/partials/{$filename}\n";
    }
    
} else {
    echo "⚠️ QUELQUES PROBLÈMES DÉTECTÉS\n";
    echo "==============================\n";
    echo "Certains tests ont échoué. Vérifiez les erreurs ci-dessus.\n";
    echo "Le fichier principal boss-seo-admin-optimizer.php a été créé et devrait fonctionner.\n";
}

echo "\n🏁 FIN DE LA VÉRIFICATION DES PARTIALS\n";
?>
