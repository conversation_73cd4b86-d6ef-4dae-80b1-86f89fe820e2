import { useState, useEffect } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  Card,
  CardBody,
  CardHeader,
  Button,
  Dashicon,
  Spinner,
  Notice,
  ProgressBar,
  ToggleControl,
  SelectControl,
  __experimentalSpacer as Spacer
} from '@wordpress/components';

// Composants du module
import ErrorCard from './ErrorCard';
import IAExplanation from './IAExplanation';
import CorrectionSuggestion from './CorrectionSuggestion';
import TodoPlan from './TodoPlan';

// Service API
import AuditIAService from './api';

const AuditIADashboard = () => {
  // États principaux
  const [isLoading, setIsLoading] = useState(true);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [error, setError] = useState(null);
  const [notice, setNotice] = useState(null);
  
  // États des données
  const [auditData, setAuditData] = useState({
    globalScore: 0,
    lastAudit: null,
    errors: {
      critical: [],
      medium: [],
      low: []
    },
    history: []
  });
  
  // États de configuration
  const [analysisMode, setAnalysisMode] = useState('novice');
  const [aiEnabled, setAiEnabled] = useState(true);
  const [selectedUrl, setSelectedUrl] = useState('');
  const [availablePages, setAvailablePages] = useState([]);
  
  // États de l'interface
  const [activeTab, setActiveTab] = useState('dashboard');
  const [expandedErrors, setExpandedErrors] = useState({});
  const [showExplanations, setShowExplanations] = useState({});
  
  // Service API
  const auditService = new AuditIAService();

  // Charger les données initiales
  useEffect(() => {
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Vérifier la configuration IA
      const aiConfig = await auditService.checkAIConfiguration();
      setAiEnabled(aiConfig.available);

      // Charger les pages disponibles
      const pages = await auditService.getAvailablePages();
      setAvailablePages(pages);
      
      if (pages.length > 0) {
        setSelectedUrl(pages[0].url);
      }

      // Charger l'historique des audits
      const history = await auditService.getAuditHistory();
      
      if (history.length > 0) {
        const lastAudit = history[0];
        setAuditData({
          globalScore: lastAudit.globalScore,
          lastAudit: lastAudit.date,
          errors: lastAudit.errors,
          history: history
        });
      }

    } catch (err) {
      console.error('Erreur lors du chargement des données:', err);
      setError(__('Erreur lors du chargement des données d\'audit.', 'boss-seo'));
    } finally {
      setIsLoading(false);
    }
  };

  // Lancer un nouvel audit
  const startNewAudit = async () => {
    if (!selectedUrl) {
      setNotice({
        type: 'warning',
        message: __('Veuillez sélectionner une page à analyser.', 'boss-seo')
      });
      return;
    }

    try {
      setIsAnalyzing(true);
      setError(null);
      setNotice({
        type: 'info',
        message: __('Analyse en cours... Cela peut prendre quelques minutes.', 'boss-seo')
      });

      // Lancer l'audit
      const auditResult = await auditService.performAudit(selectedUrl, {
        mode: analysisMode,
        useAI: aiEnabled
      });

      // Mettre à jour les données
      setAuditData({
        globalScore: auditResult.globalScore,
        lastAudit: new Date().toISOString(),
        errors: auditResult.errors,
        history: [auditResult, ...auditData.history]
      });

      setNotice({
        type: 'success',
        message: __('Audit terminé avec succès !', 'boss-seo')
      });

    } catch (err) {
      console.error('Erreur lors de l\'audit:', err);
      setError(__('Erreur lors de l\'audit SEO. Veuillez réessayer.', 'boss-seo'));
    } finally {
      setIsAnalyzing(false);
    }
  };

  // Obtenir la couleur du score
  const getScoreColor = (score) => {
    if (score >= 80) return 'boss-success';
    if (score >= 60) return 'boss-warning';
    return 'boss-error';
  };

  // Compter le total des erreurs
  const getTotalErrors = () => {
    return auditData.errors.critical.length + 
           auditData.errors.medium.length + 
           auditData.errors.low.length;
  };

  // Basculer l'expansion d'une erreur
  const toggleErrorExpansion = (errorId) => {
    setExpandedErrors(prev => ({
      ...prev,
      [errorId]: !prev[errorId]
    }));
  };

  // Basculer l'affichage d'une explication IA
  const toggleIAExplanation = (errorId) => {
    setShowExplanations(prev => ({
      ...prev,
      [errorId]: !prev[errorId]
    }));
  };

  if (isLoading) {
    return (
      <div className="boss-flex boss-justify-center boss-items-center boss-min-h-96">
        <div className="boss-text-center">
          <Spinner />
          <p className="boss-mt-4 boss-text-boss-gray">
            {__('Chargement du module d\'audit SEO...', 'boss-seo')}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="boss-flex boss-flex-col boss-min-h-screen">
      <div className="boss-p-6">
        {/* En-tête du module */}
        <div className="boss-mb-6">
          <h1 className="boss-text-2xl boss-font-bold boss-text-boss-dark boss-mb-2">
            {__('Audit SEO assisté par IA', 'boss-seo')}
          </h1>
          <p className="boss-text-boss-gray">
            {__('Analysez et optimisez votre SEO avec l\'intelligence artificielle', 'boss-seo')}
          </p>
        </div>

        {/* Notifications */}
        {notice && (
          <Notice
            status={notice.type}
            isDismissible={true}
            onRemove={() => setNotice(null)}
            className="boss-mb-4"
          >
            {notice.message}
          </Notice>
        )}

        {/* Message d'erreur */}
        {error && (
          <Notice
            status="error"
            isDismissible={true}
            onRemove={() => setError(null)}
            className="boss-mb-4"
          >
            {error}
          </Notice>
        )}

        {/* Alerte configuration IA */}
        {!aiEnabled && (
          <Notice
            status="warning"
            isDismissible={false}
            className="boss-mb-4"
          >
            <div className="boss-flex boss-items-center boss-justify-between">
              <span>
                {__('Les fonctionnalités IA ne sont pas disponibles. Configurez vos clés API dans les paramètres.', 'boss-seo')}
              </span>
              <Button
                isSecondary
                size="small"
                href="#settings"
              >
                {__('Configurer', 'boss-seo')}
              </Button>
            </div>
          </Notice>
        )}

        {/* Navigation par onglets */}
        <div className="boss-mb-6">
          <div className="boss-flex boss-border-b boss-border-gray-200">
            {[
              { id: 'dashboard', label: __('Tableau de bord', 'boss-seo'), icon: 'dashboard' },
              { id: 'errors', label: __('Erreurs détectées', 'boss-seo'), icon: 'warning' },
              { id: 'todo', label: __('Plan d\'action', 'boss-seo'), icon: 'list-view' },
              { id: 'history', label: __('Historique', 'boss-seo'), icon: 'backup' }
            ].map(tab => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`boss-flex boss-items-center boss-px-4 boss-py-2 boss-border-b-2 boss-transition-colors ${
                  activeTab === tab.id
                    ? 'boss-border-boss-primary boss-text-boss-primary'
                    : 'boss-border-transparent boss-text-boss-gray hover:boss-text-boss-dark'
                }`}
              >
                <Dashicon icon={tab.icon} className="boss-mr-2" />
                {tab.label}
              </button>
            ))}
          </div>
        </div>

        {/* Contenu des onglets */}
        {activeTab === 'dashboard' && (
          <div className="boss-space-y-6">
            {/* Configuration de l'audit */}
            <Card>
              <CardHeader>
                <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                  {__('Configuration de l\'audit', 'boss-seo')}
                </h2>
              </CardHeader>
              <CardBody>
                <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-3 boss-gap-4">
                  <SelectControl
                    label={__('Page à analyser', 'boss-seo')}
                    value={selectedUrl}
                    options={[
                      { label: __('Sélectionner une page...', 'boss-seo'), value: '' },
                      ...availablePages.map(page => ({
                        label: page.title,
                        value: page.url
                      }))
                    ]}
                    onChange={setSelectedUrl}
                  />
                  
                  <SelectControl
                    label={__('Mode d\'analyse', 'boss-seo')}
                    value={analysisMode}
                    options={[
                      { label: __('Novice (recommandé)', 'boss-seo'), value: 'novice' },
                      { label: __('Expert', 'boss-seo'), value: 'expert' }
                    ]}
                    onChange={setAnalysisMode}
                  />
                  
                  <div className="boss-flex boss-items-end">
                    <Button
                      isPrimary
                      onClick={startNewAudit}
                      disabled={isAnalyzing || !selectedUrl}
                      className="boss-w-full"
                    >
                      {isAnalyzing ? (
                        <>
                          <Spinner />
                          {__('Analyse...', 'boss-seo')}
                        </>
                      ) : (
                        <>
                          <Dashicon icon="search" className="boss-mr-1" />
                          {__('Lancer l\'audit', 'boss-seo')}
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </CardBody>
            </Card>

            {/* Résumé du score */}
            <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-4 boss-gap-6">
              <Card>
                <CardBody>
                  <div className="boss-text-center">
                    <div className={`boss-text-4xl boss-font-bold boss-mb-2 boss-text-${getScoreColor(auditData.globalScore)}`}>
                      {auditData.globalScore}/100
                    </div>
                    <div className="boss-text-sm boss-text-boss-gray">
                      {__('Score SEO global', 'boss-seo')}
                    </div>
                  </div>
                </CardBody>
              </Card>

              <Card>
                <CardBody>
                  <div className="boss-flex boss-items-center boss-justify-between">
                    <div>
                      <div className="boss-text-2xl boss-font-bold boss-text-boss-error">
                        {auditData.errors.critical.length}
                      </div>
                      <div className="boss-text-sm boss-text-boss-gray">
                        {__('Erreurs critiques', 'boss-seo')}
                      </div>
                    </div>
                    <Dashicon icon="warning" className="boss-text-boss-error boss-text-2xl" />
                  </div>
                </CardBody>
              </Card>

              <Card>
                <CardBody>
                  <div className="boss-flex boss-items-center boss-justify-between">
                    <div>
                      <div className="boss-text-2xl boss-font-bold boss-text-boss-warning">
                        {auditData.errors.medium.length}
                      </div>
                      <div className="boss-text-sm boss-text-boss-gray">
                        {__('Avertissements', 'boss-seo')}
                      </div>
                    </div>
                    <Dashicon icon="info" className="boss-text-boss-warning boss-text-2xl" />
                  </div>
                </CardBody>
              </Card>

              <Card>
                <CardBody>
                  <div className="boss-flex boss-items-center boss-justify-between">
                    <div>
                      <div className="boss-text-2xl boss-font-bold boss-text-boss-gray">
                        {auditData.errors.low.length}
                      </div>
                      <div className="boss-text-sm boss-text-boss-gray">
                        {__('Améliorations', 'boss-seo')}
                      </div>
                    </div>
                    <Dashicon icon="lightbulb" className="boss-text-boss-gray boss-text-2xl" />
                  </div>
                </CardBody>
              </Card>
            </div>

            {/* Dernier audit */}
            {auditData.lastAudit && (
              <Card>
                <CardBody>
                  <div className="boss-flex boss-items-center boss-justify-between">
                    <div>
                      <h3 className="boss-text-lg boss-font-semibold boss-mb-1">
                        {__('Dernier audit', 'boss-seo')}
                      </h3>
                      <p className="boss-text-boss-gray">
                        {new Date(auditData.lastAudit).toLocaleDateString('fr-FR', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric',
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </p>
                    </div>
                    <div className="boss-text-right">
                      <div className="boss-text-sm boss-text-boss-gray boss-mb-1">
                        {__('Total des problèmes', 'boss-seo')}
                      </div>
                      <div className="boss-text-2xl boss-font-bold">
                        {getTotalErrors()}
                      </div>
                    </div>
                  </div>
                </CardBody>
              </Card>
            )}
          </div>
        )}

        {/* Onglet Erreurs */}
        {activeTab === 'errors' && (
          <div className="boss-space-y-6">
            {/* Erreurs critiques */}
            {auditData.errors.critical.length > 0 && (
              <div>
                <h2 className="boss-text-xl boss-font-semibold boss-text-boss-error boss-mb-4">
                  <Dashicon icon="warning" className="boss-mr-2" />
                  {__('Erreurs critiques', 'boss-seo')} ({auditData.errors.critical.length})
                </h2>
                <div className="boss-space-y-4">
                  {auditData.errors.critical.map((error, index) => (
                    <ErrorCard
                      key={`critical-${index}`}
                      error={error}
                      severity="critical"
                      isExpanded={expandedErrors[`critical-${index}`]}
                      onToggleExpansion={() => toggleErrorExpansion(`critical-${index}`)}
                      showIAExplanation={showExplanations[`critical-${index}`]}
                      onToggleIAExplanation={() => toggleIAExplanation(`critical-${index}`)}
                      aiEnabled={aiEnabled}
                    />
                  ))}
                </div>
              </div>
            )}

            {/* Avertissements */}
            {auditData.errors.medium.length > 0 && (
              <div>
                <h2 className="boss-text-xl boss-font-semibold boss-text-boss-warning boss-mb-4">
                  <Dashicon icon="info" className="boss-mr-2" />
                  {__('Avertissements', 'boss-seo')} ({auditData.errors.medium.length})
                </h2>
                <div className="boss-space-y-4">
                  {auditData.errors.medium.map((error, index) => (
                    <ErrorCard
                      key={`medium-${index}`}
                      error={error}
                      severity="medium"
                      isExpanded={expandedErrors[`medium-${index}`]}
                      onToggleExpansion={() => toggleErrorExpansion(`medium-${index}`)}
                      showIAExplanation={showExplanations[`medium-${index}`]}
                      onToggleIAExplanation={() => toggleIAExplanation(`medium-${index}`)}
                      aiEnabled={aiEnabled}
                    />
                  ))}
                </div>
              </div>
            )}

            {/* Améliorations */}
            {auditData.errors.low.length > 0 && (
              <div>
                <h2 className="boss-text-xl boss-font-semibold boss-text-boss-gray boss-mb-4">
                  <Dashicon icon="lightbulb" className="boss-mr-2" />
                  {__('Améliorations suggérées', 'boss-seo')} ({auditData.errors.low.length})
                </h2>
                <div className="boss-space-y-4">
                  {auditData.errors.low.map((error, index) => (
                    <ErrorCard
                      key={`low-${index}`}
                      error={error}
                      severity="low"
                      isExpanded={expandedErrors[`low-${index}`]}
                      onToggleExpansion={() => toggleErrorExpansion(`low-${index}`)}
                      showIAExplanation={showExplanations[`low-${index}`]}
                      onToggleIAExplanation={() => toggleIAExplanation(`low-${index}`)}
                      aiEnabled={aiEnabled}
                    />
                  ))}
                </div>
              </div>
            )}

            {/* Aucune erreur */}
            {getTotalErrors() === 0 && (
              <Card>
                <CardBody>
                  <div className="boss-text-center boss-py-8">
                    <Dashicon icon="yes-alt" className="boss-text-boss-success boss-text-4xl boss-mb-4" />
                    <h3 className="boss-text-lg boss-font-semibold boss-mb-2">
                      {__('Aucun problème détecté !', 'boss-seo')}
                    </h3>
                    <p className="boss-text-boss-gray">
                      {__('Votre page semble bien optimisée pour le SEO.', 'boss-seo')}
                    </p>
                  </div>
                </CardBody>
              </Card>
            )}
          </div>
        )}

        {/* Onglet Plan d'action */}
        {activeTab === 'todo' && (
          <TodoPlan
            errors={auditData.errors}
            aiEnabled={aiEnabled}
          />
        )}

        {/* Onglet Historique */}
        {activeTab === 'history' && (
          <div className="boss-space-y-4">
            <h2 className="boss-text-xl boss-font-semibold boss-mb-4">
              {__('Historique des audits', 'boss-seo')}
            </h2>
            
            {auditData.history.length > 0 ? (
              <div className="boss-space-y-4">
                {auditData.history.map((audit, index) => (
                  <Card key={index}>
                    <CardBody>
                      <div className="boss-flex boss-items-center boss-justify-between">
                        <div>
                          <h3 className="boss-font-semibold boss-mb-1">
                            {audit.url || __('Page inconnue', 'boss-seo')}
                          </h3>
                          <p className="boss-text-sm boss-text-boss-gray">
                            {new Date(audit.date).toLocaleDateString('fr-FR', {
                              year: 'numeric',
                              month: 'long',
                              day: 'numeric',
                              hour: '2-digit',
                              minute: '2-digit'
                            })}
                          </p>
                        </div>
                        <div className="boss-text-right">
                          <div className={`boss-text-2xl boss-font-bold boss-text-${getScoreColor(audit.globalScore)}`}>
                            {audit.globalScore}/100
                          </div>
                          <div className="boss-text-sm boss-text-boss-gray">
                            {audit.errors.critical.length + audit.errors.medium.length + audit.errors.low.length} {__('problèmes', 'boss-seo')}
                          </div>
                        </div>
                      </div>
                    </CardBody>
                  </Card>
                ))}
              </div>
            ) : (
              <Card>
                <CardBody>
                  <div className="boss-text-center boss-py-8">
                    <Dashicon icon="backup" className="boss-text-boss-gray boss-text-4xl boss-mb-4" />
                    <h3 className="boss-text-lg boss-font-semibold boss-mb-2">
                      {__('Aucun audit dans l\'historique', 'boss-seo')}
                    </h3>
                    <p className="boss-text-boss-gray">
                      {__('Lancez votre premier audit pour commencer à suivre l\'évolution de votre SEO.', 'boss-seo')}
                    </p>
                  </div>
                </CardBody>
              </Card>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default AuditIADashboard;
