<?php
/**
 * The admin-specific functionality of the plugin.
 *
 * @link       https://bossseo.com
 * @since      1.1.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/admin
 */

/**
 * The admin-specific functionality of the plugin.
 *
 * Defines the plugin name, version, and two examples hooks for how to
 * enqueue the admin-specific stylesheet and JavaScript.
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/admin
 * <AUTHOR> SEO Team
 */
class Boss_SEO_Admin {

    /**
     * The ID of this plugin.
     *
     * @since    1.1.0
     * @access   private
     * @var      string    $plugin_name    The ID of this plugin.
     */
    private $plugin_name;

    /**
     * The version of this plugin.
     *
     * @since    1.1.0
     * @access   private
     * @var      string    $version    The current version of this plugin.
     */
    private $version;

    /**
     * Initialize the class and set its properties.
     *
     * @since    1.1.0
     * @param      string    $plugin_name       The name of this plugin.
     * @param      string    $version    The version of this plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
    }

    /**
     * Register the stylesheets for the admin area.
     *
     * @since    1.1.0
     */
    public function enqueue_styles() {
        wp_enqueue_style( $this->plugin_name, plugin_dir_url( __FILE__ ) . 'css/boss-seo-admin.css', array(), $this->version, 'all' );
    }

    /**
     * Register the JavaScript for the admin area.
     *
     * @since    1.1.0
     */
    public function enqueue_scripts() {
        // Scripts de base
        wp_enqueue_script( $this->plugin_name, plugin_dir_url( __FILE__ ) . 'js/boss-seo-admin.js', array( 'jquery' ), $this->version, false );

        // Enqueue des scripts WordPress natifs
        wp_enqueue_script( 'wp-element' );
        wp_enqueue_script( 'wp-components' );
        wp_enqueue_script( 'wp-i18n' );
        wp_enqueue_script( 'wp-api-fetch' );

        // Styles des composants WordPress
        wp_enqueue_style( 'wp-components' );

        // Charger le gestionnaire de cache
        if ( ! class_exists( 'Boss_Cache_Manager' ) ) {
            require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-boss-cache-manager.php';
        }
        $cache_manager = Boss_Cache_Manager::get_instance();
        $assets_version = $cache_manager->get_assets_version();

        // Enqueue le script principal du dashboard (compilé) avec versioning intelligent
        wp_enqueue_script( 'boss-seo-dashboard', plugin_dir_url( __FILE__ ) . '../assets/js/dashboard.js', array( 'wp-element', 'wp-components', 'wp-i18n' ), $assets_version, true );
        wp_enqueue_style( 'boss-seo-dashboard-style', plugin_dir_url( __FILE__ ) . '../assets/css/dashboard.css', array( 'wp-components' ), $assets_version, 'all' );

        // Localisation des données pour le script
        wp_localize_script( 'boss-seo-dashboard', 'bossSeoData', array(
            'nonce' => wp_create_nonce( 'boss_seo_nonce' ),
            'ajaxUrl' => admin_url( 'admin-ajax.php' ),
            'siteUrl' => get_site_url(),
            'pluginUrl' => BOSS_SEO_PLUGIN_URL,
            'adminUrl' => admin_url( 'admin.php' ),
            'restUrl' => get_rest_url(),
        ));
    }

    /**
     * Add menu items to the WordPress admin menu.
     *
     * @since    1.1.0
     */
    public function add_plugin_admin_menu() {
        // Menu principal
        add_menu_page(
            __( 'Boss SEO', 'boss-seo' ),
            __( 'Boss SEO', 'boss-seo' ),
            'manage_options',
            'boss-seo',
            array( $this, 'display_plugin_dashboard_page' ),
            'dashicons-chart-area',
            81
        );

        // Sous-menus
        add_submenu_page(
            'boss-seo',
            __( 'Tableau de bord', 'boss-seo' ),
            __( 'Tableau de bord', 'boss-seo' ),
            'manage_options',
            'boss-seo',
            array( $this, 'display_plugin_dashboard_page' )
        );

        add_submenu_page(
            'boss-seo',
            __( 'Boss Optimizer', 'boss-seo' ),
            __( 'Boss Optimizer', 'boss-seo' ),
            'manage_options',
            'boss-seo-optimizer',
            array( $this, 'display_plugin_optimizer_page' )
        );

        // Menu Analyse technique supprimé - Maintenant géré par le module dédié
        // Menu Optimisation de contenu supprimé - Interface multistep accessible via WordPress admin uniquement

        add_submenu_page(
            'boss-seo',
            __( 'Schémas structurés', 'boss-seo' ),
            __( 'Schémas structurés', 'boss-seo' ),
            'manage_options',
            'boss-seo-schema',
            array( $this, 'display_plugin_schema_page' )
        );

        add_submenu_page(
            'boss-seo',
            __( 'Intégrations analytics', 'boss-seo' ),
            __( 'Intégrations analytics', 'boss-seo' ),
            'manage_options',
            'boss-seo-analytics',
            array( $this, 'display_plugin_analytics_page' )
        );

        add_submenu_page(
            'boss-seo',
            __( 'SEO local & e-commerce', 'boss-seo' ),
            __( 'SEO local & e-commerce', 'boss-seo' ),
            'manage_options',
            'boss-seo-local',
            array( $this, 'display_plugin_local_page' )
        );

        add_submenu_page(
            'boss-seo',
            __( 'Gestion technique', 'boss-seo' ),
            __( 'Gestion technique', 'boss-seo' ),
            'manage_options',
            'boss-seo-technical-management',
            array( $this, 'display_plugin_technical_management_page' )
        );

        add_submenu_page(
            'boss-seo',
            __( 'Rapports', 'boss-seo' ),
            __( 'Rapports', 'boss-seo' ),
            'manage_options',
            'boss-seo-reports',
            array( $this, 'display_plugin_reports_page' )
        );

        add_submenu_page(
            'boss-seo',
            __( 'Paramètres', 'boss-seo' ),
            __( 'Paramètres', 'boss-seo' ),
            'manage_options',
            'boss-seo-settings',
            array( $this, 'display_plugin_settings_page' )
        );

        add_submenu_page(
            'boss-seo',
            __( 'Aide & Documentation', 'boss-seo' ),
            __( 'Aide & Documentation', 'boss-seo' ),
            'manage_options',
            'boss-seo-help',
            array( $this, 'display_plugin_help_page' )
        );

        // Pages de debug temporaires (seulement en mode debug)
        if ( defined( 'WP_DEBUG' ) && WP_DEBUG && current_user_can( 'manage_options' ) ) {
            add_submenu_page(
                'boss-seo',
                __( '🔍 Debug Métadonnées', 'boss-seo' ),
                __( '🔍 Debug Métadonnées', 'boss-seo' ),
                'manage_options',
                'debug-metadata',
                array( $this, 'display_debug_metadata_page' )
            );

            add_submenu_page(
                'boss-seo',
                __( '🔍 Debug PageSpeed', 'boss-seo' ),
                __( '🔍 Debug PageSpeed', 'boss-seo' ),
                'manage_options',
                'debug-pagespeed',
                array( $this, 'display_debug_pagespeed_page' )
            );

            add_submenu_page(
                'boss-seo',
                __( '🔍 Debug Optimisation', 'boss-seo' ),
                __( '🔍 Debug Optimisation', 'boss-seo' ),
                'manage_options',
                'debug-content-optimization',
                array( $this, 'display_debug_content_optimization_page' )
            );

            add_submenu_page(
                'boss-seo',
                __( '🔍 Debug Metaboxes', 'boss-seo' ),
                __( '🔍 Debug Metaboxes', 'boss-seo' ),
                'manage_options',
                'debug-metaboxes',
                array( $this, 'display_debug_metaboxes_page' )
            );

            // Page de test Analytics (temporaire)
            add_submenu_page(
                'boss-seo',
                __( '🧪 Test Analytics', 'boss-seo' ),
                __( '🧪 Test Analytics', 'boss-seo' ),
                'manage_options',
                'test-analytics-backend',
                array( $this, 'display_test_analytics_page' )
            );

            // Page de nettoyage du cache (temporaire)
            add_submenu_page(
                'boss-seo',
                __( '🧹 Vider Cache', 'boss-seo' ),
                __( '🧹 Vider Cache', 'boss-seo' ),
                'manage_options',
                'clear-all-cache',
                array( $this, 'display_clear_cache_page' )
            );

            // Script d'urgence pour les problèmes de cache
            add_submenu_page(
                'boss-seo',
                __( '🚨 Urgence Cache', 'boss-seo' ),
                __( '🚨 Urgence Cache', 'boss-seo' ),
                'manage_options',
                'emergency-cache-clear',
                array( $this, 'display_emergency_cache_page' )
            );
        }
    }

    /**
     * Render the dashboard page.
     *
     * @since    1.1.0
     */
    public function display_plugin_dashboard_page() {
        include_once 'partials/boss-seo-admin-dashboard.php';
    }

    /**
     * Render the optimizer page.
     *
     * @since    1.1.0
     */
    public function display_plugin_optimizer_page() {
        include_once 'partials/boss-seo-admin-optimizer.php';
    }

    // Méthode display_plugin_technical_page supprimée - Module dédié maintenant
    // Méthode display_plugin_content_page supprimée - Interface multistep accessible via WordPress admin uniquement

    /**
     * Render the schema page.
     *
     * @since    1.1.0
     */
    public function display_plugin_schema_page() {
        include_once 'partials/boss-seo-admin-schema.php';
    }

    /**
     * Render the analytics page.
     *
     * @since    1.1.0
     */
    public function display_plugin_analytics_page() {
        include_once 'partials/boss-seo-admin-analytics.php';
    }

    /**
     * Render the local SEO page.
     *
     * @since    1.1.0
     */
    public function display_plugin_local_page() {
        include_once 'partials/boss-seo-admin-local.php';
    }

    /**
     * Render the technical management page.
     *
     * @since    1.1.0
     */
    public function display_plugin_technical_management_page() {
        include_once 'partials/boss-seo-admin-technical-management.php';
    }

    /**
     * Render the reports page.
     *
     * @since    1.1.0
     */
    public function display_plugin_reports_page() {
        include_once 'partials/boss-seo-admin-reports.php';
    }

    /**
     * Render the settings page.
     *
     * @since    1.1.0
     */
    public function display_plugin_settings_page() {
        include_once 'partials/boss-seo-admin-settings.php';
    }

    /**
     * Render the help page.
     *
     * @since    1.1.0
     */
    public function display_plugin_help_page() {
        include_once 'partials/boss-seo-admin-help.php';
    }

    /**
     * Render the debug metadata page.
     *
     * @since    1.2.0
     */
    public function display_debug_metadata_page() {
        include_once plugin_dir_path( dirname( __FILE__ ) ) . 'debug-metadata.php';
    }

    /**
     * Render the debug PageSpeed page.
     *
     * @since    1.2.0
     */
    public function display_debug_pagespeed_page() {
        include_once plugin_dir_path( dirname( __FILE__ ) ) . 'debug-pagespeed.php';
    }

    /**
     * Render the debug content optimization page.
     *
     * @since    1.2.0
     */
    public function display_debug_content_optimization_page() {
        include_once plugin_dir_path( dirname( __FILE__ ) ) . 'debug-content-optimization.php';
    }

    /**
     * Render the debug metaboxes page.
     *
     * @since    1.2.0
     */
    public function display_debug_metaboxes_page() {
        include_once plugin_dir_path( dirname( __FILE__ ) ) . 'debug-metaboxes.php';
    }

    /**
     * Render the test analytics page.
     *
     * @since    1.2.0
     */
    public function display_test_analytics_page() {
        include_once plugin_dir_path( dirname( __FILE__ ) ) . 'test-analytics-backend.php';
    }

    /**
     * Render the clear cache page.
     *
     * @since    1.2.0
     */
    public function display_clear_cache_page() {
        include_once plugin_dir_path( dirname( __FILE__ ) ) . 'clear-all-cache.php';
    }

    /**
     * Render the emergency cache clear page.
     *
     * @since    1.2.0
     */
    public function display_emergency_cache_page() {
        include_once plugin_dir_path( dirname( __FILE__ ) ) . 'emergency-cache-clear.php';
    }
}
