import { __ } from '@wordpress/i18n';
import { Button, Dashicon } from '@wordpress/components';

// Icônes (utilisation des dashicons de WordPress)
const icons = {
  dashboard: 'dashboard',
  optimizer: 'performance',
  technical: 'code-standards',
  audit_ia: 'search',
  content: 'edit',
  schema: 'networking',
  analytics: 'chart-bar',
  local: 'store',
  technical_management: 'admin-tools',
  reports: 'clipboard',
  settings: 'admin-settings',
  notifications: 'bell',
};

const Sidebar = ({ currentPage, setCurrentPage }) => {
  // Liste des éléments du menu
  const menuItems = [
    {
      id: 'dashboard',
      label: __('Tableau de bord', 'boss-seo'),
      isPrimary: true
    },
    {
      id: 'optimizer',
      label: __('Boss Optimizer', 'boss-seo'),
      isPrimary: true,
      badge: 'NEW'
    },
    {
      id: 'technical',
      label: __('Analyse technique', 'boss-seo'),
      isPrimary: true
    },
    {
      id: 'audit_ia',
      label: __('Audit SEO assisté par IA', 'boss-seo'),
      isPrimary: true,
      badge: 'IA'
    },
    // Interface 'content' supprimée - Dupliquée, accessible via WordPress admin uniquement
    {
      id: 'schema',
      label: __('Schémas structurés', 'boss-seo'),
      isPrimary: true
    },
    {
      id: 'analytics',
      label: __('Analytics', 'boss-seo'),
      isPrimary: true
    },
    {
      id: 'local',
      label: __('SEO local & e-commerce', 'boss-seo'),
      isPrimary: true
    },
    {
      id: 'technical_management',
      label: __('Gestion technique', 'boss-seo'),
      isPrimary: true
    },
    {
      id: 'reports',
      label: __('Rapports', 'boss-seo'),
      isPrimary: false
    },
    {
      id: 'settings',
      label: __('Paramètres', 'boss-seo'),
      isPrimary: false
    },
  ];

  return (
    <div className="boss-w-64 boss-bg-white boss-border-r boss-border-gray-200 boss-flex boss-flex-col">
      {/* Logo et titre */}
      <div className="boss-p-6 boss-border-b boss-border-gray-200">
        <h2 className="boss-text-xl boss-font-bold boss-text-boss-primary boss-flex boss-items-center">
          <Dashicon icon="chart-area" className="boss-mr-2" />
          {__('Boss SEO', 'boss-seo')}
        </h2>
      </div>

      {/* Menu de navigation */}
      <nav className="boss-flex-1 boss-p-4 boss-space-y-1 boss-overflow-y-auto">
        {/* Éléments primaires */}
        <div className="boss-mb-6">
          {menuItems
            .filter(item => item.isPrimary)
            .map((item, index) => (
              <Button
                key={item.id}
                className={`boss-sidebar-item boss-w-full boss-justify-start boss-mb-1 ${currentPage === item.id ? 'boss-active' : ''}`}
                onClick={() => setCurrentPage(item.id)}
              >
                <Dashicon icon={icons[item.id]} className="boss-mr-3" />
                <span className="boss-flex-1">{item.label}</span>
                {item.badge && (
                  <span className="boss-bg-boss-primary boss-text-white boss-text-xs boss-px-2 boss-py-1 boss-rounded-full boss-ml-2">
                    {item.badge}
                  </span>
                )}
              </Button>
            ))}
        </div>

        {/* Séparateur */}
        <div className="boss-border-t boss-border-gray-200 boss-my-2"></div>

        {/* Éléments secondaires */}
        <div>
          {menuItems
            .filter(item => !item.isPrimary)
            .map((item, index) => (
              <Button
                key={item.id}
                className={`boss-sidebar-item boss-w-full boss-justify-start boss-mb-1 ${currentPage === item.id ? 'boss-active' : ''}`}
                onClick={() => setCurrentPage(item.id)}
              >
                <Dashicon icon={icons[item.id]} className="boss-mr-3" />
                <span>{item.label}</span>
              </Button>
            ))}
        </div>
      </nav>

      {/* Footer de la sidebar */}
      <div className="boss-p-4 boss-border-t boss-border-gray-200">
        <div className="boss-flex boss-justify-between boss-items-center">
          <div className="boss-text-xs boss-text-boss-gray">
            <p>{__('Boss SEO v1.1.0', 'boss-seo')}</p>
          </div>
          <Button
            className="boss-text-boss-gray boss-hover:text-boss-primary"
            onClick={() => setCurrentPage('settings')}
          >
            <Dashicon icon="admin-settings" />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
