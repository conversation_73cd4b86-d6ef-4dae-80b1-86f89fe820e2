<?php
/**
 * Script de diagnostic pour le module d'optimisation de contenu
 * À placer dans le dossier du plugin Boss SEO
 * 
 * Usage: wp-admin/admin.php?page=debug-content-optimization
 */

// Sécurité WordPress
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Vérifier les permissions
if ( ! current_user_can( 'manage_options' ) ) {
    wp_die( 'Accès refusé' );
}

echo '<div class="wrap">';
echo '<h1>🔍 Diagnostic Module d\'Optimisation de Contenu</h1>';

echo '<div class="notice notice-info"><p><strong>ℹ️ Ce diagnostic va tester le module d\'optimisation de contenu Boss SEO</strong></p></div>';

// Section 1: Configuration JavaScript
echo '<h2>📋 1. Configuration JavaScript</h2>';
echo '<div id="js-test-container">';
echo '<p>Test de chargement des composants React...</p>';
echo '</div>';

// Section 2: Test des APIs
echo '<h2>🧪 2. Test des APIs</h2>';

// Test de l'API de recherche de mots-clés
echo '<h3>API de recherche de mots-clés</h3>';
echo '<div id="keyword-api-test">';
echo '<button id="test-keyword-api" class="button button-primary">Tester l\'API de mots-clés</button>';
echo '<div id="keyword-api-result" style="margin-top: 10px;"></div>';
echo '</div>';

// Section 3: Test du store de données
echo '<h2>💾 3. Test du Store de Données</h2>';
echo '<div id="store-test">';
echo '<button id="test-store" class="button button-primary">Tester le Store</button>';
echo '<div id="store-result" style="margin-top: 10px;"></div>';
echo '</div>';

// Section 4: Simulation d'événements
echo '<h2>🖱️ 4. Simulation d\'Événements</h2>';
echo '<div id="event-test">';
echo '<button id="test-events" class="button button-primary">Tester les Événements</button>';
echo '<div id="event-result" style="margin-top: 10px;"></div>';
echo '</div>';

// Section 5: Console de debug
echo '<h2>📊 5. Console de Debug</h2>';
echo '<div id="debug-console" style="background: #f0f0f0; padding: 15px; border: 1px solid #ccc; height: 300px; overflow-y: auto; font-family: monospace; font-size: 12px;">';
echo '<p><strong>Console de debug en temps réel:</strong></p>';
echo '</div>';

echo '</div>';

// JavaScript pour les tests
?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const debugConsole = document.getElementById('debug-console');
    
    // Fonction pour logger dans la console de debug
    function debugLog(message, type = 'info') {
        const timestamp = new Date().toLocaleTimeString();
        const colors = {
            info: '#333',
            success: '#28a745',
            error: '#dc3545',
            warning: '#ffc107'
        };
        
        const logEntry = document.createElement('div');
        logEntry.style.color = colors[type] || colors.info;
        logEntry.style.marginBottom = '5px';
        logEntry.innerHTML = `[${timestamp}] ${message}`;
        
        debugConsole.appendChild(logEntry);
        debugConsole.scrollTop = debugConsole.scrollHeight;
    }
    
    // Intercepter les logs de la console
    const originalLog = console.log;
    const originalError = console.error;
    const originalWarn = console.warn;
    
    console.log = function(...args) {
        debugLog(args.join(' '), 'info');
        originalLog.apply(console, args);
    };
    
    console.error = function(...args) {
        debugLog('ERROR: ' + args.join(' '), 'error');
        originalError.apply(console, args);
    };
    
    console.warn = function(...args) {
        debugLog('WARNING: ' + args.join(' '), 'warning');
        originalWarn.apply(console, args);
    };
    
    debugLog('🚀 Diagnostic du module d\'optimisation de contenu démarré');
    
    // Test 1: Vérifier si React est chargé
    if (typeof React !== 'undefined') {
        debugLog('✅ React est chargé (version: ' + React.version + ')', 'success');
    } else {
        debugLog('❌ React n\'est pas chargé', 'error');
    }
    
    // Test 2: Vérifier si WordPress components sont disponibles
    if (typeof wp !== 'undefined' && wp.element) {
        debugLog('✅ WordPress components sont disponibles', 'success');
    } else {
        debugLog('❌ WordPress components ne sont pas disponibles', 'error');
    }
    
    // Test 3: Vérifier si Boss SEO est chargé
    if (typeof window.bossOptimizer !== 'undefined') {
        debugLog('✅ Boss SEO Optimizer est chargé', 'success');
    } else {
        debugLog('❌ Boss SEO Optimizer n\'est pas chargé', 'error');
    }
    
    // Test de l'API de mots-clés
    document.getElementById('test-keyword-api').addEventListener('click', function() {
        debugLog('🔍 Test de l\'API de mots-clés...');
        
        const testData = {
            action: 'boss_seo_search_keywords',
            nonce: '<?php echo wp_create_nonce( "boss_seo_nonce" ); ?>',
            query: 'test',
            language: 'fr',
            country: 'FR'
        };
        
        fetch('<?php echo admin_url( "admin-ajax.php" ); ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams(testData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                debugLog('✅ API de mots-clés fonctionne: ' + data.data.length + ' résultats', 'success');
                document.getElementById('keyword-api-result').innerHTML = 
                    '<div style="background: #d4edda; padding: 10px; border-radius: 4px;">✅ API fonctionnelle - ' + 
                    data.data.length + ' mots-clés trouvés</div>';
            } else {
                debugLog('❌ Erreur API mots-clés: ' + (data.data || 'Erreur inconnue'), 'error');
                document.getElementById('keyword-api-result').innerHTML = 
                    '<div style="background: #f8d7da; padding: 10px; border-radius: 4px;">❌ Erreur: ' + 
                    (data.data || 'Erreur inconnue') + '</div>';
            }
        })
        .catch(error => {
            debugLog('❌ Erreur réseau API mots-clés: ' + error.message, 'error');
            document.getElementById('keyword-api-result').innerHTML = 
                '<div style="background: #f8d7da; padding: 10px; border-radius: 4px;">❌ Erreur réseau: ' + 
                error.message + '</div>';
        });
    });
    
    // Test du store
    document.getElementById('test-store').addEventListener('click', function() {
        debugLog('💾 Test du store de données...');
        
        // Simuler des données de test
        const testStoreData = {
            keywords: {
                main: 'test principal',
                secondary: ['test1', 'test2', 'test3']
            }
        };
        
        try {
            // Tester localStorage
            localStorage.setItem('boss_seo_test', JSON.stringify(testStoreData));
            const retrieved = JSON.parse(localStorage.getItem('boss_seo_test'));
            
            if (retrieved && retrieved.keywords.main === 'test principal') {
                debugLog('✅ LocalStorage fonctionne correctement', 'success');
                document.getElementById('store-result').innerHTML = 
                    '<div style="background: #d4edda; padding: 10px; border-radius: 4px;">✅ Store fonctionnel</div>';
            } else {
                debugLog('❌ Problème avec localStorage', 'error');
                document.getElementById('store-result').innerHTML = 
                    '<div style="background: #f8d7da; padding: 10px; border-radius: 4px;">❌ Problème de store</div>';
            }
            
            // Nettoyer
            localStorage.removeItem('boss_seo_test');
            
        } catch (error) {
            debugLog('❌ Erreur store: ' + error.message, 'error');
            document.getElementById('store-result').innerHTML = 
                '<div style="background: #f8d7da; padding: 10px; border-radius: 4px;">❌ Erreur: ' + 
                error.message + '</div>';
        }
    });
    
    // Test des événements
    document.getElementById('test-events').addEventListener('click', function() {
        debugLog('🖱️ Test des événements...');
        
        // Créer un élément de test
        const testElement = document.createElement('div');
        testElement.innerHTML = '<button id="test-click-button">Test Click</button>';
        testElement.style.padding = '10px';
        testElement.style.background = '#e9ecef';
        testElement.style.border = '1px solid #ced4da';
        testElement.style.borderRadius = '4px';
        testElement.style.marginTop = '10px';
        
        document.getElementById('event-result').appendChild(testElement);
        
        // Ajouter un gestionnaire d'événement
        let clickCount = 0;
        document.getElementById('test-click-button').addEventListener('click', function(e) {
            clickCount++;
            debugLog('🖱️ Clic détecté #' + clickCount, 'success');
            this.textContent = 'Cliqué ' + clickCount + ' fois';
            
            // Tester preventDefault et stopPropagation
            e.preventDefault();
            e.stopPropagation();
            debugLog('✅ preventDefault et stopPropagation fonctionnent', 'success');
        });
        
        debugLog('✅ Gestionnaire d\'événement ajouté', 'success');
    });
    
    // Surveiller les erreurs JavaScript
    window.addEventListener('error', function(e) {
        debugLog('❌ Erreur JavaScript: ' + e.message + ' (ligne ' + e.lineno + ')', 'error');
    });
    
    debugLog('🔧 Diagnostic prêt - utilisez les boutons pour tester');
});
</script>

<style>
#debug-console {
    font-family: 'Courier New', monospace;
    white-space: pre-wrap;
}

.button {
    margin-right: 10px;
}

h2 {
    border-bottom: 2px solid #0073aa;
    padding-bottom: 5px;
}

h3 {
    color: #0073aa;
}
</style>
<?php
