<?php
/**
 * API REST pour le module Audit SEO assisté par IA
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 */

/**
 * Classe pour gérer les routes API du module Audit SEO
 */
class Boss_SEO_Audit_API {

    /**
     * Le nom du plugin.
     *
     * @since    1.2.0
     * @access   private
     * @var      string    $plugin_name    Le nom du plugin.
     */
    private $plugin_name;

    /**
     * La version du plugin.
     *
     * @since    1.2.0
     * @access   private
     * @var      string    $version    La version du plugin.
     */
    private $version;

    /**
     * Instance de la classe d'analyse.
     *
     * @since    1.2.0
     * @access   private
     * @var      Boss_Optimizer_Analysis    $analysis    Instance d'analyse SEO.
     */
    private $analysis;

    /**
     * Instance de la classe IA.
     *
     * @since    1.2.0
     * @access   private
     * @var      Boss_Optimizer_AI    $ai    Instance IA.
     */
    private $ai;

    /**
     * Initialise la classe.
     *
     * @since    1.2.0
     * @param    string                    $plugin_name    Le nom du plugin.
     * @param    string                    $version        La version du plugin.
     * @param    Boss_Optimizer_Analysis   $analysis       Instance d'analyse.
     * @param    Boss_Optimizer_AI         $ai             Instance IA.
     */
    public function __construct( $plugin_name, $version, $analysis, $ai ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
        $this->analysis = $analysis;
        $this->ai = $ai;
    }

    /**
     * Enregistre les routes API.
     *
     * @since    1.2.0
     */
    public function register_routes() {
        // Route pour les statistiques globales
        register_rest_route( 'boss-seo/v1', '/audit/global-stats', array(
            'methods' => 'GET',
            'callback' => array( $this, 'get_global_stats' ),
            'permission_callback' => array( $this, 'check_permissions' )
        ) );

        // Route pour récupérer les pages
        register_rest_route( 'boss-seo/v1', '/audit/pages', array(
            'methods' => 'GET',
            'callback' => array( $this, 'get_pages' ),
            'permission_callback' => array( $this, 'check_permissions' )
        ) );

        // Route pour effectuer un audit
        register_rest_route( 'boss-seo/v1', '/audit/perform', array(
            'methods' => 'POST',
            'callback' => array( $this, 'perform_audit' ),
            'permission_callback' => array( $this, 'check_permissions' )
        ) );

        // Route pour l'historique des audits
        register_rest_route( 'boss-seo/v1', '/audit/history', array(
            'methods' => 'GET',
            'callback' => array( $this, 'get_audit_history' ),
            'permission_callback' => array( $this, 'check_permissions' )
        ) );

        // Route pour sauvegarder un audit
        register_rest_route( 'boss-seo/v1', '/audit/save', array(
            'methods' => 'POST',
            'callback' => array( $this, 'save_audit' ),
            'permission_callback' => array( $this, 'check_permissions' )
        ) );

        // Route pour explication IA
        register_rest_route( 'boss-seo/v1', '/ai/explain', array(
            'methods' => 'POST',
            'callback' => array( $this, 'get_ai_explanation' ),
            'permission_callback' => array( $this, 'check_permissions' )
        ) );

        // Route pour suggestion IA
        register_rest_route( 'boss-seo/v1', '/ai/correct', array(
            'methods' => 'POST',
            'callback' => array( $this, 'get_ai_correction' ),
            'permission_callback' => array( $this, 'check_permissions' )
        ) );
    }

    /**
     * Vérifie les permissions.
     *
     * @since    1.2.0
     * @return   bool    True si l'utilisateur a les permissions.
     */
    public function check_permissions() {
        return current_user_can( 'manage_options' );
    }

    /**
     * Récupère les statistiques globales du site.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    La requête REST.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_global_stats( $request ) {
        try {
            // Récupérer tous les types de contenu supportés
            $post_types = array( 'post', 'page' );

            // Ajouter les produits WooCommerce si disponible
            if ( class_exists( 'WooCommerce' ) ) {
                $post_types[] = 'product';
            }

            // Ajouter d'autres types de contenu personnalisés
            $custom_post_types = get_post_types( array( 'public' => true, '_builtin' => false ), 'names' );
            $post_types = array_merge( $post_types, $custom_post_types );

            $stats = array(
                'totalContent' => 0,
                'optimizedContent' => 0,
                'unoptimizedContent' => 0,
                'averageScore' => 0,
                'contentTypes' => array(),
                'commonErrors' => array(),
                'criticalIssues' => 0,
                'lastScanDate' => get_option( 'boss_seo_last_global_scan', null )
            );

            $total_score = 0;
            $scored_content = 0;
            $all_errors = array();

            foreach ( $post_types as $post_type ) {
                $posts = get_posts( array(
                    'post_type' => $post_type,
                    'post_status' => 'publish',
                    'numberposts' => -1,
                    'meta_query' => array(
                        'relation' => 'OR',
                        array(
                            'key' => '_boss_seo_title',
                            'compare' => 'EXISTS'
                        ),
                        array(
                            'key' => '_boss_seo_meta_description',
                            'compare' => 'EXISTS'
                        )
                    )
                ) );

                $type_stats = array(
                    'total' => count( $posts ),
                    'optimized' => 0,
                    'unoptimized' => 0,
                    'average_score' => 0
                );

                foreach ( $posts as $post ) {
                    $stats['totalContent']++;

                    // Vérifier si le contenu est optimisé
                    $is_optimized = get_post_meta( $post->ID, '_boss_seo_optimized', true );
                    $seo_score = get_post_meta( $post->ID, '_boss_seo_score', true );

                    if ( $is_optimized || $seo_score > 70 ) {
                        $stats['optimizedContent']++;
                        $type_stats['optimized']++;
                    } else {
                        $stats['unoptimizedContent']++;
                        $type_stats['unoptimized']++;
                    }

                    // Calculer le score moyen
                    if ( $seo_score ) {
                        $total_score += intval( $seo_score );
                        $scored_content++;
                    }

                    // Collecter les erreurs communes
                    $recommendations = get_post_meta( $post->ID, '_boss_seo_recommendations', true );
                    if ( is_array( $recommendations ) ) {
                        foreach ( $recommendations as $rec ) {
                            if ( isset( $rec['type'] ) ) {
                                $error_key = $rec['type'];
                                if ( ! isset( $all_errors[ $error_key ] ) ) {
                                    $all_errors[ $error_key ] = array(
                                        'type' => $error_key,
                                        'title' => $rec['text'] ?? $error_key,
                                        'count' => 0,
                                        'severity' => $rec['type']
                                    );
                                }
                                $all_errors[ $error_key ]['count']++;

                                if ( $rec['type'] === 'critical' ) {
                                    $stats['criticalIssues']++;
                                }
                            }
                        }
                    }
                }

                if ( $type_stats['total'] > 0 ) {
                    $stats['contentTypes'][ $post_type ] = $type_stats;
                }
            }

            // Calculer le score moyen global
            if ( $scored_content > 0 ) {
                $stats['averageScore'] = round( $total_score / $scored_content );
            }

            // Trier les erreurs par fréquence
            uasort( $all_errors, function( $a, $b ) {
                return $b['count'] - $a['count'];
            } );

            $stats['commonErrors'] = array_slice( array_values( $all_errors ), 0, 10 );

            return new WP_REST_Response( array(
                'success' => true,
                'data' => $stats
            ), 200 );

        } catch ( Exception $e ) {
            return new WP_REST_Response( array(
                'success' => false,
                'message' => 'Erreur lors de la récupération des statistiques: ' . $e->getMessage()
            ), 500 );
        }
    }

    /**
     * Récupère la liste des pages disponibles.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    La requête REST.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_pages( $request ) {
        try {
            $post_types = array( 'post', 'page' );

            // Ajouter les produits WooCommerce si disponible
            if ( class_exists( 'WooCommerce' ) ) {
                $post_types[] = 'product';
            }

            $pages = array();

            foreach ( $post_types as $post_type ) {
                $posts = get_posts( array(
                    'post_type' => $post_type,
                    'post_status' => 'publish',
                    'numberposts' => 50, // Limiter pour les performances
                    'orderby' => 'date',
                    'order' => 'DESC'
                ) );

                foreach ( $posts as $post ) {
                    $pages[] = array(
                        'id' => $post->ID,
                        'title' => $post->post_title,
                        'url' => get_permalink( $post->ID ),
                        'type' => $post_type,
                        'date' => $post->post_date,
                        'seo_score' => get_post_meta( $post->ID, '_boss_seo_score', true ) ?: 0,
                        'is_optimized' => get_post_meta( $post->ID, '_boss_seo_optimized', true ) ? true : false
                    );
                }
            }

            return new WP_REST_Response( array(
                'success' => true,
                'data' => array( 'pages' => $pages )
            ), 200 );

        } catch ( Exception $e ) {
            return new WP_REST_Response( array(
                'success' => false,
                'message' => 'Erreur lors de la récupération des pages: ' . $e->getMessage()
            ), 500 );
        }
    }

    /**
     * Effectue un audit SEO sur un post.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    La requête REST.
     * @return   WP_REST_Response               La réponse.
     */
    public function perform_audit( $request ) {
        try {
            $post_id = $request->get_param( 'post_id' );
            $mode = $request->get_param( 'mode' ) ?: 'expert';
            $use_ai = $request->get_param( 'useAI' ) !== false;

            if ( ! $post_id ) {
                return new WP_REST_Response( array(
                    'success' => false,
                    'message' => 'ID de post requis'
                ), 400 );
            }

            $post = get_post( $post_id );
            if ( ! $post ) {
                return new WP_REST_Response( array(
                    'success' => false,
                    'message' => 'Post non trouvé'
                ), 404 );
            }

            // Utiliser le système d'analyse existant de Boss SEO
            $analysis_result = $this->analysis->analyze( $post );

            if ( ! $analysis_result['success'] ) {
                return new WP_REST_Response( array(
                    'success' => false,
                    'message' => $analysis_result['message']
                ), 500 );
            }

            // Formater les résultats pour l'interface d'audit
            $audit_result = array(
                'post_id' => $post_id,
                'url' => get_permalink( $post_id ),
                'title' => $post->post_title,
                'date' => current_time( 'mysql' ),
                'globalScore' => $analysis_result['score'] ?? 0,
                'errors' => $this->format_errors_for_audit( $analysis_result ),
                'performance' => array(
                    'seo_score' => $analysis_result['score'] ?? 0,
                    'content_length' => str_word_count( strip_tags( $post->post_content ) ),
                    'images_count' => substr_count( $post->post_content, '<img' ),
                    'links_count' => substr_count( $post->post_content, '<a' )
                ),
                'metadata' => array(
                    'title' => get_post_meta( $post_id, '_boss_seo_title', true ) ?: $post->post_title,
                    'description' => get_post_meta( $post_id, '_boss_seo_meta_description', true ),
                    'keywords' => get_post_meta( $post_id, '_boss_seo_focus_keyword', true ),
                    'og_title' => get_post_meta( $post_id, '_boss_seo_og_title', true ),
                    'og_description' => get_post_meta( $post_id, '_boss_seo_og_description', true ),
                    'twitter_title' => get_post_meta( $post_id, '_boss_seo_twitter_title', true ),
                    'twitter_description' => get_post_meta( $post_id, '_boss_seo_twitter_description', true )
                )
            );

            // Sauvegarder les résultats d'audit
            update_post_meta( $post_id, '_boss_seo_audit_result', $audit_result );
            update_post_meta( $post_id, '_boss_seo_audit_date', current_time( 'mysql' ) );

            return new WP_REST_Response( array(
                'success' => true,
                'data' => $audit_result
            ), 200 );

        } catch ( Exception $e ) {
            return new WP_REST_Response( array(
                'success' => false,
                'message' => 'Erreur lors de l\'audit: ' . $e->getMessage()
            ), 500 );
        }
    }

    /**
     * Formate les erreurs pour l'interface d'audit.
     *
     * @since    1.2.0
     * @param    array    $analysis_result    Résultat de l'analyse.
     * @return   array                        Erreurs formatées.
     */
    private function format_errors_for_audit( $analysis_result ) {
        $errors = array(
            'critical' => array(),
            'medium' => array(),
            'low' => array()
        );

        if ( isset( $analysis_result['recommendations'] ) && is_array( $analysis_result['recommendations'] ) ) {
            foreach ( $analysis_result['recommendations'] as $recommendation ) {
                $severity = $this->map_recommendation_severity( $recommendation['type'] ?? 'info' );

                $error = array(
                    'id' => sanitize_title( $recommendation['text'] ?? 'unknown' ),
                    'title' => $recommendation['text'] ?? 'Problème détecté',
                    'description' => $recommendation['description'] ?? $recommendation['text'] ?? '',
                    'category' => $this->map_recommendation_category( $recommendation ),
                    'severity' => $severity,
                    'location' => $recommendation['location'] ?? 'page',
                    'elements' => $recommendation['elements'] ?? array(),
                    'currentValue' => $recommendation['current_value'] ?? null,
                    'recommendedValue' => $recommendation['recommended_value'] ?? null
                );

                $errors[ $severity ][] = $error;
            }
        }

        return $errors;
    }

    /**
     * Mappe la sévérité des recommandations.
     *
     * @since    1.2.0
     * @param    string    $type    Type de recommandation.
     * @return   string             Sévérité mappée.
     */
    private function map_recommendation_severity( $type ) {
        $severity_map = array(
            'critical' => 'critical',
            'error' => 'critical',
            'warning' => 'medium',
            'info' => 'low',
            'success' => 'low'
        );

        return $severity_map[ $type ] ?? 'low';
    }

    /**
     * Mappe la catégorie des recommandations.
     *
     * @since    1.2.0
     * @param    array    $recommendation    Recommandation.
     * @return   string                      Catégorie mappée.
     */
    private function map_recommendation_category( $recommendation ) {
        $text = strtolower( $recommendation['text'] ?? '' );

        if ( strpos( $text, 'title' ) !== false || strpos( $text, 'titre' ) !== false ) {
            return 'meta';
        } elseif ( strpos( $text, 'description' ) !== false ) {
            return 'meta';
        } elseif ( strpos( $text, 'keyword' ) !== false || strpos( $text, 'mot-clé' ) !== false ) {
            return 'keywords';
        } elseif ( strpos( $text, 'image' ) !== false || strpos( $text, 'alt' ) !== false ) {
            return 'images';
        } elseif ( strpos( $text, 'link' ) !== false || strpos( $text, 'lien' ) !== false ) {
            return 'links';
        } elseif ( strpos( $text, 'h1' ) !== false || strpos( $text, 'heading' ) !== false ) {
            return 'structure';
        } else {
            return 'technical';
        }
    }

    /**
     * Récupère l'historique des audits.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    La requête REST.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_audit_history( $request ) {
        try {
            $history = get_option( 'boss_seo_audit_history', array() );

            return new WP_REST_Response( array(
                'success' => true,
                'data' => array( 'history' => $history )
            ), 200 );

        } catch ( Exception $e ) {
            return new WP_REST_Response( array(
                'success' => false,
                'message' => 'Erreur lors de la récupération de l\'historique: ' . $e->getMessage()
            ), 500 );
        }
    }

    /**
     * Sauvegarde un audit.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    La requête REST.
     * @return   WP_REST_Response               La réponse.
     */
    public function save_audit( $request ) {
        try {
            $audit_data = $request->get_json_params();

            if ( ! $audit_data ) {
                return new WP_REST_Response( array(
                    'success' => false,
                    'message' => 'Données d\'audit requises'
                ), 400 );
            }

            // Ajouter à l'historique
            $history = get_option( 'boss_seo_audit_history', array() );
            array_unshift( $history, $audit_data );

            // Garder seulement les 50 derniers audits
            $history = array_slice( $history, 0, 50 );

            update_option( 'boss_seo_audit_history', $history );

            return new WP_REST_Response( array(
                'success' => true,
                'data' => array( 'saved' => true )
            ), 200 );

        } catch ( Exception $e ) {
            return new WP_REST_Response( array(
                'success' => false,
                'message' => 'Erreur lors de la sauvegarde: ' . $e->getMessage()
            ), 500 );
        }
    }

    /**
     * Génère une explication IA pour une erreur.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    La requête REST.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_ai_explanation( $request ) {
        try {
            $error = $request->get_param( 'error' );
            $language = $request->get_param( 'language' ) ?: 'fr';

            if ( ! $error ) {
                return new WP_REST_Response( array(
                    'success' => false,
                    'message' => 'Erreur requise'
                ), 400 );
            }

            // Utiliser l'IA configurée pour générer l'explication
            $prompt = "Explique cette erreur SEO en français de manière simple et claire : " . $error['title'] . ". " . $error['description'];

            $ai_result = $this->ai->generate_content( $prompt, array(
                'max_tokens' => 200,
                'temperature' => 0.7
            ) );

            if ( $ai_result['success'] ) {
                $explanation = array(
                    'description' => $ai_result['content'],
                    'impact' => $this->get_impact_description( $error['severity'] ),
                    'recommendation' => 'Corrigez cette erreur pour améliorer votre référencement.'
                );

                return new WP_REST_Response( array(
                    'success' => true,
                    'data' => array( 'explanation' => $explanation )
                ), 200 );
            } else {
                throw new Exception( $ai_result['message'] ?? 'Erreur IA' );
            }

        } catch ( Exception $e ) {
            return new WP_REST_Response( array(
                'success' => false,
                'message' => 'Erreur lors de la génération de l\'explication: ' . $e->getMessage()
            ), 500 );
        }
    }

    /**
     * Génère une suggestion de correction IA.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    La requête REST.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_ai_correction( $request ) {
        try {
            $error = $request->get_param( 'error' );
            $language = $request->get_param( 'language' ) ?: 'fr';

            if ( ! $error ) {
                return new WP_REST_Response( array(
                    'success' => false,
                    'message' => 'Erreur requise'
                ), 400 );
            }

            // Utiliser l'IA pour générer une suggestion de correction
            $prompt = "Donne une suggestion de correction concrète et actionnable pour cette erreur SEO : " . $error['title'] . ". " . $error['description'] . ". Fournis du code HTML si nécessaire.";

            $ai_result = $this->ai->generate_content( $prompt, array(
                'max_tokens' => 300,
                'temperature' => 0.5
            ) );

            if ( $ai_result['success'] ) {
                $suggestion = array(
                    'type' => 'text',
                    'title' => 'Suggestion de correction IA',
                    'description' => $ai_result['content'],
                    'canApply' => false,
                    'steps' => array(
                        'Analysez la suggestion ci-dessus',
                        'Appliquez les modifications recommandées',
                        'Vérifiez le résultat'
                    )
                );

                return new WP_REST_Response( array(
                    'success' => true,
                    'data' => array( 'suggestion' => $suggestion )
                ), 200 );
            } else {
                throw new Exception( $ai_result['message'] ?? 'Erreur IA' );
            }

        } catch ( Exception $e ) {
            return new WP_REST_Response( array(
                'success' => false,
                'message' => 'Erreur lors de la génération de la suggestion: ' . $e->getMessage()
            ), 500 );
        }
    }

    /**
     * Récupère la description de l'impact selon la sévérité.
     *
     * @since    1.2.0
     * @param    string    $severity    Sévérité de l'erreur.
     * @return   string                 Description de l'impact.
     */
    private function get_impact_description( $severity ) {
        switch ( $severity ) {
            case 'critical':
                return 'Impact élevé sur le SEO - Correction prioritaire recommandée';
            case 'medium':
                return 'Impact modéré sur le SEO - Correction recommandée';
            case 'low':
                return 'Impact faible sur le SEO - Amélioration suggérée';
            default:
                return 'Impact sur le SEO à évaluer';
        }
    }
}
