/**
 * Service API pour le module Audit SEO assisté par IA
 */
import apiFetch from '@wordpress/api-fetch';
import { addQueryArgs } from '@wordpress/url';

class AuditIAService {
  constructor() {
    // Utiliser wp.apiFetch qui gère automatiquement le nonce et les permissions
  }

  /**
   * Vérifier la configuration IA
   */
  async checkAIConfiguration() {
    try {
      const data = await apiFetch({
        path: '/boss-seo/v1/ai/check'
      });

      return {
        available: data.success && data.data?.ai_available,
        providers: data.data?.providers || [],
        message: data.message
      };
    } catch (error) {
      console.error('Erreur lors de la vérification de la configuration IA:', error);
      return {
        available: false,
        providers: [],
        message: 'Configuration IA non disponible'
      };
    }
  }

  /**
   * Récupérer les pages disponibles pour l'audit
   */
  async getAvailablePages() {
    try {
      const data = await apiFetch({
        path: '/boss-seo/v1/audit/pages'
      });

      if (data.success) {
        return data.data.pages || [];
      } else {
        throw new Error(data.message || 'Erreur lors de la récupération des pages');
      }
    } catch (error) {
      console.error('Erreur lors de la récupération des pages:', error);
      // Retourner des données de test en cas d'erreur
      return [
        {
          id: 1,
          title: 'Page d\'accueil',
          url: window.location.origin,
          type: 'page'
        },
        {
          id: 2,
          title: 'À propos',
          url: window.location.origin + '/about',
          type: 'page'
        }
      ];
    }
  }

  /**
   * Effectuer un audit SEO
   */
  async performAudit(url, options = {}) {
    try {
      const data = await apiFetch({
        path: '/boss-seo/v1/audit/perform',
        method: 'POST',
        data: {
          url: url,
          mode: options.mode || 'novice',
          useAI: options.useAI !== false,
          checks: options.checks || [
            'meta_tags',
            'content_quality',
            'technical_seo',
            'performance',
            'accessibility',
            'images',
            'links',
            'structure'
          ]
        }
      });

      if (data.success) {
        return this.processAuditResult(data.data);
      } else {
        throw new Error(data.message || 'Erreur lors de l\'audit');
      }
    } catch (error) {
      console.error('Erreur lors de l\'audit:', error);
      // Retourner des données de test en cas d'erreur
      return this.getMockAuditResult(url);
    }
  }

  /**
   * Traiter le résultat d'audit
   */
  processAuditResult(rawData) {
    const errors = {
      critical: [],
      medium: [],
      low: []
    };

    // Traiter les erreurs par catégorie
    if (rawData.errors) {
      Object.entries(rawData.errors).forEach(([category, categoryErrors]) => {
        categoryErrors.forEach(error => {
          const processedError = {
            id: error.id || Math.random().toString(36).substr(2, 9),
            title: error.title,
            description: error.description,
            category: category,
            severity: error.severity || 'medium',
            location: error.location,
            elements: error.elements || [],
            currentValue: error.currentValue,
            recommendedValue: error.recommendedValue
          };

          if (error.severity === 'critical') {
            errors.critical.push(processedError);
          } else if (error.severity === 'low') {
            errors.low.push(processedError);
          } else {
            errors.medium.push(processedError);
          }
        });
      });
    }

    // Calculer le score global
    const totalErrors = errors.critical.length + errors.medium.length + errors.low.length;
    const criticalWeight = errors.critical.length * 10;
    const mediumWeight = errors.medium.length * 5;
    const lowWeight = errors.low.length * 2;
    const totalWeight = criticalWeight + mediumWeight + lowWeight;

    let globalScore = 100;
    if (totalWeight > 0) {
      globalScore = Math.max(0, 100 - totalWeight);
    }

    return {
      url: rawData.url,
      date: new Date().toISOString(),
      globalScore: Math.round(globalScore),
      errors: errors,
      performance: rawData.performance || {},
      metadata: rawData.metadata || {}
    };
  }

  /**
   * Données de test pour l'audit
   */
  getMockAuditResult(url) {
    return {
      url: url,
      date: new Date().toISOString(),
      globalScore: 75,
      errors: {
        critical: [
          {
            id: 'meta-desc-missing',
            title: 'Méta description manquante',
            description: 'Cette page n\'a pas de méta description, ce qui peut affecter son apparence dans les résultats de recherche.',
            category: 'meta',
            severity: 'critical',
            location: '<head>',
            elements: ['meta[name="description"]'],
            currentValue: null,
            recommendedValue: 'Une description de 150-160 caractères décrivant le contenu de la page'
          }
        ],
        medium: [
          {
            id: 'h1-multiple',
            title: 'Plusieurs balises H1 détectées',
            description: 'Cette page contient plusieurs balises H1, ce qui peut créer de la confusion pour les moteurs de recherche.',
            category: 'structure',
            severity: 'medium',
            location: 'body',
            elements: ['h1:nth-child(1)', 'h1:nth-child(3)'],
            currentValue: '2 balises H1',
            recommendedValue: '1 seule balise H1 par page'
          },
          {
            id: 'images-alt-missing',
            title: 'Images sans attribut alt',
            description: 'Certaines images n\'ont pas d\'attribut alt, ce qui nuit à l\'accessibilité et au SEO.',
            category: 'images',
            severity: 'medium',
            location: 'body',
            elements: ['img[src="/image1.jpg"]', 'img[src="/image2.png"]'],
            currentValue: '2 images sans alt',
            recommendedValue: 'Toutes les images doivent avoir un attribut alt descriptif'
          }
        ],
        low: [
          {
            id: 'title-length',
            title: 'Titre trop court',
            description: 'Le titre de la page est plus court que la longueur recommandée.',
            category: 'meta',
            severity: 'low',
            location: '<head>',
            elements: ['title'],
            currentValue: '35 caractères',
            recommendedValue: '50-60 caractères'
          }
        ]
      },
      performance: {
        loadTime: 2.3,
        firstContentfulPaint: 1.2,
        largestContentfulPaint: 2.8
      },
      metadata: {
        title: 'Page d\'exemple',
        description: null,
        keywords: []
      }
    };
  }

  /**
   * Récupérer l'historique des audits
   */
  async getAuditHistory() {
    try {
      const data = await apiFetch({
        path: '/boss-seo/v1/audit/history'
      });

      if (data.success) {
        return data.data.history || [];
      } else {
        throw new Error(data.message || 'Erreur lors de la récupération de l\'historique');
      }
    } catch (error) {
      console.error('Erreur lors de la récupération de l\'historique:', error);
      // Retourner un historique vide en cas d'erreur
      return [];
    }
  }

  /**
   * Sauvegarder un audit
   */
  async saveAudit(auditData) {
    try {
      const data = await apiFetch({
        path: '/boss-seo/v1/audit/save',
        method: 'POST',
        data: auditData
      });

      if (data.success) {
        return data.data;
      } else {
        throw new Error(data.message || 'Erreur lors de la sauvegarde');
      }
    } catch (error) {
      console.error('Erreur lors de la sauvegarde de l\'audit:', error);
      throw error;
    }
  }

  /**
   * Obtenir une explication IA pour une erreur
   */
  async getAIExplanation(error, language = 'fr') {
    try {
      const data = await apiFetch({
        path: '/boss-seo/v1/ai/explain',
        method: 'POST',
        data: {
          error: error,
          language: language,
          mode: 'explanation'
        }
      });

      if (data.success) {
        return data.data.explanation;
      } else {
        throw new Error(data.message || 'Erreur lors de la génération de l\'explication');
      }
    } catch (error) {
      console.error('Erreur lors de la génération de l\'explication IA:', error);
      throw error;
    }
  }

  /**
   * Obtenir une suggestion de correction IA
   */
  async getAICorrection(error, language = 'fr') {
    try {
      const data = await apiFetch({
        path: '/boss-seo/v1/ai/correct',
        method: 'POST',
        data: {
          error: error,
          language: language,
          mode: 'correction'
        }
      });

      if (data.success) {
        return data.data.suggestion;
      } else {
        throw new Error(data.message || 'Erreur lors de la génération de la suggestion');
      }
    } catch (error) {
      console.error('Erreur lors de la génération de la suggestion IA:', error);
      throw error;
    }
  }
}

export default AuditIAService;
