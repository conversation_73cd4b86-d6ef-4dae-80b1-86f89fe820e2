/**
 * Service API pour le module Audit SEO assisté par IA
 */
import apiFetch from '@wordpress/api-fetch';
import { addQueryArgs } from '@wordpress/url';

class AuditIAService {
  constructor() {
    // Utiliser wp.apiFetch qui gère automatiquement le nonce et les permissions
  }

  /**
   * Vérifier la configuration IA
   */
  async checkAIConfiguration() {
    try {
      const data = await apiFetch({
        path: '/boss-seo/v1/ai/check'
      });

      return {
        available: data.success && data.data?.ai_available,
        providers: data.data?.providers || [],
        message: data.message
      };
    } catch (error) {
      console.error('Erreur lors de la vérification de la configuration IA:', error);
      return {
        available: false,
        providers: [],
        message: 'Configuration IA non disponible'
      };
    }
  }

  /**
   * Récupérer les statistiques globales du site
   */
  async getGlobalStats() {
    try {
      const data = await apiFetch({
        path: '/boss-seo/v1/audit/global-stats'
      });

      if (data.success) {
        return data.data;
      } else {
        throw new Error(data.message || 'Erreur lors de la récupération des statistiques');
      }
    } catch (error) {
      console.error('Erreur lors de la récupération des statistiques:', error);
      throw error;
    }
  }

  /**
   * Récupérer les pages disponibles pour l'audit
   */
  async getAvailablePages() {
    try {
      const data = await apiFetch({
        path: '/boss-seo/v1/audit/pages'
      });

      if (data.success) {
        return data.data.pages || [];
      } else {
        throw new Error(data.message || 'Erreur lors de la récupération des pages');
      }
    } catch (error) {
      console.error('Erreur lors de la récupération des pages:', error);
      throw error;
    }
  }

  /**
   * Effectuer un audit SEO
   */
  async performAudit(postId, options = {}) {
    try {
      const data = await apiFetch({
        path: '/boss-seo/v1/audit/perform',
        method: 'POST',
        data: {
          post_id: postId,
          mode: options.mode || 'expert',
          useAI: options.useAI !== false,
          checks: options.checks || [
            'meta_tags',
            'content_quality',
            'technical_seo',
            'performance',
            'accessibility',
            'images',
            'links',
            'structure',
            'keywords',
            'social_media'
          ]
        }
      });

      if (data.success) {
        return data.data;
      } else {
        throw new Error(data.message || 'Erreur lors de l\'audit');
      }
    } catch (error) {
      console.error('Erreur lors de l\'audit:', error);
      throw error;
    }
  }

  /**
   * Traiter le résultat d'audit
   */
  processAuditResult(rawData) {
    const errors = {
      critical: [],
      medium: [],
      low: []
    };

    // Traiter les erreurs par catégorie
    if (rawData.errors) {
      Object.entries(rawData.errors).forEach(([category, categoryErrors]) => {
        categoryErrors.forEach(error => {
          const processedError = {
            id: error.id || Math.random().toString(36).substr(2, 9),
            title: error.title,
            description: error.description,
            category: category,
            severity: error.severity || 'medium',
            location: error.location,
            elements: error.elements || [],
            currentValue: error.currentValue,
            recommendedValue: error.recommendedValue
          };

          if (error.severity === 'critical') {
            errors.critical.push(processedError);
          } else if (error.severity === 'low') {
            errors.low.push(processedError);
          } else {
            errors.medium.push(processedError);
          }
        });
      });
    }

    // Calculer le score global
    const totalErrors = errors.critical.length + errors.medium.length + errors.low.length;
    const criticalWeight = errors.critical.length * 10;
    const mediumWeight = errors.medium.length * 5;
    const lowWeight = errors.low.length * 2;
    const totalWeight = criticalWeight + mediumWeight + lowWeight;

    let globalScore = 100;
    if (totalWeight > 0) {
      globalScore = Math.max(0, 100 - totalWeight);
    }

    return {
      url: rawData.url,
      date: new Date().toISOString(),
      globalScore: Math.round(globalScore),
      errors: errors,
      performance: rawData.performance || {},
      metadata: rawData.metadata || {}
    };
  }



  /**
   * Récupérer l'historique des audits
   */
  async getAuditHistory() {
    try {
      const data = await apiFetch({
        path: '/boss-seo/v1/audit/history'
      });

      if (data.success) {
        return data.data.history || [];
      } else {
        throw new Error(data.message || 'Erreur lors de la récupération de l\'historique');
      }
    } catch (error) {
      console.error('Erreur lors de la récupération de l\'historique:', error);
      // Retourner un historique vide en cas d'erreur
      return [];
    }
  }

  /**
   * Sauvegarder un audit
   */
  async saveAudit(auditData) {
    try {
      const data = await apiFetch({
        path: '/boss-seo/v1/audit/save',
        method: 'POST',
        data: auditData
      });

      if (data.success) {
        return data.data;
      } else {
        throw new Error(data.message || 'Erreur lors de la sauvegarde');
      }
    } catch (error) {
      console.error('Erreur lors de la sauvegarde de l\'audit:', error);
      throw error;
    }
  }

  /**
   * Obtenir une explication IA pour une erreur
   */
  async getAIExplanation(error, language = 'fr') {
    try {
      const data = await apiFetch({
        path: '/boss-seo/v1/ai/explain',
        method: 'POST',
        data: {
          error: error,
          language: language,
          mode: 'explanation'
        }
      });

      if (data.success) {
        return data.data.explanation;
      } else {
        throw new Error(data.message || 'Erreur lors de la génération de l\'explication');
      }
    } catch (error) {
      console.error('Erreur lors de la génération de l\'explication IA:', error);
      throw error;
    }
  }

  /**
   * Obtenir une suggestion de correction IA
   */
  async getAICorrection(error, language = 'fr') {
    try {
      const data = await apiFetch({
        path: '/boss-seo/v1/ai/correct',
        method: 'POST',
        data: {
          error: error,
          language: language,
          mode: 'correction'
        }
      });

      if (data.success) {
        return data.data.suggestion;
      } else {
        throw new Error(data.message || 'Erreur lors de la génération de la suggestion');
      }
    } catch (error) {
      console.error('Erreur lors de la génération de la suggestion IA:', error);
      throw error;
    }
  }
}

export default AuditIAService;
